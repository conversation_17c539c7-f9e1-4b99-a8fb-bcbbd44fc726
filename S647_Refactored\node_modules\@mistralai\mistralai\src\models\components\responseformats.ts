/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

/**
 * An object specifying the format that the model must output. Setting to `{ "type": "json_object" }` enables JSON mode, which guarantees the message the model generates is in JSON. When using JSON mode you MUST also instruct the model to produce JSON yourself with a system or a user message.
 */
export const ResponseFormats = {
  Text: "text",
  JsonObject: "json_object",
  JsonSchema: "json_schema",
} as const;
/**
 * An object specifying the format that the model must output. Setting to `{ "type": "json_object" }` enables JSON mode, which guarantees the message the model generates is in JSON. When using JSON mode you MUST also instruct the model to produce JSON yourself with a system or a user message.
 */
export type ResponseFormats = ClosedEnum<typeof ResponseFormats>;

/** @internal */
export const ResponseFormats$inboundSchema: z.Zod<PERSON>num<
  typeof ResponseFormats
> = z.nativeEnum(ResponseFormats);

/** @internal */
export const ResponseFormats$outboundSchema: z.ZodNativeEnum<
  typeof ResponseFormats
> = ResponseFormats$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ResponseFormats$ {
  /** @deprecated use `ResponseFormats$inboundSchema` instead. */
  export const inboundSchema = ResponseFormats$inboundSchema;
  /** @deprecated use `ResponseFormats$outboundSchema` instead. */
  export const outboundSchema = ResponseFormats$outboundSchema;
}
