/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

/**
 * Server side events sent when streaming a conversation response.
 */
export const SSETypes = {
  ConversationResponseStarted: "conversation.response.started",
  ConversationResponseDone: "conversation.response.done",
  ConversationResponseError: "conversation.response.error",
  MessageOutputDelta: "message.output.delta",
  ToolExecutionStarted: "tool.execution.started",
  ToolExecutionDelta: "tool.execution.delta",
  ToolExecutionDone: "tool.execution.done",
  AgentHandoffStarted: "agent.handoff.started",
  AgentHandoffDone: "agent.handoff.done",
  FunctionCallDelta: "function.call.delta",
} as const;
/**
 * Server side events sent when streaming a conversation response.
 */
export type SSETypes = ClosedEnum<typeof SSETypes>;

/** @internal */
export const SSETypes$inboundSchema: z.ZodNativeEnum<typeof SSETypes> = z
  .nativeEnum(SSETypes);

/** @internal */
export const SSETypes$outboundSchema: z.ZodNativeEnum<typeof SSETypes> =
  SSETypes$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SSETypes$ {
  /** @deprecated use `SSETypes$inboundSchema` instead. */
  export const inboundSchema = SSETypes$inboundSchema;
  /** @deprecated use `SSETypes$outboundSchema` instead. */
  export const outboundSchema = SSETypes$outboundSchema;
}
