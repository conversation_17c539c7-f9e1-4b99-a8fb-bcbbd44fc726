/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibraryOut = {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  ownerId: string;
  ownerType: string;
  totalSize: number;
  nbDocuments: number;
  chunkSize: number | null;
  emoji?: string | null | undefined;
  description?: string | null | undefined;
  generatedName?: string | null | undefined;
  generatedDescription?: string | null | undefined;
  explicitUserMembersCount?: number | null | undefined;
  explicitWorkspaceMembersCount?: number | null | undefined;
  orgSharingRole?: string | null | undefined;
};

/** @internal */
export const LibraryOut$inboundSchema: z.ZodType<
  LibraryOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  name: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  updated_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  owner_id: z.string(),
  owner_type: z.string(),
  total_size: z.number().int(),
  nb_documents: z.number().int(),
  chunk_size: z.nullable(z.number().int()),
  emoji: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
  generated_name: z.nullable(z.string()).optional(),
  generated_description: z.nullable(z.string()).optional(),
  explicit_user_members_count: z.nullable(z.number().int()).optional(),
  explicit_workspace_members_count: z.nullable(z.number().int()).optional(),
  org_sharing_role: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "updated_at": "updatedAt",
    "owner_id": "ownerId",
    "owner_type": "ownerType",
    "total_size": "totalSize",
    "nb_documents": "nbDocuments",
    "chunk_size": "chunkSize",
    "generated_name": "generatedName",
    "generated_description": "generatedDescription",
    "explicit_user_members_count": "explicitUserMembersCount",
    "explicit_workspace_members_count": "explicitWorkspaceMembersCount",
    "org_sharing_role": "orgSharingRole",
  });
});

/** @internal */
export type LibraryOut$Outbound = {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  owner_id: string;
  owner_type: string;
  total_size: number;
  nb_documents: number;
  chunk_size: number | null;
  emoji?: string | null | undefined;
  description?: string | null | undefined;
  generated_name?: string | null | undefined;
  generated_description?: string | null | undefined;
  explicit_user_members_count?: number | null | undefined;
  explicit_workspace_members_count?: number | null | undefined;
  org_sharing_role?: string | null | undefined;
};

/** @internal */
export const LibraryOut$outboundSchema: z.ZodType<
  LibraryOut$Outbound,
  z.ZodTypeDef,
  LibraryOut
> = z.object({
  id: z.string(),
  name: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  updatedAt: z.date().transform(v => v.toISOString()),
  ownerId: z.string(),
  ownerType: z.string(),
  totalSize: z.number().int(),
  nbDocuments: z.number().int(),
  chunkSize: z.nullable(z.number().int()),
  emoji: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
  generatedName: z.nullable(z.string()).optional(),
  generatedDescription: z.nullable(z.string()).optional(),
  explicitUserMembersCount: z.nullable(z.number().int()).optional(),
  explicitWorkspaceMembersCount: z.nullable(z.number().int()).optional(),
  orgSharingRole: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    updatedAt: "updated_at",
    ownerId: "owner_id",
    ownerType: "owner_type",
    totalSize: "total_size",
    nbDocuments: "nb_documents",
    chunkSize: "chunk_size",
    generatedName: "generated_name",
    generatedDescription: "generated_description",
    explicitUserMembersCount: "explicit_user_members_count",
    explicitWorkspaceMembersCount: "explicit_workspace_members_count",
    orgSharingRole: "org_sharing_role",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibraryOut$ {
  /** @deprecated use `LibraryOut$inboundSchema` instead. */
  export const inboundSchema = LibraryOut$inboundSchema;
  /** @deprecated use `LibraryOut$outboundSchema` instead. */
  export const outboundSchema = LibraryOut$outboundSchema;
  /** @deprecated use `LibraryOut$Outbound` instead. */
  export type Outbound = LibraryOut$Outbound;
}

export function libraryOutToJSON(libraryOut: LibraryOut): string {
  return JSON.stringify(LibraryOut$outboundSchema.parse(libraryOut));
}

export function libraryOutFromJSON(
  jsonString: string,
): SafeParseResult<LibraryOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibraryOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibraryOut' from JSON`,
  );
}
