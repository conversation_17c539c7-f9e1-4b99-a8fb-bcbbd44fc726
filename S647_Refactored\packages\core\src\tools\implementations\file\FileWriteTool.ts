/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { promises as fs } from 'fs';
import { dirname, resolve, isAbsolute } from 'path';
import { z } from 'zod';
import type {
  ToolParams,
  ToolContext,
  ToolResult,
  ToolHelp,
  AsyncResult,
} from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';

const FileWriteParams = z.object({
  path: z.string().describe('Path to the file to write'),
  content: z.string().describe('Content to write to the file'),
  encoding: z.string().optional().default('utf8').describe('File encoding (default: utf8)'),
  createDirectories: z.boolean().optional().default(true).describe('Create parent directories if they don\'t exist'),
  overwrite: z.boolean().optional().default(false).describe('Overwrite file if it exists'),
});

type FileWriteParams = z.infer<typeof FileWriteParams>;

/**
 * Tool for writing content to files
 */
export class FileWriteTool extends BaseTool {
  constructor() {
    super(
      'file-write',
      'File Write',
      'Write content to a file',
      '1.0.0',
      'file',
      FileWriteParams
    );
  }

  public async execute(
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    try {
      const { path, content, encoding, createDirectories, overwrite } = 
        await this.validateAndParse<FileWriteParams>(params);

      // Resolve path relative to working directory
      const resolvedPath = this.resolvePath(path, context.workingDirectory);

      // Check if file exists and overwrite is not allowed
      try {
        await fs.access(resolvedPath);
        if (!overwrite) {
          return this.error(`File already exists and overwrite is disabled: ${resolvedPath}`);
        }
      } catch {
        // File doesn't exist, which is fine
      }

      // Create parent directories if needed
      if (createDirectories) {
        const dir = dirname(resolvedPath);
        await fs.mkdir(dir, { recursive: true });
      }

      // Write file content
      await fs.writeFile(resolvedPath, content, encoding as BufferEncoding);

      // Get file stats
      const stats = await fs.stat(resolvedPath);

      return this.success({
        type: 'text',
        content: `Successfully wrote ${content.length} characters to ${resolvedPath}`,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
          contentLength: content.length,
          created: stats.birthtime.toISOString(),
          lastModified: stats.mtime.toISOString(),
        },
      });
    } catch (error) {
      return this.error(this.handleError(error));
    }
  }

  public getHelp(): ToolHelp {
    return {
      description: 'Write content to a file on the filesystem',
      parameters: {
        path: {
          type: 'string',
          description: 'Path to the file to write (relative or absolute)',
          required: true,
        },
        content: {
          type: 'string',
          description: 'Content to write to the file',
          required: true,
        },
        encoding: {
          type: 'string',
          description: 'File encoding (utf8, ascii, base64, etc.)',
          required: false,
          default: 'utf8',
        },
        createDirectories: {
          type: 'boolean',
          description: 'Create parent directories if they don\'t exist',
          required: false,
          default: true,
        },
        overwrite: {
          type: 'boolean',
          description: 'Overwrite file if it exists',
          required: false,
          default: false,
        },
      },
      examples: [
        {
          description: 'Write a simple text file',
          parameters: { 
            path: './output.txt', 
            content: 'Hello, World!',
            overwrite: true 
          },
        },
        {
          description: 'Create a JSON file with directories',
          parameters: { 
            path: './data/config.json', 
            content: '{"key": "value"}',
            createDirectories: true,
            overwrite: true
          },
        },
      ],
    };
  }

  private resolvePath(path: string, workingDirectory?: string): string {
    if (isAbsolute(path)) {
      return resolve(path);
    }
    return resolve(workingDirectory || process.cwd(), path);
  }
}
