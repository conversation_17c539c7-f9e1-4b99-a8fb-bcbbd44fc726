{"version": 3, "file": "default-loader.js", "sourceRoot": "", "sources": ["../../../src/config/loaders/default-loader.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAGxD;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IACd,IAAI,GAAG,SAAS,CAAC;IACjB,QAAQ,GAAG,CAAC,CAAC,CAAC,kBAAkB;IAEhD;;OAEG;IACI,KAAK,CAAC,IAAI,CAAC,QAA8B;QAC9C,IAAI,CAAC;YACH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,cAAqB;gBAC7B,MAAM,EAAE;oBACN,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,QAAQ,EAAE;wBACR,MAAM,EAAE,IAAI,CAAC,IAAI;wBACjB,OAAO,EAAE,cAAc,CAAC,OAAO;qBAChC;iBACF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,sCAAsC,CAAC;aAC1F,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,QAA8B;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;CACF"}