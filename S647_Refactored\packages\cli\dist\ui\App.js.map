{"version": 3, "file": "App.js", "sourceRoot": "", "sources": ["../../src/ui/App.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC;AAIxC,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAoBhE;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAY;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;IAC1B,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAW;QAC3C,SAAS,EAAE,IAAI;QACf,aAAa,EAAE,KAAK;KACrB,CAAC,CAAC;IAEH,yBAAyB;IACzB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;gBAEnE,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBAE9C,yBAAyB;gBACzB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACpE,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;gBAC9E,CAAC;gBAED,yCAAyC;gBACzC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;gBACjE,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBACjD,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;yBACxD,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;yBAC1C,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;oBAEzB,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACpC,MAAM,IAAI,KAAK,CAAC,wFAAwF,CAAC,CAAC;oBAC5G,CAAC;oBAED,MAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,8BAA8B,kBAAkB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;gBAC1H,CAAC;gBAED,4BAA4B;gBAC5B,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;qBACtD,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;qBAC1C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAI,eAAe,GAAG,CAAC;qBAC3E,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEd,MAAM,CAAC,IAAI,CAAC,gBAAgB,gBAAgB,EAAE,CAAC,CAAC;gBAChD,MAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC;gBACvE,MAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;gBAE5C,8CAA8C;gBAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAExD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS,EAAE,KAAK;oBAChB,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC,CAAC;gBAEJ,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAE7D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBAC/D,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChB,GAAG,IAAI;oBACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChE,SAAS,EAAE,KAAK;oBAChB,aAAa,EAAE,KAAK;iBACrB,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC,CAAC;QAEF,aAAa,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAErB,0BAA0B;IAC1B,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;IAEF,qBAAqB;IACrB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CACL,KAAC,aAAa,cACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,aACnF,KAAC,cAAc,KAAG,EAClB,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,mEAEhB,GACH,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,6DAEpB,GACH,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,qDAEpB,GACH,IACF,GACQ,CACjB,CAAC;IACJ,CAAC;IAED,mBAAmB;IACnB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CACL,KAAC,aAAa,cACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,aACnF,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,EAAC,IAAI,kDAEf,EACP,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,YAC3B,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,YACd,KAAK,CAAC,KAAK,CAAC,OAAO,GACf,GACH,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,mDAEb,GACH,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,YAC3B,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,+EAEX,GACH,EACN,KAAC,GAAG,IAAC,OAAO,EAAE,CAAC,YACb,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,2EAEX,GACH,EACN,KAAC,GAAG,IAAC,OAAO,EAAE,CAAC,YACb,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,8EAEX,GACH,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,2CAEpB,GACH,IACF,GACQ,CACjB,CAAC;IACJ,CAAC;IAED,6BAA6B;IAC7B,OAAO,CACL,KAAC,aAAa,cACZ,KAAC,aAAa,IACZ,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,UAAU,GAClB,GACY,CACjB,CAAC;AACJ,CAAC"}