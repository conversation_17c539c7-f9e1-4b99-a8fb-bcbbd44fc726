/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibraryIn = {
  name: string;
  description?: string | null | undefined;
  chunkSize?: number | null | undefined;
};

/** @internal */
export const LibraryIn$inboundSchema: z.ZodType<
  LibraryIn,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.string(),
  description: z.nullable(z.string()).optional(),
  chunk_size: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    "chunk_size": "chunkSize",
  });
});

/** @internal */
export type LibraryIn$Outbound = {
  name: string;
  description?: string | null | undefined;
  chunk_size?: number | null | undefined;
};

/** @internal */
export const LibraryIn$outboundSchema: z.ZodType<
  LibraryIn$Outbound,
  z.ZodTypeDef,
  LibraryIn
> = z.object({
  name: z.string(),
  description: z.nullable(z.string()).optional(),
  chunkSize: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    chunkSize: "chunk_size",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibraryIn$ {
  /** @deprecated use `LibraryIn$inboundSchema` instead. */
  export const inboundSchema = LibraryIn$inboundSchema;
  /** @deprecated use `LibraryIn$outboundSchema` instead. */
  export const outboundSchema = LibraryIn$outboundSchema;
  /** @deprecated use `LibraryIn$Outbound` instead. */
  export type Outbound = LibraryIn$Outbound;
}

export function libraryInToJSON(libraryIn: LibraryIn): string {
  return JSON.stringify(LibraryIn$outboundSchema.parse(libraryIn));
}

export function libraryInFromJSON(
  jsonString: string,
): SafeParseResult<LibraryIn, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibraryIn$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibraryIn' from JSON`,
  );
}
