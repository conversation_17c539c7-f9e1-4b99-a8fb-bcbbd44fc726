/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobsApiRoutesBatchGetBatchJobsRequest = {
  page?: number | undefined;
  pageSize?: number | undefined;
  model?: string | null | undefined;
  agentId?: string | null | undefined;
  metadata?: { [k: string]: any } | null | undefined;
  createdAfter?: Date | null | undefined;
  createdByMe?: boolean | undefined;
  status?: Array<components.BatchJobStatus> | null | undefined;
};

/** @internal */
export const JobsApiRoutesBatchGetBatchJobsRequest$inboundSchema: z.ZodType<
  JobsApiRoutesBatchGetBatchJobsRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  page: z.number().int().default(0),
  page_size: z.number().int().default(100),
  model: z.nullable(z.string()).optional(),
  agent_id: z.nullable(z.string()).optional(),
  metadata: z.nullable(z.record(z.any())).optional(),
  created_after: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  created_by_me: z.boolean().default(false),
  status: z.nullable(z.array(components.BatchJobStatus$inboundSchema))
    .optional(),
}).transform((v) => {
  return remap$(v, {
    "page_size": "pageSize",
    "agent_id": "agentId",
    "created_after": "createdAfter",
    "created_by_me": "createdByMe",
  });
});

/** @internal */
export type JobsApiRoutesBatchGetBatchJobsRequest$Outbound = {
  page: number;
  page_size: number;
  model?: string | null | undefined;
  agent_id?: string | null | undefined;
  metadata?: { [k: string]: any } | null | undefined;
  created_after?: string | null | undefined;
  created_by_me: boolean;
  status?: Array<string> | null | undefined;
};

/** @internal */
export const JobsApiRoutesBatchGetBatchJobsRequest$outboundSchema: z.ZodType<
  JobsApiRoutesBatchGetBatchJobsRequest$Outbound,
  z.ZodTypeDef,
  JobsApiRoutesBatchGetBatchJobsRequest
> = z.object({
  page: z.number().int().default(0),
  pageSize: z.number().int().default(100),
  model: z.nullable(z.string()).optional(),
  agentId: z.nullable(z.string()).optional(),
  metadata: z.nullable(z.record(z.any())).optional(),
  createdAfter: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  createdByMe: z.boolean().default(false),
  status: z.nullable(z.array(components.BatchJobStatus$outboundSchema))
    .optional(),
}).transform((v) => {
  return remap$(v, {
    pageSize: "page_size",
    agentId: "agent_id",
    createdAfter: "created_after",
    createdByMe: "created_by_me",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesBatchGetBatchJobsRequest$ {
  /** @deprecated use `JobsApiRoutesBatchGetBatchJobsRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesBatchGetBatchJobsRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesBatchGetBatchJobsRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesBatchGetBatchJobsRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesBatchGetBatchJobsRequest$Outbound` instead. */
  export type Outbound = JobsApiRoutesBatchGetBatchJobsRequest$Outbound;
}

export function jobsApiRoutesBatchGetBatchJobsRequestToJSON(
  jobsApiRoutesBatchGetBatchJobsRequest: JobsApiRoutesBatchGetBatchJobsRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesBatchGetBatchJobsRequest$outboundSchema.parse(
      jobsApiRoutesBatchGetBatchJobsRequest,
    ),
  );
}

export function jobsApiRoutesBatchGetBatchJobsRequestFromJSON(
  jsonString: string,
): SafeParseResult<JobsApiRoutesBatchGetBatchJobsRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesBatchGetBatchJobsRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'JobsApiRoutesBatchGetBatchJobsRequest' from JSON`,
  );
}
