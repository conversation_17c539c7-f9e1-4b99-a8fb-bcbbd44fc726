/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
/**
 * Environment configuration schema
 */
declare const EnvSchema: z.ZodObject<{
    OPENAI_API_KEY: z.ZodOptional<z.ZodString>;
    OPENAI_BASE_URL: z.ZodDefault<z.ZodString>;
    OPENAI_MODEL: z.ZodDefault<z.ZodString>;
    OPENAI_MAX_TOKENS: z.ZodDefault<z.ZodNumber>;
    OPENAI_TEMPERATURE: z.ZodDefault<z.ZodNumber>;
    ANTHROPIC_API_KEY: z.ZodOptional<z.ZodString>;
    ANTHROPIC_BASE_URL: z.ZodDefault<z.ZodString>;
    ANTHROPIC_MODEL: z.ZodDefault<z.ZodString>;
    ANTHROPIC_MAX_TOKENS: z.<PERSON>od<PERSON>efault<z.ZodNumber>;
    ANTHROPIC_TEMPERATURE: z.ZodDefault<z.ZodNumber>;
    GOOGLE_API_KEY: z.ZodOptional<z.ZodString>;
    GOOGLE_BASE_URL: z.ZodDefault<z.ZodString>;
    GOOGLE_MODEL: z.ZodDefault<z.ZodString>;
    GOOGLE_MAX_TOKENS: z.ZodDefault<z.ZodNumber>;
    GOOGLE_TEMPERATURE: z.ZodDefault<z.ZodNumber>;
    MISTRAL_API_KEY: z.ZodOptional<z.ZodString>;
    MISTRAL_BASE_URL: z.ZodDefault<z.ZodString>;
    MISTRAL_MODEL: z.ZodDefault<z.ZodString>;
    MISTRAL_MAX_TOKENS: z.ZodDefault<z.ZodNumber>;
    MISTRAL_TEMPERATURE: z.ZodDefault<z.ZodNumber>;
    DEFAULT_PROVIDER: z.ZodDefault<z.ZodEnum<["openai", "anthropic", "google", "mistral"]>>;
    LOG_LEVEL: z.ZodDefault<z.ZodEnum<["debug", "info", "warn", "error"]>>;
    LOG_FORMAT: z.ZodDefault<z.ZodEnum<["text", "json"]>>;
    DEBUG: z.ZodDefault<z.ZodBoolean>;
    UI_THEME: z.ZodDefault<z.ZodEnum<["dark", "light"]>>;
    UI_ANIMATIONS: z.ZodDefault<z.ZodBoolean>;
    UI_SHOW_TIMESTAMPS: z.ZodDefault<z.ZodBoolean>;
    UI_SHOW_TOKEN_COUNT: z.ZodDefault<z.ZodBoolean>;
    UI_MAX_HISTORY_LINES: z.ZodDefault<z.ZodNumber>;
    MAX_CONCURRENT_REQUESTS: z.ZodDefault<z.ZodNumber>;
    REQUEST_TIMEOUT: z.ZodDefault<z.ZodNumber>;
    RETRY_ATTEMPTS: z.ZodDefault<z.ZodNumber>;
    RETRY_DELAY: z.ZodDefault<z.ZodNumber>;
    SANDBOX_ENABLED: z.ZodDefault<z.ZodBoolean>;
    ENCRYPTION_ENABLED: z.ZodDefault<z.ZodBoolean>;
    TELEMETRY_ENABLED: z.ZodDefault<z.ZodBoolean>;
    MCP_ENABLED: z.ZodDefault<z.ZodBoolean>;
    MCP_SERVERS_CONFIG_PATH: z.ZodDefault<z.ZodString>;
    MEMORY_ENABLED: z.ZodDefault<z.ZodBoolean>;
    MEMORY_MAX_SIZE: z.ZodDefault<z.ZodNumber>;
    MEMORY_TTL: z.ZodDefault<z.ZodNumber>;
    FILE_INTEGRATION_ENABLED: z.ZodDefault<z.ZodBoolean>;
    FILE_MAX_SIZE: z.ZodDefault<z.ZodNumber>;
    FILE_ALLOWED_EXTENSIONS: z.ZodDefault<z.ZodString>;
    NODE_ENV: z.ZodDefault<z.ZodEnum<["development", "production", "test"]>>;
    PORT: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    OPENAI_BASE_URL: string;
    OPENAI_MODEL: string;
    OPENAI_MAX_TOKENS: number;
    OPENAI_TEMPERATURE: number;
    ANTHROPIC_BASE_URL: string;
    ANTHROPIC_MODEL: string;
    ANTHROPIC_MAX_TOKENS: number;
    ANTHROPIC_TEMPERATURE: number;
    GOOGLE_BASE_URL: string;
    GOOGLE_MODEL: string;
    GOOGLE_MAX_TOKENS: number;
    GOOGLE_TEMPERATURE: number;
    MISTRAL_BASE_URL: string;
    MISTRAL_MODEL: string;
    MISTRAL_MAX_TOKENS: number;
    MISTRAL_TEMPERATURE: number;
    DEFAULT_PROVIDER: "openai" | "anthropic" | "google" | "mistral";
    LOG_LEVEL: "debug" | "info" | "warn" | "error";
    LOG_FORMAT: "text" | "json";
    DEBUG: boolean;
    UI_THEME: "dark" | "light";
    UI_ANIMATIONS: boolean;
    UI_SHOW_TIMESTAMPS: boolean;
    UI_SHOW_TOKEN_COUNT: boolean;
    UI_MAX_HISTORY_LINES: number;
    MAX_CONCURRENT_REQUESTS: number;
    REQUEST_TIMEOUT: number;
    RETRY_ATTEMPTS: number;
    RETRY_DELAY: number;
    SANDBOX_ENABLED: boolean;
    ENCRYPTION_ENABLED: boolean;
    TELEMETRY_ENABLED: boolean;
    MCP_ENABLED: boolean;
    MCP_SERVERS_CONFIG_PATH: string;
    MEMORY_ENABLED: boolean;
    MEMORY_MAX_SIZE: number;
    MEMORY_TTL: number;
    FILE_INTEGRATION_ENABLED: boolean;
    FILE_MAX_SIZE: number;
    FILE_ALLOWED_EXTENSIONS: string;
    NODE_ENV: "development" | "production" | "test";
    PORT: number;
    OPENAI_API_KEY?: string | undefined;
    ANTHROPIC_API_KEY?: string | undefined;
    GOOGLE_API_KEY?: string | undefined;
    MISTRAL_API_KEY?: string | undefined;
}, {
    OPENAI_API_KEY?: string | undefined;
    OPENAI_BASE_URL?: string | undefined;
    OPENAI_MODEL?: string | undefined;
    OPENAI_MAX_TOKENS?: number | undefined;
    OPENAI_TEMPERATURE?: number | undefined;
    ANTHROPIC_API_KEY?: string | undefined;
    ANTHROPIC_BASE_URL?: string | undefined;
    ANTHROPIC_MODEL?: string | undefined;
    ANTHROPIC_MAX_TOKENS?: number | undefined;
    ANTHROPIC_TEMPERATURE?: number | undefined;
    GOOGLE_API_KEY?: string | undefined;
    GOOGLE_BASE_URL?: string | undefined;
    GOOGLE_MODEL?: string | undefined;
    GOOGLE_MAX_TOKENS?: number | undefined;
    GOOGLE_TEMPERATURE?: number | undefined;
    MISTRAL_API_KEY?: string | undefined;
    MISTRAL_BASE_URL?: string | undefined;
    MISTRAL_MODEL?: string | undefined;
    MISTRAL_MAX_TOKENS?: number | undefined;
    MISTRAL_TEMPERATURE?: number | undefined;
    DEFAULT_PROVIDER?: "openai" | "anthropic" | "google" | "mistral" | undefined;
    LOG_LEVEL?: "debug" | "info" | "warn" | "error" | undefined;
    LOG_FORMAT?: "text" | "json" | undefined;
    DEBUG?: boolean | undefined;
    UI_THEME?: "dark" | "light" | undefined;
    UI_ANIMATIONS?: boolean | undefined;
    UI_SHOW_TIMESTAMPS?: boolean | undefined;
    UI_SHOW_TOKEN_COUNT?: boolean | undefined;
    UI_MAX_HISTORY_LINES?: number | undefined;
    MAX_CONCURRENT_REQUESTS?: number | undefined;
    REQUEST_TIMEOUT?: number | undefined;
    RETRY_ATTEMPTS?: number | undefined;
    RETRY_DELAY?: number | undefined;
    SANDBOX_ENABLED?: boolean | undefined;
    ENCRYPTION_ENABLED?: boolean | undefined;
    TELEMETRY_ENABLED?: boolean | undefined;
    MCP_ENABLED?: boolean | undefined;
    MCP_SERVERS_CONFIG_PATH?: string | undefined;
    MEMORY_ENABLED?: boolean | undefined;
    MEMORY_MAX_SIZE?: number | undefined;
    MEMORY_TTL?: number | undefined;
    FILE_INTEGRATION_ENABLED?: boolean | undefined;
    FILE_MAX_SIZE?: number | undefined;
    FILE_ALLOWED_EXTENSIONS?: string | undefined;
    NODE_ENV?: "development" | "production" | "test" | undefined;
    PORT?: number | undefined;
}>;
export type EnvConfig = z.infer<typeof EnvSchema>;
/**
 * Load and validate environment configuration
 */
export declare function loadEnvConfig(envPath?: string): EnvConfig;
/**
 * Get provider configuration from environment
 */
export declare function getProviderConfig(env: EnvConfig, provider: string): {
    type: "openai";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
} | {
    type: "anthropic";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
} | {
    type: "google";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
} | {
    type: "mistral";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
};
/**
 * Get all available providers from environment
 */
export declare function getAvailableProviders(env: EnvConfig): ({
    type: "openai";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
} | {
    type: "anthropic";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
} | {
    type: "google";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
} | {
    type: "mistral";
    apiKey: string | undefined;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    enabled: boolean;
})[];
/**
 * Validate that at least one provider is configured
 */
export declare function validateProviders(env: EnvConfig): void;
export {};
//# sourceMappingURL=env.d.ts.map