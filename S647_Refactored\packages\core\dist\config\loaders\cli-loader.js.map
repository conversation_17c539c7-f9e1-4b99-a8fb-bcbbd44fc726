{"version": 3, "file": "cli-loader.js", "sourceRoot": "", "sources": ["../../../src/config/loaders/cli-loader.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH;;;GAGG;AACH,MAAM,OAAO,eAAe;IACV,IAAI,GAAG,KAAK,CAAC;IACb,QAAQ,GAAG,GAAG,CAAC,CAAC,mBAAmB;IAEnD;;OAEG;IACI,KAAK,CAAC,IAAI,CAAC,OAA6B;QAC7C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,IAAI,KAAK,CAAC,yCAAyC,CAAC;iBAC5D,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,MAAM,EAAE;oBACN,IAAI,EAAE,KAAK;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,QAAQ,EAAE;wBACR,MAAM,EAAE,IAAI,CAAC,IAAI;wBACjB,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;qBAC1C;iBACF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,kCAAkC,CAAC;aACtF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,OAA6B;QAChD,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAyB;QACjD,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,gDAAgD;QAChD,MAAM,WAAW,GAA2B;YAC1C,gBAAgB;YAChB,UAAU,EAAE,iBAAiB;YAC7B,kBAAkB,EAAE,iBAAiB;YACrC,aAAa,EAAE,aAAa;YAC5B,KAAK,EAAE,aAAa;YACpB,SAAS,EAAE,SAAS;YAEpB,cAAc;YACd,OAAO,EAAE,UAAU;YACnB,eAAe,EAAE,eAAe;YAChC,SAAS,EAAE,YAAY;YACvB,OAAO,EAAE,YAAY;YAErB,mBAAmB;YACnB,WAAW,EAAE,eAAe;YAC5B,YAAY,EAAE,gBAAgB;YAC9B,YAAY,EAAE,gBAAgB;YAE9B,gBAAgB;YAChB,OAAO,EAAE,eAAe;YACxB,cAAc,EAAE,eAAe;YAC/B,eAAe,EAAE,eAAe;YAEhC,6BAA6B;YAC7B,SAAS,EAAE,0BAA0B;YACrC,UAAU,EAAE,2BAA2B;YACvC,SAAS,EAAE,2BAA2B;YAEtC,qBAAqB;YACrB,WAAW,EAAE,mBAAmB;YAChC,cAAc,EAAE,mBAAmB;SACpC,CAAC;QAEF,KAAK,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/D,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACnB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEzB,uBAAuB;gBACvB,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,iBAAiB;gBACnC,CAAC;qBAAM,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;oBAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,6BAA6B;gBAC/C,CAAC;qBAAM,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;oBACrC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,iBAAiB;gBACnC,CAAC;qBAAM,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;oBAC3D,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvF,CAAC;qBAAM,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBACtC,gEAAgE;oBAChE,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;oBACnG,mDAAmD;oBACnD,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,IAAyB,EAAE,MAAW;QACnE,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEvG,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACtC,MAAM,cAAc,GAAQ,EAAE,CAAC;YAE/B,uCAAuC;YACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC;oBACjC,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAEjD,gCAAgC;oBAChC,QAAQ,WAAW,EAAE,CAAC;wBACpB,KAAK,SAAS;4BACZ,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;4BAC9B,MAAM;wBACR,KAAK,UAAU;4BACb,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC;4BAC/B,MAAM;wBACR,KAAK,SAAS;4BACZ,cAAc,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;4BAC7C,MAAM;wBACR,KAAK,SAAS;4BACZ,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC;4BAC/B,MAAM;wBACR;4BACE,cAAc,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;oBACxC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,SAAS;oBAAE,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;YAC5C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAQ,EAAE,IAAc,EAAE,KAAU;QACzD,IAAI,OAAO,GAAG,GAAG,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,GAAG;gBAAE,SAAS;YAEnB,IAAI,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC1D,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACpB,CAAC;YACD,OAAO,GAAG,OAAO,CAAC,GAAG,CAAQ,CAAC;QAChC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAyB;QAClD,MAAM,UAAU,GAAG;YACjB,UAAU,EAAE,kBAAkB,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS;YAC/D,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,OAAO;YAC5C,WAAW,EAAE,YAAY,EAAE,YAAY;YACvC,OAAO,EAAE,cAAc,EAAE,eAAe;YACxC,SAAS,EAAE,UAAU,EAAE,SAAS;YAChC,WAAW,EAAE,cAAc;SAC5B,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IAC9F,CAAC;CACF"}