/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { render } from 'ink';
import { parseArguments } from './config/args.js';
import { loadConfiguration } from './config/loader.js';
import { createLogger } from '@inkbytefo/s647-shared';
import { App } from './ui/App.js';
/**
 * Main entry point for the CLI application
 */
export async function main() {
    try {
        console.log('🚀 Starting S647 Refactored CLI...\n');
        // Parse command line arguments
        const args = parseArguments();
        // Create logger
        const logger = createLogger({
            level: args.verbose ? 'debug' : 'info',
            format: 'text',
            output: 'console',
        });
        logger.info('Initializing S647 Refactored...');
        // Load configuration
        logger.info('Loading configuration...');
        const config = await loadConfiguration(args);
        logger.info(`Configuration loaded successfully`);
        logger.info(`Environment: ${config.environment}`);
        logger.info(`Default Provider: ${config.defaultProvider}`);
        logger.info(`Enabled Tools: ${config.tools.enabled.join(', ')}`);
        // Check for non-interactive mode
        if (args.nonInteractive || args.help || args.version) {
            // Handle non-interactive commands
            if (args.help) {
                showHelp();
                return;
            }
            if (args.version) {
                console.log(`S647 Refactored v${config.version}`);
                return;
            }
            // TODO: Implement non-interactive mode
            logger.warn('Non-interactive mode not yet implemented');
            return;
        }
        // Start interactive UI
        logger.info('Starting interactive UI...');
        const { unmount } = render(React.createElement(App, {
            args,
            config,
            logger,
        }));
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            logger.info('Received SIGINT, shutting down gracefully...');
            unmount();
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            logger.info('Received SIGTERM, shutting down gracefully...');
            unmount();
            process.exit(0);
        });
    }
    catch (error) {
        console.error('❌ Failed to start S647 Refactored:');
        if (error instanceof Error) {
            console.error(error.message);
            if (process.env.DEBUG) {
                console.error(error.stack);
            }
        }
        else {
            console.error(String(error));
        }
        process.exit(1);
    }
}
/**
 * Show help information
 */
function showHelp() {
    console.log(`
🚀 S647 Refactored - Advanced AI-Powered CLI Agent

USAGE:
  s647 [OPTIONS] [COMMAND]

OPTIONS:
  -h, --help              Show this help message
  -v, --version           Show version information
  --verbose               Enable verbose logging
  --non-interactive       Run in non-interactive mode
  --config <path>         Specify configuration file path
  --provider <name>       Set default AI provider
  --theme <theme>         Set UI theme (dark, light, auto)

INTERACTIVE MODE:
  1-4                     Quick screen navigation
  Alt+1-4                 Alternative navigation
  F5-F8                   Function key navigation
  Ctrl+C                  Exit application

SCREENS:
  1/F5: Chat              AI chat interface
  2/F6: Config            Configuration management
  3/F7: Providers         AI provider management
  4/F8: Tools             Tool management

EXAMPLES:
  s647                    Start interactive mode
  s647 --help             Show this help
  s647 --version          Show version
  s647 --provider openai  Start with OpenAI as default

For more information, visit: https://github.com/inkbytefo/S647-refactored
`);
}
//# sourceMappingURL=main.js.map