/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { DEFAULT_CONFIG } from '@inkbytefo/s647-shared';
import type { ConfigLoader, ConfigLoaderOptions, LoadResult } from './types.js';

/**
 * Default configuration loader
 * Provides fallback configuration values
 */
export class DefaultConfigLoader implements ConfigLoader {
  public readonly name = 'default';
  public readonly priority = 0; // Lowest priority

  /**
   * Load default configuration
   */
  public async load(_options?: ConfigLoaderOptions): Promise<LoadResult> {
    try {
      return {
        success: true,
        config: DEFAULT_CONFIG as any,
        source: {
          type: 'default',
          priority: this.priority,
          timestamp: Date.now(),
          metadata: {
            loader: this.name,
            version: DEFAULT_CONFIG.version,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error loading default config'),
      };
    }
  }

  /**
   * Default loader can always load
   */
  public async canLoad(_options?: ConfigLoaderOptions): Promise<boolean> {
    return true;
  }
}
