import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type RetrieveModelV1ModelsModelIdGetRequest = {
    /**
     * The ID of the model to retrieve.
     */
    modelId: string;
};
/**
 * Successful Response
 */
export type RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet = (components.BaseModelCard & {
    type: "base";
}) | (components.FTModelCard & {
    type: "fine-tuned";
});
/** @internal */
export declare const RetrieveModelV1ModelsModelIdGetRequest$inboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type RetrieveModelV1ModelsModelIdGetRequest$Outbound = {
    model_id: string;
};
/** @internal */
export declare const RetrieveModelV1ModelsModelIdGetRequest$outboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetRequest$Outbound, z.ZodTypeDef, RetrieveModelV1ModelsModelIdGetRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace RetrieveModelV1ModelsModelIdGetRequest$ {
    /** @deprecated use `RetrieveModelV1ModelsModelIdGetRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `RetrieveModelV1ModelsModelIdGetRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetRequest$Outbound, z.ZodTypeDef, RetrieveModelV1ModelsModelIdGetRequest>;
    /** @deprecated use `RetrieveModelV1ModelsModelIdGetRequest$Outbound` instead. */
    type Outbound = RetrieveModelV1ModelsModelIdGetRequest$Outbound;
}
export declare function retrieveModelV1ModelsModelIdGetRequestToJSON(retrieveModelV1ModelsModelIdGetRequest: RetrieveModelV1ModelsModelIdGetRequest): string;
export declare function retrieveModelV1ModelsModelIdGetRequestFromJSON(jsonString: string): SafeParseResult<RetrieveModelV1ModelsModelIdGetRequest, SDKValidationError>;
/** @internal */
export declare const RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$inboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet, z.ZodTypeDef, unknown>;
/** @internal */
export type RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound = (components.BaseModelCard$Outbound & {
    type: "base";
}) | (components.FTModelCard$Outbound & {
    type: "fine-tuned";
});
/** @internal */
export declare const RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$outboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound, z.ZodTypeDef, RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$ {
    /** @deprecated use `RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$inboundSchema` instead. */
    const inboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet, z.ZodTypeDef, unknown>;
    /** @deprecated use `RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$outboundSchema` instead. */
    const outboundSchema: z.ZodType<RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound, z.ZodTypeDef, RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet>;
    /** @deprecated use `RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound` instead. */
    type Outbound = RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound;
}
export declare function retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGetToJSON(retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet: RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet): string;
export declare function retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGetFromJSON(jsonString: string): SafeParseResult<RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet, SDKValidationError>;
//# sourceMappingURL=retrievemodelv1modelsmodelidget.d.ts.map