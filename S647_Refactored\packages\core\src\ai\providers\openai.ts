/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  OpenAIProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * OpenAI provider implementation
 */
export class OpenAIProvider extends BaseProvider {
  private client: any; // OpenAI client instance
  
  constructor(id: ProviderId, config: OpenAIProviderConfig) {
    super(id, config);
  }

  public get openaiConfig(): OpenAIProviderConfig {
    return this.config as OpenAIProviderConfig;
  }

  /**
   * Initialize the OpenAI provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();
      
      // Initialize OpenAI client
      const { OpenAI } = await import('openai');
      this.client = new OpenAI({
        apiKey: this.openaiConfig.apiKey,
        baseURL: this.openaiConfig.baseUrl,
        organization: this.openaiConfig.organization,
        project: this.openaiConfig.project,
        timeout: this.openaiConfig.timeout || 30000,
        maxRetries: this.openaiConfig.retries || 3,
        defaultHeaders: this.openaiConfig.headers,
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Test with a simple models list call
      await this.client.models.list();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available models
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.models.list();
      const models: ModelInfo[] = response.data.map((model: any) => ({
        id: model.id,
        name: model.id,
        provider: 'openai' as const,
        capabilities: {
          chat: model.id.includes('gpt'),
          completion: true,
          embedding: model.id.includes('embedding'),
          vision: model.id.includes('vision') || model.id.includes('gpt-4'),
          functionCalling: model.id.includes('gpt'),
          streaming: true,
          systemMessages: true,
          multiModal: model.id.includes('gpt-4'),
        },
        contextLength: this.getContextLength(model.id),
        maxTokens: this.getMaxTokens(model.id),
      }));

      return { success: true, data: models };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: false,
        tools: request.tools,
        tool_choice: request.toolChoice,
        user: request.user,
      });

      return { success: true, data: response as ChatCompletionResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const stream = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: true,
        tools: request.tools,
        tool_choice: request.toolChoice,
        user: request.user,
      });

      return { success: true, data: stream as AsyncIterable<ChatCompletionChunk> };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings
   */
  public async createEmbedding(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.embeddings.create({
        model: request.model,
        input: request.input,
        user: request.user,
      });

      return { success: true, data: response as EmbeddingResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      // For now, use a simple estimation
      // In a real implementation, you'd use tiktoken or similar
      const text = typeof input === 'string' 
        ? input 
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');
      
      const estimatedTokens = Math.ceil(text.length / 4);
      return { success: true, data: estimatedTokens };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Get context length for a model
   */
  private getContextLength(modelId: string): number {
    const contextLengths: Record<string, number> = {
      'gpt-4': 8192,
      'gpt-4-32k': 32768,
      'gpt-4-turbo': 128000,
      'gpt-4o': 128000,
      'gpt-3.5-turbo': 4096,
      'gpt-3.5-turbo-16k': 16384,
    };

    for (const [model, length] of Object.entries(contextLengths)) {
      if (modelId.includes(model)) {
        return length;
      }
    }

    return 4096; // Default
  }

  /**
   * Get max tokens for a model
   */
  private getMaxTokens(modelId: string): number {
    const maxTokens: Record<string, number> = {
      'gpt-4': 4096,
      'gpt-4-32k': 4096,
      'gpt-4-turbo': 4096,
      'gpt-4o': 4096,
      'gpt-3.5-turbo': 4096,
    };

    for (const [model, tokens] of Object.entries(maxTokens)) {
      if (modelId.includes(model)) {
        return tokens;
      }
    }

    return 4096; // Default
  }
}
