/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsGetV1Request = {
  libraryId: string;
  documentId: string;
};

/** @internal */
export const LibrariesDocumentsGetV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsGetV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  document_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "document_id": "documentId",
  });
});

/** @internal */
export type LibrariesDocumentsGetV1Request$Outbound = {
  library_id: string;
  document_id: string;
};

/** @internal */
export const LibrariesDocumentsGetV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsGetV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsGetV1Request
> = z.object({
  libraryId: z.string(),
  documentId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    documentId: "document_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsGetV1Request$ {
  /** @deprecated use `LibrariesDocumentsGetV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesDocumentsGetV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsGetV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesDocumentsGetV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsGetV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsGetV1Request$Outbound;
}

export function librariesDocumentsGetV1RequestToJSON(
  librariesDocumentsGetV1Request: LibrariesDocumentsGetV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsGetV1Request$outboundSchema.parse(
      librariesDocumentsGetV1Request,
    ),
  );
}

export function librariesDocumentsGetV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDocumentsGetV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesDocumentsGetV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDocumentsGetV1Request' from JSON`,
  );
}
