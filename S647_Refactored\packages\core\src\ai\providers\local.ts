/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  LocalProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * Local provider implementation
 * Supports local model servers like Ollama, LocalAI, etc.
 */
export class LocalProvider extends BaseProvider {
  private client: any; // OpenAI client instance for OpenAI-compatible local servers

  constructor(id: ProviderId, config: LocalProviderConfig) {
    super(id, config);
  }

  public get localConfig(): LocalProviderConfig {
    return this.config as LocalProviderConfig;
  }

  /**
   * Initialize the Local provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();

      // Initialize OpenAI client with local endpoint
      const { OpenAI } = await import('openai');

      if (!this.localConfig.endpoint) {
        throw new Error('Local provider endpoint is required');
      }

      this.client = new OpenAI({
        apiKey: this.localConfig.apiKey || 'local-key', // Many local servers don't require real API keys
        baseURL: this.localConfig.endpoint,
        timeout: this.localConfig.timeout || 60000, // Local models might be slower
        maxRetries: this.localConfig.retries || 1, // Fewer retries for local
        defaultHeaders: {
          ...this.localConfig.headers,
        },
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Try to list models first
      try {
        await this.client.models.list();
        return true;
      } catch {
        // If models endpoint doesn't exist, try a simple health check
        const response = await fetch(`${this.localConfig.endpoint}/health`);
        return response.ok;
      }
    } catch {
      return false;
    }
  }

  /**
   * Get available models from local server
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      // Try to get models from the local server
      try {
        const response = await this.client.models.list();
        const models: ModelInfo[] = response.data.map((model: any) => ({
          id: model.id,
          name: model.id,
          provider: 'local' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: model.id.includes('embed'),
            vision: model.id.includes('vision') || model.id.includes('llava'),
            functionCalling: false, // Most local models don't support function calling yet
            streaming: true,
            systemMessages: true,
            multiModal: model.id.includes('vision') || model.id.includes('llava'),
          },
          contextLength: model.context_length || 4096,
          maxTokens: model.max_completion_tokens || 2048,
        }));

        return { success: true, data: models };
      } catch {
        // If models endpoint doesn't exist, return configured models or default
        const models: ModelInfo[] = this.localConfig.models || [{
          id: 'local-model',
          name: 'Local Model',
          provider: 'local' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: false,
            functionCalling: false,
            streaming: true,
            systemMessages: true,
            multiModal: false,
          },
          contextLength: 4096,
          maxTokens: 2048,
        }];

        return { success: true, data: models };
      }
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion (OpenAI-compatible)
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        stop: request.stop,
        stream: false,
        // Note: Most local models don't support tools/functions yet
        ...(request.tools && { tools: request.tools }),
        ...(request.toolChoice && { tool_choice: request.toolChoice }),
      });

      return { success: true, data: response as ChatCompletionResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion (OpenAI-compatible)
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const stream = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        stop: request.stop,
        stream: true,
        // Note: Most local models don't support tools/functions yet
        ...(request.tools && { tools: request.tools }),
        ...(request.toolChoice && { tool_choice: request.toolChoice }),
      });

      return { success: true, data: stream as AsyncIterable<ChatCompletionChunk> };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings (if supported by local server)
   */
  public async createEmbedding(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.embeddings.create({
        model: request.model,
        input: request.input,
      });

      return { success: true, data: response as EmbeddingResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      // Local servers typically don't have token counting endpoints
      // Use estimation based on character count
      const text = typeof input === 'string'
        ? input
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');

      // Use a conservative estimation (roughly 3 characters per token for local models)
      const estimatedTokens = Math.ceil(text.length / 3);
      return { success: true, data: estimatedTokens };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }
}
