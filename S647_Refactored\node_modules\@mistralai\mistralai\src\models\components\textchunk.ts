/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const TextChunkType = {
  Text: "text",
} as const;
export type TextChunkType = ClosedEnum<typeof TextChunkType>;

export type TextChunk = {
  text: string;
  type?: TextChunkType | undefined;
};

/** @internal */
export const TextChunkType$inboundSchema: z.ZodNativeEnum<
  typeof TextChunkType
> = z.nativeEnum(TextChunkType);

/** @internal */
export const TextChunkType$outboundSchema: z.ZodNativeEnum<
  typeof TextChunkType
> = TextChunkType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TextChunkType$ {
  /** @deprecated use `TextChunkType$inboundSchema` instead. */
  export const inboundSchema = TextChunkType$inboundSchema;
  /** @deprecated use `TextChunkType$outboundSchema` instead. */
  export const outboundSchema = TextChunkType$outboundSchema;
}

/** @internal */
export const TextChunk$inboundSchema: z.ZodType<
  TextChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  text: z.string(),
  type: TextChunkType$inboundSchema.default("text"),
});

/** @internal */
export type TextChunk$Outbound = {
  text: string;
  type: string;
};

/** @internal */
export const TextChunk$outboundSchema: z.ZodType<
  TextChunk$Outbound,
  z.ZodTypeDef,
  TextChunk
> = z.object({
  text: z.string(),
  type: TextChunkType$outboundSchema.default("text"),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TextChunk$ {
  /** @deprecated use `TextChunk$inboundSchema` instead. */
  export const inboundSchema = TextChunk$inboundSchema;
  /** @deprecated use `TextChunk$outboundSchema` instead. */
  export const outboundSchema = TextChunk$outboundSchema;
  /** @deprecated use `TextChunk$Outbound` instead. */
  export type Outbound = TextChunk$Outbound;
}

export function textChunkToJSON(textChunk: TextChunk): string {
  return JSON.stringify(TextChunk$outboundSchema.parse(textChunk));
}

export function textChunkFromJSON(
  jsonString: string,
): SafeParseResult<TextChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TextChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TextChunk' from JSON`,
  );
}
