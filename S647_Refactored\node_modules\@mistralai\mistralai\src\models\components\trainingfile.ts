/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type TrainingFile = {
  fileId: string;
  weight?: number | undefined;
};

/** @internal */
export const TrainingFile$inboundSchema: z.ZodType<
  TrainingFile,
  z.ZodTypeDef,
  unknown
> = z.object({
  file_id: z.string(),
  weight: z.number().default(1),
}).transform((v) => {
  return remap$(v, {
    "file_id": "fileId",
  });
});

/** @internal */
export type TrainingFile$Outbound = {
  file_id: string;
  weight: number;
};

/** @internal */
export const TrainingFile$outboundSchema: z.ZodType<
  TrainingFile$Outbound,
  z.ZodTypeDef,
  TrainingFile
> = z.object({
  fileId: z.string(),
  weight: z.number().default(1),
}).transform((v) => {
  return remap$(v, {
    fileId: "file_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TrainingFile$ {
  /** @deprecated use `TrainingFile$inboundSchema` instead. */
  export const inboundSchema = TrainingFile$inboundSchema;
  /** @deprecated use `TrainingFile$outboundSchema` instead. */
  export const outboundSchema = TrainingFile$outboundSchema;
  /** @deprecated use `TrainingFile$Outbound` instead. */
  export type Outbound = TrainingFile$Outbound;
}

export function trainingFileToJSON(trainingFile: TrainingFile): string {
  return JSON.stringify(TrainingFile$outboundSchema.parse(trainingFile));
}

export function trainingFileFromJSON(
  jsonString: string,
): SafeParseResult<TrainingFile, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TrainingFile$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TrainingFile' from JSON`,
  );
}
