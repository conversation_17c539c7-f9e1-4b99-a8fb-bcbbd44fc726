/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import {
  catchUnrecognizedEnum,
  OpenEnum,
  Unrecognized,
} from "../../types/enums.js";

export const ShareEnum = {
  Viewer: "Viewer",
  Editor: "Editor",
} as const;
export type ShareEnum = OpenEnum<typeof ShareEnum>;

/** @internal */
export const ShareEnum$inboundSchema: z.ZodType<
  ShareEnum,
  z.ZodTypeDef,
  unknown
> = z
  .union([
    z.nativeEnum(ShareEnum),
    z.string().transform(catchUnrecognizedEnum),
  ]);

/** @internal */
export const ShareEnum$outboundSchema: z.ZodType<
  ShareEnum,
  z.ZodTypeDef,
  ShareEnum
> = z.union([
  z.nativeEnum(ShareEnum),
  z.string().and(z.custom<Unrecognized<string>>()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ShareEnum$ {
  /** @deprecated use `ShareEnum$inboundSchema` instead. */
  export const inboundSchema = ShareEnum$inboundSchema;
  /** @deprecated use `ShareEnum$outboundSchema` instead. */
  export const outboundSchema = ShareEnum$outboundSchema;
}
