/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ModerationObject,
  ModerationObject$inboundSchema,
  ModerationObject$Outbound,
  ModerationObject$outboundSchema,
} from "./moderationobject.js";

export type ModerationResponse = {
  id: string;
  model: string;
  results: Array<ModerationObject>;
};

/** @internal */
export const ModerationResponse$inboundSchema: z.ZodType<
  ModerationResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  model: z.string(),
  results: z.array(ModerationObject$inboundSchema),
});

/** @internal */
export type ModerationResponse$Outbound = {
  id: string;
  model: string;
  results: Array<ModerationObject$Outbound>;
};

/** @internal */
export const ModerationResponse$outboundSchema: z.ZodType<
  ModerationResponse$Outbound,
  z.ZodTypeDef,
  ModerationResponse
> = z.object({
  id: z.string(),
  model: z.string(),
  results: z.array(ModerationObject$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModerationResponse$ {
  /** @deprecated use `ModerationResponse$inboundSchema` instead. */
  export const inboundSchema = ModerationResponse$inboundSchema;
  /** @deprecated use `ModerationResponse$outboundSchema` instead. */
  export const outboundSchema = ModerationResponse$outboundSchema;
  /** @deprecated use `ModerationResponse$Outbound` instead. */
  export type Outbound = ModerationResponse$Outbound;
}

export function moderationResponseToJSON(
  moderationResponse: ModerationResponse,
): string {
  return JSON.stringify(
    ModerationResponse$outboundSchema.parse(moderationResponse),
  );
}

export function moderationResponseFromJSON(
  jsonString: string,
): SafeParseResult<ModerationResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ModerationResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ModerationResponse' from JSON`,
  );
}
