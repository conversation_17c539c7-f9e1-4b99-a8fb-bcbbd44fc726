/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default logging configuration
 */
export const DEFAULT_LOGGING = {
    level: 'info',
    format: 'text',
    output: 'console',
    structured: false,
    includeTimestamp: true,
    includeLevel: true,
    includeSource: false,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    datePattern: 'YYYY-MM-DD',
};
/**
 * Logging configurations for different environments
 */
export const LOGGING_CONFIGS = {
    development: {
        ...DEFAULT_LOGGING,
        level: 'debug',
        includeSource: true,
        structured: false,
    },
    production: {
        ...DEFAULT_LOGGING,
        level: 'warn',
        format: 'json',
        structured: true,
        includeSource: false,
    },
    test: {
        ...DEFAULT_LOGGING,
        level: 'error',
        output: 'file',
        structured: false,
    },
};
/**
 * Get logging configuration for environment
 */
export function getLoggingConfigForEnvironment(env) {
    return LOGGING_CONFIGS[env] ?? DEFAULT_LOGGING;
}
/**
 * Log level priorities
 */
export const LOG_LEVELS = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
};
/**
 * Check if log level should be logged
 */
export function shouldLog(currentLevel, messageLevel) {
    return LOG_LEVELS[messageLevel] >= LOG_LEVELS[currentLevel];
}
//# sourceMappingURL=logging.js.map