# S647 Refactored Architecture Guide

## Overview

S647 Refactored follows a modern, modular architecture designed for scalability, maintainability, and extensibility. The system is built using TypeScript and follows clean architecture principles with clear separation of concerns.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        CLI Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Commands  │  │     UI      │  │    Configuration    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        Core Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ AI Manager  │  │    Tools    │  │      Services       │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  Providers  │  │   Config    │  │     Utilities       │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                       Shared Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    Types    │  │   Schemas   │  │     Constants       │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Package Structure

### 1. Shared Package (`@s647/shared`)

The shared package contains common types, schemas, and utilities used across all other packages.

```
packages/shared/
├── src/
│   ├── types/           # TypeScript type definitions
│   │   ├── common.ts    # Common utility types
│   │   ├── providers.ts # AI provider types
│   │   ├── tools.ts     # Tool system types
│   │   ├── config.ts    # Configuration types
│   │   └── ui.ts        # UI component types
│   ├── schemas/         # Zod validation schemas
│   │   ├── config.ts    # Configuration schemas
│   │   ├── providers.ts # Provider schemas
│   │   └── tools.ts     # Tool schemas
│   ├── constants/       # Shared constants
│   │   ├── defaults.ts  # Default values
│   │   ├── errors.ts    # Error codes
│   │   └── versions.ts  # Version constants
│   └── utils/           # Shared utilities
│       ├── validation.ts # Validation helpers
│       ├── formatting.ts # Formatting utilities
│       └── helpers.ts    # General helpers
```

**Key Features:**
- Type-safe interfaces for all system components
- Zod schemas for runtime validation
- Shared constants and utilities
- Brand types for type safety

### 2. Core Package (`@s647/core`)

The core package contains the main business logic and AI functionality.

```
packages/core/
├── src/
│   ├── ai/              # AI provider system
│   │   ├── interfaces/  # Provider interfaces
│   │   ├── providers/   # Provider implementations
│   │   ├── models/      # Model utilities
│   │   ├── manager.ts   # AI Manager
│   │   └── client.ts    # AI Client
│   ├── tools/           # Tool system
│   │   ├── registry/    # Tool registration
│   │   ├── implementations/ # Tool implementations
│   │   └── types/       # Tool type definitions
│   ├── config/          # Configuration management
│   │   ├── loaders/     # Config file loaders
│   │   ├── validators/  # Config validation
│   │   ├── defaults/    # Default configurations
│   │   └── manager.ts   # Configuration manager
│   ├── services/        # Business logic services
│   │   ├── chat/        # Chat management
│   │   ├── file/        # File operations
│   │   ├── git/         # Git operations
│   │   └── memory/      # Memory management
│   └── utils/           # Core utilities
│       ├── errors/      # Error handling
│       ├── validation/  # Input validation
│       └── helpers/     # General helpers
```

**Key Features:**
- Plugin-based AI provider system
- Extensible tool registry
- Hierarchical configuration management
- Service-oriented architecture

### 3. CLI Package (`@s647/cli`)

The CLI package provides the command-line interface and user interaction.

```
packages/cli/
├── src/
│   ├── commands/        # Command implementations
│   │   ├── interactive/ # Interactive mode commands
│   │   ├── batch/       # Batch mode commands
│   │   └── config/      # Configuration commands
│   ├── ui/              # User interface components
│   │   ├── components/  # Reusable UI components
│   │   ├── screens/     # Full screen components
│   │   ├── themes/      # Theme management
│   │   └── hooks/       # Custom React hooks
│   ├── config/          # CLI-specific configuration
│   │   ├── args.ts      # Argument parsing
│   │   ├── loader.ts    # Configuration loading
│   │   └── types.ts     # CLI types
│   ├── services/        # CLI services
│   │   ├── input.ts     # Input handling
│   │   ├── output.ts    # Output formatting
│   │   └── history.ts   # Command history
│   └── utils/           # CLI utilities
│       ├── logger.ts    # Logging setup
│       ├── system.ts    # System checks
│       └── cleanup.ts   # Cleanup handlers
```

**Key Features:**
- React-based terminal UI with Ink
- Interactive and non-interactive modes
- Comprehensive command system
- Theme and customization support

## Design Principles

### 1. Separation of Concerns

Each package has a clear responsibility:
- **Shared**: Common types and utilities
- **Core**: Business logic and AI functionality
- **CLI**: User interface and interaction

### 2. Dependency Inversion

Higher-level modules don't depend on lower-level modules. Both depend on abstractions:
- Providers implement interfaces
- Services use dependency injection
- Configuration is externalized

### 3. Plugin Architecture

The system is designed to be extensible:
- AI providers are plugins
- Tools are plugins
- UI themes are plugins
- Configuration loaders are plugins

### 4. Type Safety

Strong typing throughout the system:
- TypeScript strict mode
- Runtime validation with Zod
- Brand types for domain concepts
- Comprehensive error types

## AI Provider System

### Provider Interface

All AI providers implement the `Provider` interface:

```typescript
interface Provider {
  readonly id: ProviderId;
  readonly type: ProviderType;
  readonly config: ProviderConfig;
  readonly status: ProviderStatus;

  initialize(): AsyncResult<void>;
  isAvailable(): Promise<boolean>;
  getModels(): AsyncResult<ModelInfo[]>;
  createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
  createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
  createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
  countTokens(input: string | ChatMessage[]): AsyncResult<number>;
  dispose(): Promise<void>;
}
```

### Provider Factory

The `ProviderFactory` creates provider instances:

```typescript
class ProviderFactory {
  create(id: ProviderId, config: ProviderConfig): AsyncResult<Provider>;
  getSupportedTypes(): ProviderType[];
  validateConfig(config: ProviderConfig): AsyncResult<void>;
}
```

### Provider Registry

The `ProviderRegistry` manages provider instances:

```typescript
class ProviderRegistry {
  register(provider: Provider): void;
  unregister(id: ProviderId): void;
  get(id: ProviderId): Provider | undefined;
  getAll(): Provider[];
  getByType(type: ProviderType): Provider[];
  getAvailable(): Provider[];
  findBestProvider(modelId: string): Provider | undefined;
}
```

### AI Manager

The `AIManager` coordinates all AI operations:

```typescript
class AIManager {
  initialize(configs: Record<string, ProviderConfig>, defaultProvider?: string): AsyncResult<void>;
  getDefaultProvider(): Provider | undefined;
  getProvider(id: ProviderId): Provider | undefined;
  createChatCompletion(request: ChatCompletionRequest, providerId?: ProviderId): AsyncResult<ChatCompletionResponse>;
  createChatCompletionStream(request: ChatCompletionRequest, providerId?: ProviderId): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
  createEmbedding(request: EmbeddingRequest, providerId?: ProviderId): AsyncResult<EmbeddingResponse>;
  countTokens(input: string | ChatMessage[], modelId?: string, providerId?: ProviderId): AsyncResult<number>;
  getAvailableModels(): AsyncResult<ModelInfo[]>;
}
```

## Configuration System

### Hierarchical Configuration

Configuration is loaded from multiple sources in order of priority:

1. CLI arguments (highest priority)
2. Environment variables
3. Project configuration file (`.s647/config.json`)
4. Global configuration file (`~/.s647/config.json`)
5. Default configuration (lowest priority)

### Configuration Schema

All configuration is validated using Zod schemas:

```typescript
const ConfigurationSchema = z.object({
  version: z.string(),
  environment: z.enum(['development', 'production', 'test']),
  defaultProvider: z.string(),
  providers: z.record(ProviderConfigSchema),
  tools: ToolsConfigSchema,
  ui: UIConfigSchema,
  logging: LoggingConfigSchema,
  // ... other schemas
});
```

### Configuration Loading System

The configuration system uses a hierarchical loading approach with multiple loaders:

#### Configuration Loaders

1. **DefaultConfigLoader** (Priority: 0)
   - Provides fallback configuration values
   - Always available as the base configuration

2. **FileConfigLoader** (Priority: 50)
   - Loads from JSON configuration files
   - Search paths: `.s647/config.json`, `.s647.json`, `~/.s647/config.json`
   - Supports project-specific and global configurations

3. **EnvironmentConfigLoader** (Priority: 75)
   - Loads from environment variables with `S647_` prefix
   - Supports nested configuration via underscore notation
   - Example: `S647_PROVIDERS_OPENAI_API_KEY=sk-...`

4. **CliConfigLoader** (Priority: 100)
   - Highest priority loader for CLI arguments
   - Supports provider-specific arguments
   - Example: `--openai-api-key=sk-... --theme=dark`

#### Configuration Merge Strategy

Configurations are merged using a deep merge strategy where:
- Higher priority sources override lower priority sources
- Objects are merged recursively
- Arrays can be replaced, merged, or appended based on strategy
- Warnings are collected and reported for debugging
```

### Configuration Manager

The `ConfigurationManager` handles all configuration operations:

```typescript
class ConfigurationManager {
  load(sources: ConfigurationSource[]): AsyncResult<Configuration>;
  validate(config: Configuration): ConfigurationValidationResult;
  merge(configs: Partial<Configuration>[]): Configuration;
  save(config: Configuration, path: string): AsyncResult<void>;
  migrate(config: Configuration, targetVersion: string): ConfigurationMigrationResult;
}
```

## Tool System

### Tool Interface

All tools implement the `Tool` interface:

```typescript
interface Tool {
  readonly id: ToolId;
  readonly name: string;
  readonly description: string;
  readonly version: string;
  readonly schema: JsonSchema;

  execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
  validate(params: ToolParams): ValidationResult;
  getHelp(): ToolHelp;
}
```

### Tool Registry

The `ToolRegistry` manages tool instances:

```typescript
class ToolRegistry {
  register(tool: Tool): void;
  unregister(id: ToolId): void;
  get(id: ToolId): Tool | undefined;
  getAll(): Tool[];
  getByCategory(category: string): Tool[];
  search(query: string): Tool[];
}
```

## Error Handling

### Result Type

All async operations return a `Result` type:

```typescript
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

type AsyncResult<T, E = Error> = Promise<Result<T, E>>;
```

### Error Classes

Custom error classes for different error types:

```typescript
class ProviderError extends Error {
  constructor(message: string, public providerId: ProviderId, public cause?: Error) {
    super(message);
  }
}

class ConfigurationError extends Error {
  constructor(message: string, public path?: string, public cause?: Error) {
    super(message);
  }
}

class ToolError extends Error {
  constructor(message: string, public toolId: ToolId, public cause?: Error) {
    super(message);
  }
}
```

## Testing Strategy

### Unit Tests

Each module has comprehensive unit tests:
- Provider implementations
- Tool implementations
- Configuration management
- Utility functions

### Integration Tests

Integration tests verify component interactions:
- AI Manager with providers
- Configuration loading and validation
- Tool execution with context

### End-to-End Tests

E2E tests verify complete workflows:
- CLI command execution
- Interactive mode scenarios
- Configuration management commands

### Test Structure

```
packages/*/src/**/*.test.ts    # Unit tests
packages/*/src/**/*.spec.ts    # Integration tests
integration-tests/             # E2E tests
```

## Performance Considerations

### Lazy Loading

Modules are loaded on demand:
- Providers are initialized when first used
- Tools are loaded when first accessed
- UI components are rendered as needed

### Caching

Strategic caching for performance:
- Model information caching
- Configuration caching
- Tool schema caching

### Memory Management

Careful memory management:
- Provider disposal on shutdown
- Tool cleanup after execution
- UI component unmounting

### Async Operations

Non-blocking operations:
- Streaming responses
- Concurrent provider initialization
- Background tool execution

## Security Considerations

### Sandbox Execution

Tools can run in sandboxed environments:
- Docker containers
- Podman containers
- Native OS sandboxing

### Input Validation

All inputs are validated:
- Configuration validation
- Tool parameter validation
- User input sanitization

### Credential Management

Secure credential handling:
- Environment variable support
- Encrypted storage options
- Token rotation support

## Extensibility

### Adding New Providers

1. Implement the `Provider` interface
2. Add provider type to `ProviderType` union
3. Update `ProviderFactory` to handle new type
4. Add configuration schema
5. Write tests

### Adding New Tools

1. Implement the `Tool` interface
2. Define tool schema
3. Register with `ToolRegistry`
4. Add documentation
5. Write tests

### Adding New UI Components

1. Create React component with Ink
2. Add to component library
3. Update theme system if needed
4. Add to storybook
5. Write tests

## Migration Guide

### From v1.x to v2.0

The refactored version includes breaking changes:

1. **Configuration Format**: New hierarchical configuration
2. **Provider API**: Unified provider interface
3. **Tool System**: New plugin-based architecture
4. **CLI Commands**: Restructured command system

Use the migration tool:

```bash
s647 migrate --from-version=1.x
```

## Future Roadmap

### Planned Features

- Local model support (Ollama, LocalAI)
- Plugin marketplace
- Web interface
- API server mode
- Multi-user support
- Advanced caching
- Performance monitoring
- Custom model fine-tuning

### Architecture Evolution

- Microservices architecture for server mode
- Event-driven architecture for real-time features
- GraphQL API for web interface
- WebSocket support for streaming
- Database integration for persistence

---

This architecture provides a solid foundation for the S647 AI assistant while maintaining flexibility for future enhancements and extensions.
