{"version": 3, "file": "ShellExecuteTool.js", "sourceRoot": "", "sources": ["../../../../src/tools/implementations/shell/ShellExecuteTool.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC;AAC3C,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAQxB,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAElD,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAElC,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC;IAClC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IACxD,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;IAC3F,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC;IACzF,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;IAC/E,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;IACtE,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;IACtF,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,sCAAsC,CAAC;CACvG,CAAC,CAAC;AAIH;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,QAAQ;IAC3B,iBAAiB,GAAG;QACnC,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,MAAM;QACN,QAAQ;QACR,SAAS;QACT,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;KACT,CAAC;IAEF;QACE,KAAK,CACH,eAAe,EACf,eAAe,EACf,0CAA0C,EAC1C,OAAO,EACP,OAAO,EACP,kBAAkB,CACnB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAClB,MAAkB,EAClB,OAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,EAAE,cAAc,EAAE,GACrF,MAAM,IAAI,CAAC,gBAAgB,CAAqB,MAAM,CAAC,CAAC;YAE1D,wCAAwC;YACxC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxD,OAAO,IAAI,CAAC,KAAK,CACf,4CAA4C,OAAO,KAAK;oBACxD,6CAA6C,CAC9C,CAAC;YACJ,CAAC;YAED,4BAA4B;YAC5B,MAAM,GAAG,GAAG,gBAAgB;gBAC1B,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,CAAC;gBAC9D,CAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YAE9C,gCAAgC;YAChC,MAAM,OAAO,GAAG;gBACd,GAAG,OAAO,CAAC,GAAG;gBACd,GAAG,GAAG;aACP,CAAC;YAEF,kBAAkB;YAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;gBAChD,GAAG;gBACH,OAAO;gBACP,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,CAAC;gBACvB,GAAG,EAAE,OAAO;aACb,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC;gBAChD,QAAQ,EAAE;oBACR,OAAO;oBACP,gBAAgB,EAAE,GAAG;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,KAAK,EAAE,KAAK,IAAI,IAAI;oBACpB,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM;iBAC1D;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,OAAO;YACL,WAAW,EAAE,iDAAiD;YAC9D,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,0BAA0B;oBACvC,QAAQ,EAAE,IAAI;iBACf;gBACD,gBAAgB,EAAE;oBAChB,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,yCAAyC;oBACtD,QAAQ,EAAE,KAAK;iBAChB;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,iCAAiC;oBAC9C,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,kDAAkD;oBAC/D,QAAQ,EAAE,KAAK;iBAChB;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,0CAA0C;oBACvD,QAAQ,EAAE,KAAK;iBAChB;gBACD,aAAa,EAAE;oBACb,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,mCAAmC;oBAChD,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,IAAI;iBACd;gBACD,cAAc,EAAE;oBACd,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,sCAAsC;oBACnD,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,KAAK;iBACf;aACF;YACD,QAAQ,EAAE;gBACR;oBACE,WAAW,EAAE,yBAAyB;oBACtC,UAAU,EAAE;wBACV,OAAO,EAAE,QAAQ;qBAClB;iBACF;gBACD;oBACE,WAAW,EAAE,uBAAuB;oBACpC,UAAU,EAAE;wBACV,OAAO,EAAE,gBAAgB;qBAC1B;iBACF;gBACD;oBACE,WAAW,EAAE,mCAAmC;oBAChD,UAAU,EAAE;wBACV,OAAO,EAAE,aAAa;wBACtB,gBAAgB,EAAE,cAAc;qBACjC;iBACF;gBACD;oBACE,WAAW,EAAE,uCAAuC;oBACpD,UAAU,EAAE;wBACV,OAAO,EAAE,cAAc;wBACvB,GAAG,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE;qBACjC;iBACF;gBACD;oBACE,WAAW,EAAE,2BAA2B;oBACxC,UAAU,EAAE;wBACV,OAAO,EAAE,eAAe;wBACxB,OAAO,EAAE,MAAM;qBAChB;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,IAAY,EAAE,gBAAyB;QACzD,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;QACD,OAAO,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAC7C,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAC/C,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,OAAe,EACf,OAKC;QAOD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE;gBAClD,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE,cAAc;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,MAAM,IAAI,EAAE;gBACpB,MAAM,EAAE,MAAM,IAAI,EAAE;gBACpB,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;gBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC3C,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;gBACzB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,YAAY,CAClB,OAAe,EACf,MAKC,EACD,GAAW;QAEX,MAAM,KAAK,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAE5C,KAAK,CAAC,IAAI,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;QAClC,KAAK,CAAC,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;QACxC,KAAK,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5C,KAAK,CAAC,IAAI,CAAC,mBAAmB,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;QACxD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEf,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC7B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC7B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACrC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF"}