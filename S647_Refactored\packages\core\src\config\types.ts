/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  ProviderConfig,
  ProviderType,
  Theme,
  LogLevel,
  Environment,
} from '@inkbytefo/s647-shared';

/**
 * Tool configuration
 */
export interface ToolConfig {
  enabled: boolean;
  timeout?: number;
  retries?: number;
  options?: Record<string, unknown>;
}

/**
 * File operation configuration
 */
export interface FileConfig extends ToolConfig {
  maxFileSize?: string;
  allowedExtensions?: string[];
  excludePatterns?: string[];
  includePatterns?: string[];
  encoding?: string;
}

/**
 * Git operation configuration
 */
export interface GitConfig extends ToolConfig {
  autoCommit?: boolean;
  commitMessage?: string;
  excludeBranches?: string[];
  maxDiffSize?: number;
}

/**
 * Web operation configuration
 */
export interface WebConfig extends ToolConfig {
  userAgent?: string;
  maxResponseSize?: number;
  followRedirects?: boolean;
  timeout?: number;
}

/**
 * Shell operation configuration
 */
export interface ShellConfig extends ToolConfig {
  allowedCommands?: string[];
  blockedCommands?: string[];
  workingDirectory?: string;
  environment?: Record<string, string>;
  timeout?: number;
}

/**
 * Memory configuration
 */
export interface MemoryConfig extends ToolConfig {
  maxEntries?: number;
  persistToDisk?: boolean;
  storageLocation?: string;
  compressionEnabled?: boolean;
}

/**
 * Tools configuration
 */
export interface ToolsConfig {
  enabled: string[];
  file?: FileConfig;
  git?: GitConfig;
  web?: WebConfig;
  shell?: ShellConfig;
  memory?: MemoryConfig;
}

/**
 * UI configuration
 */
export interface UIConfig {
  theme: Theme;
  animations: boolean;
  verbose: boolean;
  showTimestamps: boolean;
  showTokenCount: boolean;
  autoScroll: boolean;
  maxHistoryLines: number;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

/**
 * Logging configuration
 */
export interface LoggingConfig {
  level: LogLevel;
  format: 'text' | 'json';
  output: 'console' | 'file' | 'both';
  file?: {
    path: string;
    maxSize: string;
    maxFiles: number;
    compress: boolean;
  };
  structured: boolean;
  includeTimestamp: boolean;
  includeLevel: boolean;
  includeSource: boolean;
}

/**
 * Telemetry configuration
 */
export interface TelemetryConfig {
  enabled: boolean;
  endpoint?: string;
  apiKey?: string;
  collectUsage: boolean;
  collectErrors: boolean;
  collectPerformance: boolean;
  anonymize: boolean;
}

/**
 * Security configuration
 */
export interface SecurityConfig {
  sandbox: {
    enabled: boolean;
    type: 'docker' | 'podman' | 'native';
    image?: string;
    allowNetworking: boolean;
    allowFileSystem: boolean;
    allowedPaths?: string[];
    blockedPaths?: string[];
  };
  encryption: {
    enabled: boolean;
    algorithm: string;
    keyDerivation: string;
  };
  authentication: {
    required: boolean;
    methods: string[];
    tokenExpiry: number;
  };
}

/**
 * Performance configuration
 */
export interface PerformanceConfig {
  maxConcurrentRequests: number;
  requestTimeout: number;
  retryAttempts: number;
  retryDelay: number;
  caching: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
  rateLimit: {
    enabled: boolean;
    requestsPerMinute: number;
    burstLimit: number;
  };
}

/**
 * Main configuration interface
 */
export interface Configuration {
  // Metadata
  version: string;
  environment: Environment;
  profile?: string;
  
  // Core settings
  defaultProvider: string;
  providers: Record<string, ProviderConfig>;
  
  // Feature configurations
  tools: ToolsConfig;
  ui: UIConfig;
  logging: LoggingConfig;
  telemetry: TelemetryConfig;
  security: SecurityConfig;
  performance: PerformanceConfig;
  
  // Custom settings
  custom?: Record<string, unknown>;
}

/**
 * Configuration source information
 */
export interface ConfigurationSource {
  type: 'file' | 'environment' | 'cli' | 'default';
  path?: string;
  priority: number;
  timestamp: number;
}

/**
 * Configuration with metadata
 */
export interface ConfigurationWithMetadata {
  config: Configuration;
  sources: ConfigurationSource[];
  merged: boolean;
  validated: boolean;
  errors?: string[];
  warnings?: string[];
}

/**
 * Configuration validation result
 */
export interface ConfigurationValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions?: string[];
}

/**
 * Configuration migration result
 */
export interface ConfigurationMigrationResult {
  success: boolean;
  fromVersion: string;
  toVersion: string;
  changes: string[];
  errors?: string[];
  backup?: string;
}

/**
 * Configuration profile
 */
export interface ConfigurationProfile {
  name: string;
  description?: string;
  extends?: string;
  config: Partial<Configuration>;
  active: boolean;
  created: number;
  modified: number;
}
