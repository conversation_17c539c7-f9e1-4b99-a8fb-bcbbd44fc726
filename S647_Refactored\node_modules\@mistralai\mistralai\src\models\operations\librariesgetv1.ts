/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesGetV1Request = {
  libraryId: string;
};

/** @internal */
export const LibrariesGetV1Request$inboundSchema: z.ZodType<
  LibrariesGetV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
  });
});

/** @internal */
export type LibrariesGetV1Request$Outbound = {
  library_id: string;
};

/** @internal */
export const LibrariesGetV1Request$outboundSchema: z.ZodType<
  LibrariesGetV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesGetV1Request
> = z.object({
  libraryId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesGetV1Request$ {
  /** @deprecated use `LibrariesGetV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesGetV1Request$inboundSchema;
  /** @deprecated use `LibrariesGetV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesGetV1Request$outboundSchema;
  /** @deprecated use `LibrariesGetV1Request$Outbound` instead. */
  export type Outbound = LibrariesGetV1Request$Outbound;
}

export function librariesGetV1RequestToJSON(
  librariesGetV1Request: LibrariesGetV1Request,
): string {
  return JSON.stringify(
    LibrariesGetV1Request$outboundSchema.parse(librariesGetV1Request),
  );
}

export function librariesGetV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesGetV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesGetV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesGetV1Request' from JSON`,
  );
}
