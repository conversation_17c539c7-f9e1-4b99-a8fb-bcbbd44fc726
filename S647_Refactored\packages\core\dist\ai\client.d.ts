/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, AsyncResult, Logger } from '@inkbytefo/s647-shared';
import { AIManager } from './manager.js';
/**
 * AI Client - High-level interface for AI operations
 */
export declare class AIClient {
    private logger?;
    private aiManager;
    constructor(aiManager: AIManager, logger?: Logger | undefined);
    /**
     * Create a chat completion
     */
    chat(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    chatStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings
     */
    embed(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens
     */
    countTokens(input: string, modelId?: string): AsyncResult<number>;
}
//# sourceMappingURL=client.d.ts.map