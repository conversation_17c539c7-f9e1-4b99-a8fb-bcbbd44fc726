/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const TimestampGranularity = {
  Segment: "segment",
} as const;
export type TimestampGranularity = ClosedEnum<typeof TimestampGranularity>;

/** @internal */
export const TimestampGranularity$inboundSchema: z.ZodNativeEnum<
  typeof TimestampGranularity
> = z.nativeEnum(TimestampGranularity);

/** @internal */
export const TimestampGranularity$outboundSchema: z.ZodNativeEnum<
  typeof TimestampGranularity
> = TimestampGranularity$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TimestampGranularity$ {
  /** @deprecated use `TimestampGranularity$inboundSchema` instead. */
  export const inboundSchema = TimestampGranularity$inboundSchema;
  /** @deprecated use `TimestampGranularity$outboundSchema` instead. */
  export const outboundSchema = TimestampGranularity$outboundSchema;
}
