/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { OpenAIProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * OpenAI provider implementation
 */
export declare class OpenAIProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: OpenAIProviderConfig);
    get openaiConfig(): OpenAIProviderConfig;
    /**
     * Initialize the OpenAI provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings
     */
    createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
    /**
     * Get context length for a model
     */
    private getContextLength;
    /**
     * Get max tokens for a model
     */
    private getMaxTokens;
}
//# sourceMappingURL=openai.d.ts.map