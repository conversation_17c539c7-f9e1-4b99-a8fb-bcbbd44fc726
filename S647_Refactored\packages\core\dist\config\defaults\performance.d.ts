/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default performance configuration
 */
export declare const DEFAULT_PERFORMANCE: {
    maxConcurrentRequests: number;
    requestTimeout: number;
    retryDelay: number;
    maxRetries: number;
    caching: {
        enabled: boolean;
        maxSize: number;
        ttl: number;
        checkPeriod: number;
    };
    memory: {
        maxHeapSize: number;
        gcThreshold: number;
        monitoringEnabled: boolean;
    };
    networking: {
        keepAlive: boolean;
        maxSockets: number;
        timeout: number;
        retryOnTimeout: boolean;
    };
    fileSystem: {
        maxFileSize: number;
        maxConcurrentReads: number;
        bufferSize: number;
    };
};
/**
 * Performance configurations for different environments
 */
export declare const PERFORMANCE_CONFIGS: {
    development: {
        maxConcurrentRequests: number;
        requestTimeout: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
            checkPeriod: number;
        };
        memory: {
            maxHeapSize: number;
            monitoringEnabled: boolean;
            gcThreshold: number;
        };
        retryDelay: number;
        maxRetries: number;
        networking: {
            keepAlive: boolean;
            maxSockets: number;
            timeout: number;
            retryOnTimeout: boolean;
        };
        fileSystem: {
            maxFileSize: number;
            maxConcurrentReads: number;
            bufferSize: number;
        };
    };
    production: {
        maxConcurrentRequests: number;
        requestTimeout: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
            checkPeriod: number;
        };
        memory: {
            maxHeapSize: number;
            monitoringEnabled: boolean;
            gcThreshold: number;
        };
        retryDelay: number;
        maxRetries: number;
        networking: {
            keepAlive: boolean;
            maxSockets: number;
            timeout: number;
            retryOnTimeout: boolean;
        };
        fileSystem: {
            maxFileSize: number;
            maxConcurrentReads: number;
            bufferSize: number;
        };
    };
    test: {
        maxConcurrentRequests: number;
        requestTimeout: number;
        caching: {
            enabled: boolean;
            maxSize: number;
            ttl: number;
            checkPeriod: number;
        };
        memory: {
            maxHeapSize: number;
            monitoringEnabled: boolean;
            gcThreshold: number;
        };
        retryDelay: number;
        maxRetries: number;
        networking: {
            keepAlive: boolean;
            maxSockets: number;
            timeout: number;
            retryOnTimeout: boolean;
        };
        fileSystem: {
            maxFileSize: number;
            maxConcurrentReads: number;
            bufferSize: number;
        };
    };
};
/**
 * Get performance configuration for environment
 */
export declare function getPerformanceConfigForEnvironment(env: 'development' | 'production' | 'test'): typeof DEFAULT_PERFORMANCE;
/**
 * Calculate optimal concurrency based on system resources
 */
export declare function calculateOptimalConcurrency(): number;
/**
 * Memory usage monitoring utilities
 */
export declare function getMemoryUsage(): {
    used: number;
    total: number;
    percentage: number;
};
/**
 * Check if memory usage is above threshold
 */
export declare function isMemoryUsageHigh(threshold?: number): boolean;
//# sourceMappingURL=performance.d.ts.map