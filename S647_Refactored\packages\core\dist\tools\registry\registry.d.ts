/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Tool, ToolId } from '@inkbytefo/s647-shared';
/**
 * Tool registry implementation
 */
export declare class ToolRegistry {
    private tools;
    register(tool: Tool): void;
    unregister(id: ToolId): void;
    get(id: ToolId): Tool | undefined;
    getAll(): Tool[];
    getByCategory(category: string): Tool[];
    search(query: string): Tool[];
    clear(): void;
}
//# sourceMappingURL=registry.d.ts.map