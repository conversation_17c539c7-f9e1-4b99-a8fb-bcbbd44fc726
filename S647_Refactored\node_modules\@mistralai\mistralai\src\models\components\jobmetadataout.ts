/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobMetadataOut = {
  expectedDurationSeconds?: number | null | undefined;
  cost?: number | null | undefined;
  costCurrency?: string | null | undefined;
  trainTokensPerStep?: number | null | undefined;
  trainTokens?: number | null | undefined;
  dataTokens?: number | null | undefined;
  estimatedStartTime?: number | null | undefined;
};

/** @internal */
export const JobMetadataOut$inboundSchema: z.ZodType<
  JobMetadataOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  expected_duration_seconds: z.nullable(z.number().int()).optional(),
  cost: z.nullable(z.number()).optional(),
  cost_currency: z.nullable(z.string()).optional(),
  train_tokens_per_step: z.nullable(z.number().int()).optional(),
  train_tokens: z.nullable(z.number().int()).optional(),
  data_tokens: z.nullable(z.number().int()).optional(),
  estimated_start_time: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    "expected_duration_seconds": "expectedDurationSeconds",
    "cost_currency": "costCurrency",
    "train_tokens_per_step": "trainTokensPerStep",
    "train_tokens": "trainTokens",
    "data_tokens": "dataTokens",
    "estimated_start_time": "estimatedStartTime",
  });
});

/** @internal */
export type JobMetadataOut$Outbound = {
  expected_duration_seconds?: number | null | undefined;
  cost?: number | null | undefined;
  cost_currency?: string | null | undefined;
  train_tokens_per_step?: number | null | undefined;
  train_tokens?: number | null | undefined;
  data_tokens?: number | null | undefined;
  estimated_start_time?: number | null | undefined;
};

/** @internal */
export const JobMetadataOut$outboundSchema: z.ZodType<
  JobMetadataOut$Outbound,
  z.ZodTypeDef,
  JobMetadataOut
> = z.object({
  expectedDurationSeconds: z.nullable(z.number().int()).optional(),
  cost: z.nullable(z.number()).optional(),
  costCurrency: z.nullable(z.string()).optional(),
  trainTokensPerStep: z.nullable(z.number().int()).optional(),
  trainTokens: z.nullable(z.number().int()).optional(),
  dataTokens: z.nullable(z.number().int()).optional(),
  estimatedStartTime: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    expectedDurationSeconds: "expected_duration_seconds",
    costCurrency: "cost_currency",
    trainTokensPerStep: "train_tokens_per_step",
    trainTokens: "train_tokens",
    dataTokens: "data_tokens",
    estimatedStartTime: "estimated_start_time",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobMetadataOut$ {
  /** @deprecated use `JobMetadataOut$inboundSchema` instead. */
  export const inboundSchema = JobMetadataOut$inboundSchema;
  /** @deprecated use `JobMetadataOut$outboundSchema` instead. */
  export const outboundSchema = JobMetadataOut$outboundSchema;
  /** @deprecated use `JobMetadataOut$Outbound` instead. */
  export type Outbound = JobMetadataOut$Outbound;
}

export function jobMetadataOutToJSON(jobMetadataOut: JobMetadataOut): string {
  return JSON.stringify(JobMetadataOut$outboundSchema.parse(jobMetadataOut));
}

export function jobMetadataOutFromJSON(
  jsonString: string,
): SafeParseResult<JobMetadataOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => JobMetadataOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'JobMetadataOut' from JSON`,
  );
}
