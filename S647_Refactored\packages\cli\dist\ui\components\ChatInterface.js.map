{"version": 3, "file": "ChatInterface.js", "sourceRoot": "", "sources": ["../../../src/ui/components/ChatInterface.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACzE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,KAAK,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAoB,MAAM,0BAA0B,CAAC;AACvG,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAC1G,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAYtD;;GAEG;AACH,SAAS,eAAe;IACtB,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;IAC/B,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC;QAC/B,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;QACvB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;KAC9B,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,OAAO,CAAC;gBACN,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;gBACvB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAChC,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAChD,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAiC,CAAC,EAC1D,MAAM,EACN,MAAM,EACN,MAAM,GACP,EAAE,EAAE;IACH,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,eAAe,EAAE,CAAC;IAC3E,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,QAAQ,EAAE,CAAC;IAEzC,QAAQ;IACR,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9D,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IAC/E,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;IAErD,QAAQ;IACR,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3C,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/C,MAAM,eAAe,GAAG,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3D,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,MAAM,QAAQ,GAAG,WAAW,CAC1B,MAAM,EACN,MAAM,EACN,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,YAAY,EACpB,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EACrB,eAAe,EACf,kBAAkB,CACnB,CAAC;IAEF,uBAAuB;IACvB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACrE,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC;IACvE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC,qCAAqC;IAE3F,wBAAwB;IACxB,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,QAAgB,EAAW,EAAE;QAC5D,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,MAAM,MAAM,GAAG,aAAa,CAAC;QAC3B,WAAW,EAAE,EAAE;QACf,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE;QAC1C,KAAK;QACL,UAAU;QACV,WAAW;QACX,eAAe;KAChB,CAAC,CAAC;IAEH,4BAA4B;IAC5B,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,EAAE,OAAe,EAAE,EAAE;QACzD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAAE,OAAO;QAE5B,IAAI,CAAC;YACH,0BAA0B;YAC1B,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACtD,IAAI,MAAM,EAAE,CAAC;oBACX,wDAAwD;oBACxD,OAAO;gBACT,CAAC;YACH,CAAC;YAED,0BAA0B;YAC1B,MAAM,gBAAgB,GAAG,MAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAE9E,gBAAgB;YAChB,MAAM,MAAM,CAAC,SAAS,CACpB,gBAAgB,EAChB,eAAe,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,EACzC,MAAM,CACP,CAAC;YAEF,oCAAoC;YACpC,MAAM,gBAAgB,GAAG,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAExE,sBAAsB;YACtB,MAAM,SAAS,CAAC,WAAW,CACzB,gBAAgB,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,CAAC,EAAU,EAAE,OAAe,EAAE,EAAE;gBAC9B,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YACtC,CAAC,EACD,eAAe,CAChB,CAAC;YAEF,0CAA0C;YAC1C,IAAI,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,QAAQ,EAAE,CAAC;gBACzD,MAAM,MAAM,CAAC,SAAS,CACpB,SAAS,CAAC,eAAe,EACzB,gBAAgB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,EAC1C,IAAI,CACL,CAAC;YACJ,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,YAAY,GAAgB;gBAChC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gBACzB,IAAI,EAAE,WAAW,CAAC,KAAK;gBACvB,OAAO,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC3E,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YACF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;IAErF,sBAAsB;IACtB,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;QACzC,OAAO,CAAC,YAAY,EAAE,CAAC;QACvB,eAAe,CAAC,gBAAgB,CAAC,CAAC;IACpC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,iCAAiC;IACjC,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,yBAAyB;QACzB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,MAAM,EAAE,CAAC;YACT,OAAO;QACT,CAAC;QAED,8CAA8C;QAC9C,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC3D,MAAM,EAAE,CAAC;YACT,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;YACX,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;YACvB,OAAO;QACT,CAAC;QAED,iCAAiC;QACjC,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;YACX,kBAAkB,CAAC,CAAC,eAAe,CAAC,CAAC;YACrC,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;YAC3E,OAAO;QACT,CAAC;QAED,+BAA+B;QAC/B,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;YACX,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CACpD,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,CACxC,CAAC;YACF,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;YACxD,kBAAkB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;YACzC,eAAe,CAAC,yBAAyB,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACjE,OAAO;QACT,CAAC;QAED,oCAAoC;QACpC,IAAI,GAAG,CAAC,MAAM,IAAI,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,SAAS,EAAE,CAAC;YACxE,SAAS,CAAC,YAAY,EAAE,CAAC;YACzB,eAAe,CAAC,qBAAqB,CAAC,CAAC;YACvC,OAAO;QACT,CAAC;IACH,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IAEvB,2CAA2C;IAC3C,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;QAChC,OAAO,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACzC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;QAE1D,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAClC,KAAC,GAAG,IAAe,YAAY,EAAE,CAAC,YAChC,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aAEzB,MAAC,GAAG,eACF,KAAC,IAAI,IAAC,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,kBAC1C,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,GACvB,EACP,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,kBACxB,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,GAC/B,GACH,EACL,IAAI,CAAC,QAAQ,IAAI,CAChB,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,2BACpB,IAAI,CAAC,QAAQ,IACb,GACH,CACP,EACA,IAAI,CAAC,WAAW,IAAI,CACnB,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,oCAEb,GACH,CACP,IACG,EAGN,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YAC9B,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,YAChB,IAAI,CAAC,OAAO,GACR,GACH,IACF,IAlCE,IAAI,CAAC,EAAE,CAmCX,CACP,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,oBAAoB;IACpB,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,EAAE,CAAC;QAElD,OAAO,CACL,MAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,aACrD,MAAC,GAAG,IAAC,cAAc,EAAC,eAAe,EAAC,KAAK,EAAC,MAAM,aAC9C,KAAC,GAAG,cACF,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,2BACL,eAAe,mBACf,OAAO,CAAC,OAAO,CAAC,MAAM,iBACxB,WAAW,CAAC,YAAY,gBACzB,SAAS,CAAC,SAAS,IACtB,GACH,EACN,KAAC,GAAG,cACF,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,aACf,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,SAAS,IAAI,cAAc,EACvE,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,QAAQ,IAAI,SAAS,EACjE,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,KAAK,IAAI,SAAS,EAC9D,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,IAAI,IAAI,SAAS,IACzD,GACH,IACF,EAEL,YAAY,IAAI,CACf,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,MAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,QAAQ,8BACnB,YAAY,IACf,GACH,CACP,IACG,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,oBAAoB;IACpB,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAE3B,OAAO,CACL,KAAC,GAAG,IACF,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,QAAQ,EACpB,OAAO,EAAE,CAAC,EACV,YAAY,EAAE,CAAC,YAEf,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,oDAElB,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,4CAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,6CAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,8CAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,gDAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,4CAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,gDAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,sDAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,sDAEZ,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,6CAEZ,IACH,GACF,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aAEvC,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,YACtE,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,YACvC,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,uEAEhB,GACH,GACF,EAGL,UAAU,EAAE,EAGb,KAAC,GAAG,IACF,QAAQ,EAAE,CAAC,EACX,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,MAAM,EAClB,OAAO,EAAE,CAAC,EACV,YAAY,EAAE,CAAC,EACf,aAAa,EAAC,QAAQ,YAErB,OAAO,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAC9B,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,YAC5D,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,aAC7C,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,+DAEhB,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,4DAEX,EACP,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,yCACN,eAAe,IAC7B,IACH,GACF,CACP,CAAC,CAAC,CAAC,CACF,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACxB,kBAAkB,EAAE,GACjB,CACP,GACG,EAGN,KAAC,WAAW,IACV,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,YAAY,EACtB,YAAY,EAAE,YAAY,EAC1B,aAAa,EAAE,iBAAiB,EAChC,MAAM,EAAE,MAAM,EACd,WAAW,EAAC,+DAAqD,EACjE,KAAK,EAAE,IAAI,EACX,UAAU,EAAE,UAAU,EACtB,gBAAgB,EAAE,gBAAgB,EAClC,eAAe,EAAE,eAAe,EAChC,kBAAkB,EAAE,kBAAkB,EACtC,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB,GACrD,EAGD,eAAe,EAAE,IACd,CACP,CAAC;AACJ,CAAC,CAAC"}