/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default telemetry configuration
 */
export declare const DEFAULT_TELEMETRY: {
    enabled: boolean;
    collectUsage: boolean;
    collectErrors: boolean;
    collectPerformance: boolean;
    anonymize: boolean;
    endpoint: undefined;
    apiKey: undefined;
    batchSize: number;
    flushInterval: number;
    maxRetries: number;
    retryDelay: number;
};
/**
 * Telemetry configurations for different environments
 */
export declare const TELEMETRY_CONFIGS: {
    development: {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        endpoint: undefined;
        apiKey: undefined;
        batchSize: number;
        flushInterval: number;
        maxRetries: number;
        retryDelay: number;
    };
    production: {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        endpoint: undefined;
        apiKey: undefined;
        batchSize: number;
        flushInterval: number;
        maxRetries: number;
        retryDelay: number;
    };
    test: {
        enabled: boolean;
        collectUsage: boolean;
        collectErrors: boolean;
        collectPerformance: boolean;
        anonymize: boolean;
        endpoint: undefined;
        apiKey: undefined;
        batchSize: number;
        flushInterval: number;
        maxRetries: number;
        retryDelay: number;
    };
};
/**
 * Get telemetry configuration for environment
 */
export declare function getTelemetryConfigForEnvironment(env: 'development' | 'production' | 'test'): typeof DEFAULT_TELEMETRY;
/**
 * Privacy-safe telemetry data types
 */
export declare const SAFE_TELEMETRY_EVENTS: readonly ["app_start", "app_stop", "command_executed", "provider_used", "tool_used", "error_occurred", "performance_metric"];
/**
 * Check if telemetry event is safe to collect
 */
export declare function isSafeTelemetryEvent(event: string): boolean;
//# sourceMappingURL=telemetry.d.ts.map