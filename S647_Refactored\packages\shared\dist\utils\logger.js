/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Log level priorities
 */
const LOG_LEVELS = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
};
/**
 * Console logger implementation
 */
export class ConsoleLogger {
    config;
    constructor(config) {
        this.config = {
            includeTimestamp: true,
            includeLevel: true,
            includeSource: false,
            ...config,
        };
    }
    debug(message, ...args) {
        this.log('debug', message, ...args);
    }
    info(message, ...args) {
        this.log('info', message, ...args);
    }
    warn(message, ...args) {
        this.log('warn', message, ...args);
    }
    error(message, ...args) {
        this.log('error', message, ...args);
    }
    log(level, message, ...args) {
        // Check if this level should be logged
        if (LOG_LEVELS[level] < LOG_LEVELS[this.config.level]) {
            return;
        }
        const logEntry = this.formatMessage(level, message, ...args);
        // Output to appropriate destination
        switch (level) {
            case 'error':
                console.error(logEntry);
                break;
            case 'warn':
                console.warn(logEntry);
                break;
            case 'debug':
                console.debug(logEntry);
                break;
            default:
                console.log(logEntry);
                break;
        }
    }
    formatMessage(level, message, ...args) {
        const parts = [];
        // Add timestamp
        if (this.config.includeTimestamp) {
            parts.push(`[${new Date().toISOString()}]`);
        }
        // Add level
        if (this.config.includeLevel) {
            const levelStr = level.toUpperCase().padEnd(5);
            parts.push(`[${levelStr}]`);
        }
        // Add source (if available)
        if (this.config.includeSource) {
            const stack = new Error().stack;
            if (stack) {
                const caller = stack.split('\n')[3]; // Get the caller line
                if (caller) {
                    const match = caller.match(/at\s+(.+)\s+\((.+):(\d+):(\d+)\)/);
                    if (match) {
                        const [, , file, line] = match;
                        const fileName = file?.split('/').pop() || 'unknown';
                        parts.push(`[${fileName}:${line}]`);
                    }
                }
            }
        }
        // Add message
        parts.push(message);
        // Add additional arguments
        if (args.length > 0) {
            const formattedArgs = args.map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                }
                return String(arg);
            }).join(' ');
            parts.push(formattedArgs);
        }
        return parts.join(' ');
    }
}
/**
 * Create a logger instance
 */
export function createLogger(config) {
    return new ConsoleLogger(config);
}
/**
 * Default logger instance
 */
export const defaultLogger = createLogger({
    level: 'info',
    format: 'text',
    output: 'console',
});
/**
 * Get log level color for console output
 */
export function getLogLevelColor(level) {
    switch (level) {
        case 'debug': return '\x1b[36m'; // Cyan
        case 'info': return '\x1b[32m'; // Green
        case 'warn': return '\x1b[33m'; // Yellow
        case 'error': return '\x1b[31m'; // Red
        default: return '\x1b[0m'; // Reset
    }
}
/**
 * Reset color code
 */
export const RESET_COLOR = '\x1b[0m';
/**
 * Colored console logger
 */
export class ColoredConsoleLogger extends ConsoleLogger {
    log(level, message, ...args) {
        // Check if this level should be logged
        if (LOG_LEVELS[level] < LOG_LEVELS[this.config.level]) {
            return;
        }
        const color = getLogLevelColor(level);
        const logEntry = this.formatMessage(level, message, ...args);
        const coloredEntry = `${color}${logEntry}${RESET_COLOR}`;
        // Output to appropriate destination
        switch (level) {
            case 'error':
                console.error(coloredEntry);
                break;
            case 'warn':
                console.warn(coloredEntry);
                break;
            case 'debug':
                console.debug(coloredEntry);
                break;
            default:
                console.log(coloredEntry);
                break;
        }
    }
}
/**
 * Create a colored logger instance
 */
export function createColoredLogger(config) {
    return new ColoredConsoleLogger(config);
}
//# sourceMappingURL=logger.js.map