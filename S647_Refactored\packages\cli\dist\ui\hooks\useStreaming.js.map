{"version": 3, "file": "useStreaming.js", "sourceRoot": "", "sources": ["../../../src/ui/hooks/useStreaming.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAGjE;;GAEG;AACH,MAAM,CAAN,IAAY,cAOX;AAPD,WAAY,cAAc;IACxB,+BAAa,CAAA;IACb,2CAAyB,CAAA;IACzB,yCAAuB,CAAA;IACvB,uCAAqB,CAAA;IACrB,iCAAe,CAAA;IACf,6CAA2B,CAAA;AAC7B,CAAC,EAPW,cAAc,KAAd,cAAc,QAOzB;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;IACjB,4BAAa,CAAA;IACb,8BAAe,CAAA;AACjB,CAAC,EANW,WAAW,KAAX,WAAW,QAMtB;AAuCD;;GAEG;AACH,MAAM,cAAc;IAER;IACA;IACA;IACA;IAJV,YACU,MAAc,EACd,OAAe,EACf,KAAa,EACb,MAAc;QAHd,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;IACrB,CAAC;IAEJ,KAAK,CAAC,MAAM,CACV,QAAuB,EACvB,OAAgC,EAChC,UAAiD,EACjD,OAA+B,EAC/B,MAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAAE;gBAC/D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;iBACzC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW;wBAC1D,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;oBACH,MAAM,EAAE,IAAI;oBACZ,WAAW,EAAE,GAAG;iBACjB,CAAC;gBACF,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,WAAW,GAAG,EAAE,CAAC;YAErB,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5C,IAAI,IAAI;oBAAE,MAAM;gBAEhB,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC3B,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;4BACtB,UAAU,CAAC;gCACT,OAAO,EAAE,WAAW;gCACpB,UAAU,EAAE,IAAI;gCAChB,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;6BACpD,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBAED,IAAI,CAAC;4BACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAChC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC;4BAClD,IAAI,KAAK,EAAE,CAAC;gCACV,WAAW,IAAI,KAAK,CAAC;gCACrB,OAAO,CAAC,KAAK,CAAC,CAAC;4BACjB,CAAC;wBACH,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,6CAA6C;wBAC/C,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,iBAAiB;IAEX;IACA;IACA;IACA;IAJV,YACU,MAAc,EACd,OAAe,EACf,KAAa,EACb,MAAc;QAHd,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;IACrB,CAAC;IAEJ,KAAK,CAAC,MAAM,CACV,QAAuB,EACvB,OAAgC,EAChC,UAAiD,EACjD,OAA+B,EAC/B,MAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,cAAc,EAAE;gBAC1D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,WAAW,EAAE,IAAI,CAAC,MAAM;oBACxB,mBAAmB,EAAE,YAAY;iBAClC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW;wBAC1D,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;oBACH,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBACjB,CAAC;gBACF,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACpF,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,WAAW,GAAG,EAAE,CAAC;YAErB,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5C,IAAI,IAAI;oBAAE,MAAM;gBAEhB,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAE3B,IAAI,CAAC;4BACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAEhC,IAAI,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;gCAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC;gCACjC,IAAI,KAAK,EAAE,CAAC;oCACV,WAAW,IAAI,KAAK,CAAC;oCACrB,OAAO,CAAC,KAAK,CAAC,CAAC;gCACjB,CAAC;4BACH,CAAC;iCAAM,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gCAC1C,UAAU,CAAC;oCACT,OAAO,EAAE,WAAW;oCACpB,UAAU,EAAE,IAAI;oCAChB,QAAQ,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;iCACvD,CAAC,CAAC;gCACH,OAAO;4BACT,CAAC;wBACH,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,6CAA6C;wBAC/C,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,cAAc;IAER;IACA;IACA;IACA;IAJV,YACU,MAAc,EACd,OAAe,EACf,KAAa,EACb,MAAc;QAHd,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;IACrB,CAAC;IAEJ,KAAK,CAAC,MAAM,CACV,QAAuB,EACvB,OAAgC,EAChC,UAAiD,EACjD,OAA+B,EAC/B,MAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;gBACtD,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC;aAC/B,CAAC,CAAC,CAAC;YAEJ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,GAAG,IAAI,CAAC,OAAO,kBAAkB,IAAI,CAAC,KAAK,8BAA8B,IAAI,CAAC,MAAM,EAAE,EACtF;gBACE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,QAAQ;oBACR,gBAAgB,EAAE;wBAChB,WAAW,EAAE,GAAG;wBAChB,eAAe,EAAE,IAAI;qBACtB;iBACF,CAAC;gBACF,MAAM;aACP,CACF,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,WAAW,GAAG,EAAE,CAAC;YAErB,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5C,IAAI,IAAI;oBAAE,MAAM;gBAEhB,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;wBAChB,IAAI,CAAC;4BACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAChC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;4BAE/D,IAAI,IAAI,EAAE,CAAC;gCACT,WAAW,IAAI,IAAI,CAAC;gCACpB,OAAO,CAAC,IAAI,CAAC,CAAC;4BAChB,CAAC;4BAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC;gCACzC,UAAU,CAAC;oCACT,OAAO,EAAE,WAAW;oCACpB,UAAU,EAAE,IAAI;oCAChB,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;iCACpD,CAAC,CAAC;gCACH,OAAO;4BACT,CAAC;wBACH,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,6CAA6C;wBAC/C,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,MAAqB,EAAE,MAAc;IAChE,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAAiB,cAAc,CAAC,IAAI,CAAC,CAAC;IAC1F,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAS,EAAE,CAAC,CAAC;IACnE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAe,IAAI,CAAC,CAAC;IAEvD,MAAM,kBAAkB,GAAG,MAAM,CAAyB,IAAI,CAAC,CAAC;IAChE,MAAM,YAAY,GAAG,MAAM,CAAgC,IAAI,GAAG,EAAE,CAAC,CAAC;IAEtE,uBAAuB;IACvB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,SAAS,GAAG,IAAI,GAAG,EAA4B,CAAC;QAEtD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE,EAAE;YAClE,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;gBAAE,OAAO;YAE9D,QAAQ,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC5B,KAAK,QAAQ;oBACX,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,cAAc,CACpC,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,IAAI,2BAA2B,EACrD,cAAc,CAAC,KAAK,IAAI,OAAO,EAC/B,MAAM,CACP,CAAC,CAAC;oBACH,MAAM;gBAER,KAAK,WAAW;oBACd,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,iBAAiB,CACvC,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,IAAI,2BAA2B,EACrD,cAAc,CAAC,KAAK,IAAI,0BAA0B,EAClD,MAAM,CACP,CAAC,CAAC;oBACH,MAAM;gBAER,KAAK,QAAQ;oBACX,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,cAAc,CACpC,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,IAAI,2CAA2C,EACrE,cAAc,CAAC,KAAK,IAAI,YAAY,EACpC,MAAM,CACP,CAAC,CAAC;oBACH,MAAM;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;IACnC,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAE/B,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,EACnC,KAAa,EACb,OAAsB,EACtB,YAA4C,EAC5C,eAAsD,EACtD,QAAiB,EACjB,EAAE;QACF,6BAA6B;QAC7B,IAAI,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAC/B,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrC,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,kBAAkB,CAAC,OAAO,GAAG,eAAe,CAAC;QAE7C,mBAAmB;QACnB,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;YACxB,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,YAAY,CAAC,WAAW,CAAC,CAAC;QAE1B,uCAAuC;QACvC,MAAM,gBAAgB,GAAgB;YACpC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;YAC7B,IAAI,EAAE,WAAW,CAAC,SAAS;YAC3B,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAE/B,IAAI,CAAC;YACH,iBAAiB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC7C,kBAAkB,CAAC,EAAE,CAAC,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEf,MAAM,YAAY,GAAG,QAAQ,IAAI,MAAM,CAAC,eAAe,CAAC;YACxD,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAExD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,iBAAiB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAE5C,MAAM,QAAQ,CAAC,MAAM,CACnB,CAAC,GAAG,OAAO,EAAE,WAAW,CAAC,EACzB,CAAC,KAAa,EAAE,EAAE;gBAChB,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBACzC,eAAe,CAAC,gBAAgB,CAAC,EAAE,EAAE,eAAe,GAAG,KAAK,CAAC,CAAC;YAChE,CAAC,EACD,CAAC,QAA2B,EAAE,EAAE;gBAC9B,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC3C,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrC,eAAe,CAAC,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAEvD,2BAA2B;gBAC3B,MAAM,eAAe,GAAgB;oBACnC,GAAG,gBAAgB;oBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,WAAW,EAAE,KAAK;oBAClB,UAAU,EAAE,IAAI;oBAChB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ;oBACrC,KAAK,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK;oBAC/B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC;gBAEF,YAAY,CAAC,eAAe,CAAC,CAAC;YAChC,CAAC,EACD,CAAC,WAAkB,EAAE,EAAE;gBACrB,iBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACxC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACtB,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;gBAE9C,MAAM,YAAY,GAAgB;oBAChC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;oBACzB,IAAI,EAAE,WAAW,CAAC,KAAK;oBACvB,OAAO,EAAE,UAAU,WAAW,CAAC,OAAO,EAAE;oBACxC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,YAAY,CAAC,YAAY,CAAC,CAAC;YAC7B,CAAC,EACD,eAAe,CAAC,MAAM,CACvB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,QAAQ,CAAC,GAAG,CAAC,CAAC;YACd,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;IAEtD,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,IAAI,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAC/B,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACnC,iBAAiB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,cAAc;QACd,eAAe;QACf,KAAK;QACL,WAAW;QACX,YAAY;KACb,CAAC;AACJ,CAAC"}