/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import {
  catchUnrecognizedEnum,
  OpenEnum,
  Unrecognized,
} from "../../types/enums.js";

export const Source = {
  Upload: "upload",
  Repository: "repository",
  Mistral: "mistral",
} as const;
export type Source = OpenEnum<typeof Source>;

/** @internal */
export const Source$inboundSchema: z.ZodType<Source, z.ZodTypeDef, unknown> = z
  .union([
    z.nativeEnum(Source),
    z.string().transform(catchUnrecognizedEnum),
  ]);

/** @internal */
export const Source$outboundSchema: z.ZodType<Source, z.ZodTypeDef, Source> = z
  .union([
    z.nativeEnum(Source),
    z.string().and(z.custom<Unrecognized<string>>()),
  ]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Source$ {
  /** @deprecated use `Source$inboundSchema` instead. */
  export const inboundSchema = Source$inboundSchema;
  /** @deprecated use `Source$outboundSchema` instead. */
  export const outboundSchema = Source$outboundSchema;
}
