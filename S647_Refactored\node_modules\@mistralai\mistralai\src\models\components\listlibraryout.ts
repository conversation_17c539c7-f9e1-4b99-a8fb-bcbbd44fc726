/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  LibraryOut,
  LibraryOut$inboundSchema,
  LibraryOut$Outbound,
  LibraryOut$outboundSchema,
} from "./libraryout.js";

export type ListLibraryOut = {
  data: Array<LibraryOut>;
};

/** @internal */
export const ListLibraryOut$inboundSchema: z.ZodType<
  ListLibraryOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  data: z.array(LibraryOut$inboundSchema),
});

/** @internal */
export type ListLibraryOut$Outbound = {
  data: Array<LibraryOut$Outbound>;
};

/** @internal */
export const ListLibraryOut$outboundSchema: z.ZodType<
  ListLibraryOut$Outbound,
  z.ZodTypeDef,
  ListLibraryOut
> = z.object({
  data: z.array(LibraryOut$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListLibraryOut$ {
  /** @deprecated use `ListLibraryOut$inboundSchema` instead. */
  export const inboundSchema = ListLibraryOut$inboundSchema;
  /** @deprecated use `ListLibraryOut$outboundSchema` instead. */
  export const outboundSchema = ListLibraryOut$outboundSchema;
  /** @deprecated use `ListLibraryOut$Outbound` instead. */
  export type Outbound = ListLibraryOut$Outbound;
}

export function listLibraryOutToJSON(listLibraryOut: ListLibraryOut): string {
  return JSON.stringify(ListLibraryOut$outboundSchema.parse(listLibraryOut));
}

export function listLibraryOutFromJSON(
  jsonString: string,
): SafeParseResult<ListLibraryOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListLibraryOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListLibraryOut' from JSON`,
  );
}
