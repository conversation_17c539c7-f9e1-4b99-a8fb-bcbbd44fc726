/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Box, Text, useInput, useStdin, useStdout } from 'ink';
import { useTextBuffer } from './shared/text-buffer.js';
import { InputPrompt } from './InputPrompt.js';
import { useStreaming, StreamingState, MessageType, type HistoryItem } from '../hooks/useStreaming.js';
import { useHistory, formatHistoryItem, getMessageColor, getMessagePrefix } from '../hooks/useHistory.js';
import { useFileIntegration } from '../hooks/useFileIntegration.js';
import { useMemory } from '../hooks/useMemory.js';
import { useCommands } from '../hooks/useCommands.js';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';

/**
 * Chat interface props
 */
export interface ChatInterfaceProps {
  config: Configuration;
  logger: Logger;
  onExit: () => void;
}

/**
 * Terminal size hook
 */
function useTerminalSize() {
  const { stdout } = useStdout();
  const [size, setSize] = useState({
    rows: stdout.rows || 24,
    columns: stdout.columns || 80,
  });

  useEffect(() => {
    const updateSize = () => {
      setSize({
        rows: stdout.rows || 24,
        columns: stdout.columns || 80,
      });
    };

    stdout.on('resize', updateSize);
    return () => stdout.off('resize', updateSize);
  }, [stdout]);

  return size;
}

/**
 * Main chat interface component
 */
export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  config,
  logger,
  onExit,
}) => {
  const { rows: terminalHeight, columns: terminalWidth } = useTerminalSize();
  const { stdin, setRawMode } = useStdin();
  
  // State
  const [shellModeActive, setShellModeActive] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [currentProvider, setCurrentProvider] = useState(config.defaultProvider);
  const [debugMessage, setDebugMessage] = useState('');

  // Hooks
  const history = useHistory(config, logger);
  const streaming = useStreaming(config, logger);
  const fileIntegration = useFileIntegration(config, logger);
  const memory = useMemory(config, logger);
  const commands = useCommands(
    config,
    logger,
    history.addItem,
    history.clearHistory,
    () => history.history,
    currentProvider,
    setCurrentProvider
  );

  // Calculate dimensions
  const inputWidth = Math.max(20, Math.floor(terminalWidth * 0.9) - 3);
  const suggestionsWidth = Math.max(60, Math.floor(terminalWidth * 0.8));
  const chatHeight = Math.max(10, terminalHeight - 15); // Reserve space for input and status

  // Text buffer for input
  const isValidPath = useCallback((filePath: string): boolean => {
    return fileIntegration.isFileAllowed(filePath);
  }, [fileIntegration]);

  const buffer = useTextBuffer({
    initialText: '',
    viewport: { height: 5, width: inputWidth },
    stdin,
    setRawMode,
    isValidPath,
    shellModeActive,
  });

  // Handle message submission
  const handleSubmit = useCallback(async (message: string) => {
    if (!message.trim()) return;

    try {
      // Check if it's a command
      if (message.startsWith('/')) {
        const result = await commands.executeCommand(message);
        if (result) {
          // Command was executed, result already added to history
          return;
        }
      }

      // Process file references
      const processedMessage = await fileIntegration.processFileReferences(message);

      // Add to memory
      await memory.addMemory(
        processedMessage,
        `User input: ${new Date().toISOString()}`,
        'user'
      );

      // Get relevant memories for context
      const relevantMemories = memory.getRelevantMemories(history.history, 3);

      // Submit to streaming
      await streaming.submitQuery(
        processedMessage,
        history.history,
        history.addItem,
        (id: string, content: string) => {
          history.updateItem(id, { content });
        },
        currentProvider
      );

      // Add AI response to memory when complete
      if (streaming.streamingState === StreamingState.Complete) {
        await memory.addMemory(
          streaming.currentResponse,
          `AI response: ${new Date().toISOString()}`,
          'ai'
        );
      }

    } catch (error) {
      logger.error('Failed to submit message:', error);
      const errorMessage: HistoryItem = {
        id: `error-${Date.now()}`,
        type: MessageType.Error,
        content: `Error: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date(),
      };
      history.addItem(errorMessage);
    }
  }, [commands, fileIntegration, memory, history, streaming, currentProvider, logger]);

  // Handle clear screen
  const handleClearScreen = useCallback(() => {
    history.clearHistory();
    setDebugMessage('Screen cleared');
  }, [history]);

  // Handle global key combinations
  useInput((input, key) => {
    // Handle Ctrl+C for exit
    if (key.ctrl && input === 'c') {
      onExit();
      return;
    }

    // Handle Ctrl+D for exit (if buffer is empty)
    if (key.ctrl && input === 'd' && buffer.text.trim() === '') {
      onExit();
      return;
    }

    // Handle F1 for help
    if (key.f1) {
      setShowHelp(!showHelp);
      return;
    }

    // Handle F2 to toggle shell mode
    if (key.f2) {
      setShellModeActive(!shellModeActive);
      setDebugMessage(`Shell mode ${!shellModeActive ? 'enabled' : 'disabled'}`);
      return;
    }

    // Handle F3 to cycle providers
    if (key.f3) {
      const providers = Object.keys(config.providers).filter(
        name => config.providers[name]?.enabled
      );
      const currentIndex = providers.indexOf(currentProvider);
      const nextIndex = (currentIndex + 1) % providers.length;
      setCurrentProvider(providers[nextIndex]);
      setDebugMessage(`Switched to provider: ${providers[nextIndex]}`);
      return;
    }

    // Handle Escape to cancel streaming
    if (key.escape && streaming.streamingState === StreamingState.Streaming) {
      streaming.cancelStream();
      setDebugMessage('Streaming cancelled');
      return;
    }
  }, { isActive: true });

  // Get user messages for history navigation
  const userMessages = useMemo(() => {
    return history.getLastUserMessages(50);
  }, [history]);

  // Render chat messages
  const renderChatMessages = () => {
    const visibleHistory = history.history.slice(-chatHeight);
    
    return visibleHistory.map((item) => (
      <Box key={item.id} marginBottom={1}>
        <Box flexDirection="column">
          {/* Message header */}
          <Box>
            <Text color={getMessageColor(item.type)} bold>
              {getMessagePrefix(item.type)}
            </Text>
            <Box marginLeft={1}>
              <Text color="gray" dimColor>
                {item.timestamp.toLocaleTimeString()}
              </Text>
            </Box>
            {item.provider && (
              <Box marginLeft={1}>
                <Text color="gray" dimColor>
                  via {item.provider}
                </Text>
              </Box>
            )}
            {item.isStreaming && (
              <Box marginLeft={1}>
                <Text color="yellow">
                  ⏳ streaming...
                </Text>
              </Box>
            )}
          </Box>
          
          {/* Message content */}
          <Box marginLeft={2} marginTop={1}>
            <Text color="white">
              {item.content}
            </Text>
          </Box>
        </Box>
      </Box>
    ));
  };

  // Render status bar
  const renderStatusBar = () => {
    const memoryStats = memory.getMemoryStats();
    const fileStats = fileIntegration.getCacheStats();
    
    return (
      <Box borderStyle="single" borderColor="cyan" padding={1}>
        <Box justifyContent="space-between" width="100%">
          <Box>
            <Text color="cyan">
              Provider: {currentProvider} | 
              Messages: {history.history.length} | 
              Memory: {memoryStats.totalEntries} | 
              Files: {fileStats.fileCount}
            </Text>
          </Box>
          <Box>
            <Text color="cyan">
              {streaming.streamingState === StreamingState.Streaming && '🔄 Streaming'}
              {streaming.streamingState === StreamingState.Complete && '✅ Ready'}
              {streaming.streamingState === StreamingState.Error && '❌ Error'}
              {streaming.streamingState === StreamingState.Idle && '💤 Idle'}
            </Text>
          </Box>
        </Box>
        
        {debugMessage && (
          <Box marginTop={1}>
            <Text color="yellow" dimColor>
              Debug: {debugMessage}
            </Text>
          </Box>
        )}
      </Box>
    );
  };

  // Render help panel
  const renderHelp = () => {
    if (!showHelp) return null;

    return (
      <Box
        borderStyle="single"
        borderColor="yellow"
        padding={1}
        marginBottom={1}
      >
        <Box flexDirection="column">
          <Text color="yellow" bold>
            🆘 Help & Shortcuts
          </Text>
          <Text color="white">
            • F1: Toggle this help
          </Text>
          <Text color="white">
            • F2: Toggle shell mode
          </Text>
          <Text color="white">
            • F3: Cycle AI providers
          </Text>
          <Text color="white">
            • Ctrl+C: Exit application
          </Text>
          <Text color="white">
            • Ctrl+L: Clear screen
          </Text>
          <Text color="white">
            • Escape: Cancel streaming
          </Text>
          <Text color="white">
            • @path/to/file: Reference files
          </Text>
          <Text color="white">
            • ↑↓: Navigate history
          </Text>
          <Text color="white">
            • Tab: Show suggestions
          </Text>
        </Box>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Box borderStyle="single" borderColor="blue" padding={1} marginBottom={1}>
        <Box justifyContent="center" width="100%">
          <Text color="blue" bold>
            🤖 S647 Refactored - AI Chat Interface
          </Text>
        </Box>
      </Box>

      {/* Help panel */}
      {renderHelp()}

      {/* Chat area */}
      <Box
        flexGrow={1}
        borderStyle="single"
        borderColor="gray"
        padding={1}
        marginBottom={1}
        flexDirection="column"
      >
        {history.history.length === 0 ? (
          <Box justifyContent="center" alignItems="center" height="100%">
            <Box flexDirection="column" alignItems="center">
              <Text color="gray" bold>
                Welcome to S647 Refactored! 🚀
              </Text>
              <Text color="gray">
                Start chatting with AI or press F1 for help
              </Text>
              <Text color="gray" dimColor>
                Current provider: {currentProvider}
              </Text>
            </Box>
          </Box>
        ) : (
          <Box flexDirection="column">
            {renderChatMessages()}
          </Box>
        )}
      </Box>

      {/* Input area */}
      <InputPrompt
        buffer={buffer}
        onSubmit={handleSubmit}
        userMessages={userMessages}
        onClearScreen={handleClearScreen}
        config={config}
        placeholder="💬 Type your message, /command, or @path/to/file..."
        focus={true}
        inputWidth={inputWidth}
        suggestionsWidth={suggestionsWidth}
        shellModeActive={shellModeActive}
        setShellModeActive={setShellModeActive}
        getCommandSuggestions={commands.getCommandSuggestions}
      />

      {/* Status bar */}
      {renderStatusBar()}
    </Box>
  );
};
