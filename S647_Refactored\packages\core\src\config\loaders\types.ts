/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { Configuration, AsyncResult } from '@inkbytefo/s647-shared';

/**
 * Configuration loader interface
 */
export interface ConfigLoader {
  /**
   * Loader name for identification
   */
  readonly name: string;

  /**
   * Priority level (higher = more priority)
   */
  readonly priority: number;

  /**
   * Load configuration from this source
   */
  load(options?: ConfigLoaderOptions): Promise<LoadResult>;

  /**
   * Check if this loader can load configuration
   */
  canLoad(options?: ConfigLoaderOptions): Promise<boolean>;
}

/**
 * Configuration loader options
 */
export interface ConfigLoaderOptions {
  /**
   * Base directory for relative paths
   */
  baseDir?: string;

  /**
   * Environment variables prefix
   */
  envPrefix?: string;

  /**
   * CLI arguments
   */
  args?: Record<string, any>;

  /**
   * Additional options
   */
  [key: string]: any;
}

/**
 * Load result from a configuration loader
 */
export interface LoadResult {
  /**
   * Whether loading was successful
   */
  success: boolean;

  /**
   * Loaded configuration (partial)
   */
  config?: Partial<Configuration>;

  /**
   * Source information
   */
  source?: ConfigSource;

  /**
   * Error if loading failed
   */
  error?: Error;

  /**
   * Warnings during loading
   */
  warnings?: string[];
}

/**
 * Configuration source information
 */
export interface ConfigSource {
  /**
   * Source type
   */
  type: 'file' | 'environment' | 'cli' | 'default';

  /**
   * Source path or identifier
   */
  path?: string;

  /**
   * Priority level
   */
  priority: number;

  /**
   * Timestamp when loaded
   */
  timestamp: number;

  /**
   * Additional metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Configuration merge strategy
 */
export type MergeStrategy = 'replace' | 'merge' | 'append';

/**
 * Configuration merge options
 */
export interface MergeOptions {
  /**
   * Default merge strategy
   */
  strategy?: MergeStrategy;

  /**
   * Field-specific merge strategies
   */
  fieldStrategies?: Record<string, MergeStrategy>;

  /**
   * Whether to preserve source information
   */
  preserveSources?: boolean;
}
