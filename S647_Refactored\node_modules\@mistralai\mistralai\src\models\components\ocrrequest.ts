/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DocumentURLChunk,
  DocumentURLChunk$inboundSchema,
  DocumentURLChunk$Outbound,
  DocumentURLChunk$outboundSchema,
} from "./documenturlchunk.js";
import {
  FileChunk,
  FileChunk$inboundSchema,
  FileChunk$Outbound,
  FileChunk$outboundSchema,
} from "./filechunk.js";
import {
  ImageURLChunk,
  ImageURLChunk$inboundSchema,
  ImageURLChunk$Outbound,
  ImageURLChunk$outboundSchema,
} from "./imageurlchunk.js";
import {
  ResponseFormat,
  ResponseFormat$inboundSchema,
  ResponseFormat$Outbound,
  ResponseFormat$outboundSchema,
} from "./responseformat.js";

/**
 * Document to run OCR on
 */
export type Document = FileChunk | ImageURLChunk | DocumentURLChunk;

export type OCRRequest = {
  model: string | null;
  id?: string | undefined;
  /**
   * Document to run OCR on
   */
  document: FileChunk | ImageURLChunk | DocumentURLChunk;
  /**
   * Specific pages user wants to process in various formats: single number, range, or list of both. Starts from 0
   */
  pages?: Array<number> | null | undefined;
  /**
   * Include image URLs in response
   */
  includeImageBase64?: boolean | null | undefined;
  /**
   * Max images to extract
   */
  imageLimit?: number | null | undefined;
  /**
   * Minimum height and width of image to extract
   */
  imageMinSize?: number | null | undefined;
  /**
   * Structured output class for extracting useful information from each extracted bounding box / image from document. Only json_schema is valid for this field
   */
  bboxAnnotationFormat?: ResponseFormat | null | undefined;
  /**
   * Structured output class for extracting useful information from the entire document. Only json_schema is valid for this field
   */
  documentAnnotationFormat?: ResponseFormat | null | undefined;
};

/** @internal */
export const Document$inboundSchema: z.ZodType<
  Document,
  z.ZodTypeDef,
  unknown
> = z.union([
  FileChunk$inboundSchema,
  ImageURLChunk$inboundSchema,
  DocumentURLChunk$inboundSchema,
]);

/** @internal */
export type Document$Outbound =
  | FileChunk$Outbound
  | ImageURLChunk$Outbound
  | DocumentURLChunk$Outbound;

/** @internal */
export const Document$outboundSchema: z.ZodType<
  Document$Outbound,
  z.ZodTypeDef,
  Document
> = z.union([
  FileChunk$outboundSchema,
  ImageURLChunk$outboundSchema,
  DocumentURLChunk$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Document$ {
  /** @deprecated use `Document$inboundSchema` instead. */
  export const inboundSchema = Document$inboundSchema;
  /** @deprecated use `Document$outboundSchema` instead. */
  export const outboundSchema = Document$outboundSchema;
  /** @deprecated use `Document$Outbound` instead. */
  export type Outbound = Document$Outbound;
}

export function documentToJSON(document: Document): string {
  return JSON.stringify(Document$outboundSchema.parse(document));
}

export function documentFromJSON(
  jsonString: string,
): SafeParseResult<Document, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Document$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Document' from JSON`,
  );
}

/** @internal */
export const OCRRequest$inboundSchema: z.ZodType<
  OCRRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  model: z.nullable(z.string()),
  id: z.string().optional(),
  document: z.union([
    FileChunk$inboundSchema,
    ImageURLChunk$inboundSchema,
    DocumentURLChunk$inboundSchema,
  ]),
  pages: z.nullable(z.array(z.number().int())).optional(),
  include_image_base64: z.nullable(z.boolean()).optional(),
  image_limit: z.nullable(z.number().int()).optional(),
  image_min_size: z.nullable(z.number().int()).optional(),
  bbox_annotation_format: z.nullable(ResponseFormat$inboundSchema).optional(),
  document_annotation_format: z.nullable(ResponseFormat$inboundSchema)
    .optional(),
}).transform((v) => {
  return remap$(v, {
    "include_image_base64": "includeImageBase64",
    "image_limit": "imageLimit",
    "image_min_size": "imageMinSize",
    "bbox_annotation_format": "bboxAnnotationFormat",
    "document_annotation_format": "documentAnnotationFormat",
  });
});

/** @internal */
export type OCRRequest$Outbound = {
  model: string | null;
  id?: string | undefined;
  document:
    | FileChunk$Outbound
    | ImageURLChunk$Outbound
    | DocumentURLChunk$Outbound;
  pages?: Array<number> | null | undefined;
  include_image_base64?: boolean | null | undefined;
  image_limit?: number | null | undefined;
  image_min_size?: number | null | undefined;
  bbox_annotation_format?: ResponseFormat$Outbound | null | undefined;
  document_annotation_format?: ResponseFormat$Outbound | null | undefined;
};

/** @internal */
export const OCRRequest$outboundSchema: z.ZodType<
  OCRRequest$Outbound,
  z.ZodTypeDef,
  OCRRequest
> = z.object({
  model: z.nullable(z.string()),
  id: z.string().optional(),
  document: z.union([
    FileChunk$outboundSchema,
    ImageURLChunk$outboundSchema,
    DocumentURLChunk$outboundSchema,
  ]),
  pages: z.nullable(z.array(z.number().int())).optional(),
  includeImageBase64: z.nullable(z.boolean()).optional(),
  imageLimit: z.nullable(z.number().int()).optional(),
  imageMinSize: z.nullable(z.number().int()).optional(),
  bboxAnnotationFormat: z.nullable(ResponseFormat$outboundSchema).optional(),
  documentAnnotationFormat: z.nullable(ResponseFormat$outboundSchema)
    .optional(),
}).transform((v) => {
  return remap$(v, {
    includeImageBase64: "include_image_base64",
    imageLimit: "image_limit",
    imageMinSize: "image_min_size",
    bboxAnnotationFormat: "bbox_annotation_format",
    documentAnnotationFormat: "document_annotation_format",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OCRRequest$ {
  /** @deprecated use `OCRRequest$inboundSchema` instead. */
  export const inboundSchema = OCRRequest$inboundSchema;
  /** @deprecated use `OCRRequest$outboundSchema` instead. */
  export const outboundSchema = OCRRequest$outboundSchema;
  /** @deprecated use `OCRRequest$Outbound` instead. */
  export type Outbound = OCRRequest$Outbound;
}

export function ocrRequestToJSON(ocrRequest: OCRRequest): string {
  return JSON.stringify(OCRRequest$outboundSchema.parse(ocrRequest));
}

export function ocrRequestFromJSON(
  jsonString: string,
): SafeParseResult<OCRRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OCRRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OCRRequest' from JSON`,
  );
}
