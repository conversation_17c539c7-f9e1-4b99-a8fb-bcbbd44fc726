/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Tool, ToolId, ToolParams, ToolContext, ToolResult, AsyncResult } from '@inkbytefo/s647-shared';
/**
 * Tool manager for registering and executing tools
 */
export declare class ToolManager {
    private registry;
    constructor();
    /**
     * Register a tool
     */
    registerTool(tool: Tool): void;
    /**
     * Unregister a tool
     */
    unregisterTool(id: ToolId): void;
    /**
     * Get a tool by ID
     */
    getTool(id: ToolId): Tool | undefined;
    /**
     * Get all registered tools
     */
    getAllTools(): Tool[];
    /**
     * Get tools by category
     */
    getToolsByCategory(category: string): Tool[];
    /**
     * Search tools by query
     */
    searchTools(query: string): Tool[];
    /**
     * Execute a tool
     */
    executeTool(id: ToolId, params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    /**
     * Validate tool parameters
     */
    validateToolParams(id: ToolId, params: ToolParams): {
        valid: boolean;
        errors: string[];
    };
    /**
     * Get tool help
     */
    getToolHelp(id: ToolId): import("@inkbytefo/s647-shared").ToolHelp | undefined;
    /**
     * Get tool categories
     */
    getCategories(): string[];
    /**
     * Get tools summary
     */
    getToolsSummary(): {
        totalTools: number;
        categories: {
            name: string;
            count: number;
            tools: {
                id: string;
                name: string;
                description: string;
                version: string;
            }[];
        }[];
    };
    /**
     * Clear all tools
     */
    clear(): void;
    /**
     * Register default tools
     */
    private registerDefaultTools;
}
//# sourceMappingURL=ToolManager.d.ts.map