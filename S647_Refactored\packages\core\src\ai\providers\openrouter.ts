/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  OpenRouterProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * OpenRouter provider implementation
 * OpenRouter is OpenAI-compatible, so we use the OpenAI SDK with custom base URL
 */
export class OpenRouterProvider extends BaseProvider {
  private client: any; // OpenAI client instance

  constructor(id: ProviderId, config: OpenRouterProviderConfig) {
    super(id, config);
  }

  public get openrouterConfig(): OpenRouterProviderConfig {
    return this.config as OpenRouterProviderConfig;
  }

  /**
   * Initialize the OpenRouter provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();

      // Initialize OpenAI client with OpenRouter base URL
      const { OpenAI } = await import('openai');

      if (!this.openrouterConfig.apiKey) {
        throw new Error('OpenRouter API key is required');
      }

      this.client = new OpenAI({
        apiKey: this.openrouterConfig.apiKey,
        baseURL: this.openrouterConfig.baseUrl || 'https://openrouter.ai/api/v1',
        timeout: this.openrouterConfig.timeout || 30000,
        maxRetries: this.openrouterConfig.retries || 3,
        defaultHeaders: {
          'HTTP-Referer': this.openrouterConfig.siteUrl || 'https://s647.dev',
          'X-Title': this.openrouterConfig.siteName || 'S647',
          ...this.openrouterConfig.headers,
        },
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Test with a simple models list call
      await this.client.models.list();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available models from OpenRouter
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.models.list();
      const models: ModelInfo[] = response.data.map((model: any) => ({
        id: model.id,
        name: model.id,
        provider: 'openrouter' as const,
        capabilities: {
          chat: true,
          completion: true,
          embedding: model.id.includes('embedding'),
          vision: model.id.includes('vision') || model.id.includes('gpt-4'),
          functionCalling: !model.id.includes('embedding'),
          streaming: true,
          systemMessages: true,
          multiModal: model.id.includes('vision') || model.id.includes('gpt-4'),
        },
        contextLength: model.context_length || 4096,
        maxTokens: model.max_completion_tokens || 4096,
        pricing: {
          input: model.pricing?.prompt,
          output: model.pricing?.completion,
        },
      }));

      return { success: true, data: models };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion (OpenAI-compatible)
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: false,
        tools: request.tools,
        tool_choice: request.toolChoice,
        user: request.user,
      });

      return { success: true, data: response as ChatCompletionResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion (OpenAI-compatible)
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const stream = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: true,
        tools: request.tools,
        tool_choice: request.toolChoice,
        user: request.user,
      });

      return { success: true, data: stream as AsyncIterable<ChatCompletionChunk> };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings (OpenAI-compatible)
   */
  public async createEmbedding(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.embeddings.create({
        model: request.model,
        input: request.input,
        user: request.user,
      });

      return { success: true, data: response as EmbeddingResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      // OpenRouter doesn't have a dedicated token counting endpoint
      // Use estimation based on character count
      const text = typeof input === 'string'
        ? input
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');

      // Use OpenAI's estimation (roughly 4 characters per token)
      const estimatedTokens = Math.ceil(text.length / 4);
      return { success: true, data: estimatedTokens };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }
}
