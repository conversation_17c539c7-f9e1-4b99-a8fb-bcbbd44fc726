/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConfigLoader, ConfigLoaderOptions, LoadResult } from './types.js';
/**
 * CLI arguments configuration loader
 * Loads configuration from command line arguments
 */
export declare class CliConfigLoader implements ConfigLoader {
    readonly name = "cli";
    readonly priority = 100;
    /**
     * Load configuration from CLI arguments
     */
    load(options?: ConfigLoaderOptions): Promise<LoadResult>;
    /**
     * Check if this loader can load configuration
     */
    canLoad(options?: ConfigLoaderOptions): Promise<boolean>;
    /**
     * Parse CLI arguments into configuration object
     */
    private parseCliArguments;
    /**
     * Parse provider-specific CLI arguments
     */
    private parseProviderArguments;
    /**
     * Set nested value in object
     */
    private setNestedValue;
    /**
     * Check if CLI arguments contain configuration
     */
    private hasConfigArguments;
}
//# sourceMappingURL=cli-loader.d.ts.map