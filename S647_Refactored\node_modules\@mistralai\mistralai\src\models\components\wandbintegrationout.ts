/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const WandbIntegrationOutType = {
  Wandb: "wandb",
} as const;
export type WandbIntegrationOutType = ClosedEnum<
  typeof WandbIntegrationOutType
>;

export type WandbIntegrationOut = {
  type?: WandbIntegrationOutType | undefined;
  /**
   * The name of the project that the new run will be created under.
   */
  project: string;
  /**
   * A display name to set for the run. If not set, will use the job ID as the name.
   */
  name?: string | null | undefined;
  runName?: string | null | undefined;
  url?: string | null | undefined;
};

/** @internal */
export const WandbIntegrationOutType$inboundSchema: z.ZodNativeEnum<
  typeof WandbIntegrationOutType
> = z.nativeEnum(WandbIntegrationOutType);

/** @internal */
export const WandbIntegrationOutType$outboundSchema: z.ZodNativeEnum<
  typeof WandbIntegrationOutType
> = WandbIntegrationOutType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WandbIntegrationOutType$ {
  /** @deprecated use `WandbIntegrationOutType$inboundSchema` instead. */
  export const inboundSchema = WandbIntegrationOutType$inboundSchema;
  /** @deprecated use `WandbIntegrationOutType$outboundSchema` instead. */
  export const outboundSchema = WandbIntegrationOutType$outboundSchema;
}

/** @internal */
export const WandbIntegrationOut$inboundSchema: z.ZodType<
  WandbIntegrationOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: WandbIntegrationOutType$inboundSchema.default("wandb"),
  project: z.string(),
  name: z.nullable(z.string()).optional(),
  run_name: z.nullable(z.string()).optional(),
  url: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "run_name": "runName",
  });
});

/** @internal */
export type WandbIntegrationOut$Outbound = {
  type: string;
  project: string;
  name?: string | null | undefined;
  run_name?: string | null | undefined;
  url?: string | null | undefined;
};

/** @internal */
export const WandbIntegrationOut$outboundSchema: z.ZodType<
  WandbIntegrationOut$Outbound,
  z.ZodTypeDef,
  WandbIntegrationOut
> = z.object({
  type: WandbIntegrationOutType$outboundSchema.default("wandb"),
  project: z.string(),
  name: z.nullable(z.string()).optional(),
  runName: z.nullable(z.string()).optional(),
  url: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    runName: "run_name",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WandbIntegrationOut$ {
  /** @deprecated use `WandbIntegrationOut$inboundSchema` instead. */
  export const inboundSchema = WandbIntegrationOut$inboundSchema;
  /** @deprecated use `WandbIntegrationOut$outboundSchema` instead. */
  export const outboundSchema = WandbIntegrationOut$outboundSchema;
  /** @deprecated use `WandbIntegrationOut$Outbound` instead. */
  export type Outbound = WandbIntegrationOut$Outbound;
}

export function wandbIntegrationOutToJSON(
  wandbIntegrationOut: WandbIntegrationOut,
): string {
  return JSON.stringify(
    WandbIntegrationOut$outboundSchema.parse(wandbIntegrationOut),
  );
}

export function wandbIntegrationOutFromJSON(
  jsonString: string,
): SafeParseResult<WandbIntegrationOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => WandbIntegrationOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'WandbIntegrationOut' from JSON`,
  );
}
