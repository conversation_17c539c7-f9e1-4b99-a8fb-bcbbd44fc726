/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FileSignedURL = {
  url: string;
};

/** @internal */
export const FileSignedURL$inboundSchema: z.ZodType<
  FileSignedURL,
  z.ZodTypeDef,
  unknown
> = z.object({
  url: z.string(),
});

/** @internal */
export type FileSignedURL$Outbound = {
  url: string;
};

/** @internal */
export const FileSignedURL$outboundSchema: z.ZodType<
  FileSignedURL$Outbound,
  z.ZodTypeDef,
  FileSignedURL
> = z.object({
  url: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FileSignedURL$ {
  /** @deprecated use `FileSignedURL$inboundSchema` instead. */
  export const inboundSchema = FileSignedURL$inboundSchema;
  /** @deprecated use `FileSignedURL$outboundSchema` instead. */
  export const outboundSchema = FileSignedURL$outboundSchema;
  /** @deprecated use `FileSignedURL$Outbound` instead. */
  export type Outbound = FileSignedURL$Outbound;
}

export function fileSignedURLToJSON(fileSignedURL: FileSignedURL): string {
  return JSON.stringify(FileSignedURL$outboundSchema.parse(fileSignedURL));
}

export function fileSignedURLFromJSON(
  jsonString: string,
): SafeParseResult<FileSignedURL, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FileSignedURL$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FileSignedURL' from JSON`,
  );
}
