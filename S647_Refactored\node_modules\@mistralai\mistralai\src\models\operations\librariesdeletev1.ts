/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDeleteV1Request = {
  libraryId: string;
};

/** @internal */
export const LibrariesDeleteV1Request$inboundSchema: z.ZodType<
  LibrariesDeleteV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
  });
});

/** @internal */
export type LibrariesDeleteV1Request$Outbound = {
  library_id: string;
};

/** @internal */
export const LibrariesDeleteV1Request$outboundSchema: z.ZodType<
  LibrariesDeleteV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDeleteV1Request
> = z.object({
  libraryId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDeleteV1Request$ {
  /** @deprecated use `LibrariesDeleteV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesDeleteV1Request$inboundSchema;
  /** @deprecated use `LibrariesDeleteV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesDeleteV1Request$outboundSchema;
  /** @deprecated use `LibrariesDeleteV1Request$Outbound` instead. */
  export type Outbound = LibrariesDeleteV1Request$Outbound;
}

export function librariesDeleteV1RequestToJSON(
  librariesDeleteV1Request: LibrariesDeleteV1Request,
): string {
  return JSON.stringify(
    LibrariesDeleteV1Request$outboundSchema.parse(librariesDeleteV1Request),
  );
}

export function librariesDeleteV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDeleteV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesDeleteV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDeleteV1Request' from JSON`,
  );
}
