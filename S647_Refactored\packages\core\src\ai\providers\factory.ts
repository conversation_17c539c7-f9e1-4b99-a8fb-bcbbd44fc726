/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  Provider,
  ProviderConfig,
  ProviderType,
  ProviderId,
  AsyncResult,
} from '@inkbytefo/s647-shared';
import type { IProviderFactory } from '../interfaces/provider.js';
import { OpenAIProvider } from './openai.js';
import { AnthropicProvider } from './anthropic.js';
import { GoogleProvider } from './google.js';
import { MistralProvider } from './mistral.js';
import { OpenRouterProvider } from './openrouter.js';
import { CustomProvider } from './custom.js';
import { LocalProvider } from './local.js';

/**
 * Provider factory implementation
 */
export class ProviderFactory implements IProviderFactory {
  private static instance: ProviderFactory;

  /**
   * Get the singleton instance
   */
  public static getInstance(): ProviderFactory {
    if (!ProviderFactory.instance) {
      ProviderFactory.instance = new ProviderFactory();
    }
    return ProviderFactory.instance;
  }

  /**
   * Create a provider instance
   */
  public async create(
    id: ProviderId,
    config: ProviderConfig
  ): AsyncResult<Provider> {
    try {
      // Validate configuration first
      const validationResult = await this.validateConfig(config);
      if (!validationResult.success) {
        return validationResult;
      }

      let provider: Provider;

      switch (config.type) {
        case 'openai':
          provider = new OpenAIProvider(id, config);
          break;
        
        case 'anthropic':
          provider = new AnthropicProvider(id, config);
          break;

        case 'google':
          provider = new GoogleProvider(id, config);
          break;

        case 'mistral':
          provider = new MistralProvider(id, config);
          break;

        case 'openrouter':
          provider = new OpenRouterProvider(id, config);
          break;

        case 'custom':
          provider = new CustomProvider(id, config);
          break;

        case 'local':
          provider = new LocalProvider(id, config);
          break;
        
        default:
          throw new Error(`Unsupported provider type: ${(config as any).type}`);
      }

      // Initialize the provider
      const initResult = await provider.initialize();
      if (!initResult.success) {
        return initResult;
      }

      return { success: true, data: provider };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  }

  /**
   * Get supported provider types
   */
  public getSupportedTypes(): ProviderType[] {
    return [
      'openai',
      'anthropic',
      'google',
      'mistral',
      'openrouter',
      'custom',
      'local',
    ];
  }

  /**
   * Validate provider configuration
   */
  public async validateConfig(config: ProviderConfig): AsyncResult<void> {
    try {
      // Basic validation
      if (!config.type) {
        throw new Error('Provider type is required');
      }

      if (!this.getSupportedTypes().includes(config.type)) {
        throw new Error(`Unsupported provider type: ${config.type}`);
      }

      // Type-specific validation
      switch (config.type) {
        case 'openai':
          this.validateOpenAIConfig(config);
          break;
        
        case 'anthropic':
          this.validateAnthropicConfig(config);
          break;
        
        case 'google':
          this.validateGoogleConfig(config);
          break;
        
        case 'mistral':
          this.validateMistralConfig(config);
          break;
        
        case 'openrouter':
          this.validateOpenRouterConfig(config);
          break;
        
        case 'custom':
          this.validateCustomConfig(config);
          break;
        
        case 'local':
          this.validateLocalConfig(config);
          break;
      }

      return { success: true, data: undefined };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  }

  /**
   * Validate OpenAI configuration
   */
  private validateOpenAIConfig(config: ProviderConfig): void {
    if (!config.apiKey) {
      throw new Error('OpenAI API key is required');
    }
  }

  /**
   * Validate Anthropic configuration
   */
  private validateAnthropicConfig(config: ProviderConfig): void {
    if (!config.apiKey) {
      throw new Error('Anthropic API key is required');
    }
  }

  /**
   * Validate Google configuration
   */
  private validateGoogleConfig(config: ProviderConfig): void {
    if (!config.apiKey) {
      throw new Error('Google API key is required');
    }
  }

  /**
   * Validate Mistral configuration
   */
  private validateMistralConfig(config: ProviderConfig): void {
    if (!config.apiKey) {
      throw new Error('Mistral API key is required');
    }
  }

  /**
   * Validate OpenRouter configuration
   */
  private validateOpenRouterConfig(config: ProviderConfig): void {
    if (!config.apiKey) {
      throw new Error('OpenRouter API key is required');
    }
  }

  /**
   * Validate Custom configuration
   */
  private validateCustomConfig(config: ProviderConfig): void {
    if (!config.baseUrl) {
      throw new Error('Base URL is required for custom provider');
    }
  }

  /**
   * Validate Local configuration
   */
  private validateLocalConfig(config: ProviderConfig): void {
    const localConfig = config as any;
    if (!localConfig.endpoint) {
      throw new Error('Endpoint is required for local provider');
    }
  }
}
