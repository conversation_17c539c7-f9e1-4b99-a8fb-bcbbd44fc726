/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ChatMessage, AsyncResult } from '@inkbytefo/s647-shared';
/**
 * Chat interface definitions
 */
export interface ChatSession {
    id: string;
    messages: ChatMessage[];
    createdAt: number;
    updatedAt: number;
    metadata?: Record<string, unknown>;
}
export interface ChatManager {
    createSession(): AsyncResult<ChatSession>;
    getSession(id: string): AsyncResult<ChatSession>;
    updateSession(id: string, session: Partial<ChatSession>): AsyncResult<ChatSession>;
    deleteSession(id: string): AsyncResult<void>;
    listSessions(): AsyncResult<ChatSession[]>;
}
//# sourceMappingURL=chat.d.ts.map