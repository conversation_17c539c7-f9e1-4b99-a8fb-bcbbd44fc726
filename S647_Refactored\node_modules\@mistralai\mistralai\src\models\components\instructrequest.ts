/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  AssistantMessage,
  AssistantMessage$inboundSchema,
  AssistantMessage$Outbound,
  AssistantMessage$outboundSchema,
} from "./assistantmessage.js";
import {
  SystemMessage,
  SystemMessage$inboundSchema,
  SystemMessage$Outbound,
  SystemMessage$outboundSchema,
} from "./systemmessage.js";
import {
  ToolMessage,
  ToolMessage$inboundSchema,
  ToolMessage$Outbound,
  ToolMessage$outboundSchema,
} from "./toolmessage.js";
import {
  UserMessage,
  UserMessage$inboundSchema,
  UserMessage$Outbound,
  UserMessage$outboundSchema,
} from "./usermessage.js";

export type InstructRequestMessages =
  | (SystemMessage & { role: "system" })
  | (UserMessage & { role: "user" })
  | (AssistantMessage & { role: "assistant" })
  | (ToolMessage & { role: "tool" });

export type InstructRequest = {
  messages: Array<
    | (SystemMessage & { role: "system" })
    | (UserMessage & { role: "user" })
    | (AssistantMessage & { role: "assistant" })
    | (ToolMessage & { role: "tool" })
  >;
};

/** @internal */
export const InstructRequestMessages$inboundSchema: z.ZodType<
  InstructRequestMessages,
  z.ZodTypeDef,
  unknown
> = z.union([
  SystemMessage$inboundSchema.and(
    z.object({ role: z.literal("system") }).transform((v) => ({
      role: v.role,
    })),
  ),
  UserMessage$inboundSchema.and(
    z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role })),
  ),
  AssistantMessage$inboundSchema.and(
    z.object({ role: z.literal("assistant") }).transform((v) => ({
      role: v.role,
    })),
  ),
  ToolMessage$inboundSchema.and(
    z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role })),
  ),
]);

/** @internal */
export type InstructRequestMessages$Outbound =
  | (SystemMessage$Outbound & { role: "system" })
  | (UserMessage$Outbound & { role: "user" })
  | (AssistantMessage$Outbound & { role: "assistant" })
  | (ToolMessage$Outbound & { role: "tool" });

/** @internal */
export const InstructRequestMessages$outboundSchema: z.ZodType<
  InstructRequestMessages$Outbound,
  z.ZodTypeDef,
  InstructRequestMessages
> = z.union([
  SystemMessage$outboundSchema.and(
    z.object({ role: z.literal("system") }).transform((v) => ({
      role: v.role,
    })),
  ),
  UserMessage$outboundSchema.and(
    z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role })),
  ),
  AssistantMessage$outboundSchema.and(
    z.object({ role: z.literal("assistant") }).transform((v) => ({
      role: v.role,
    })),
  ),
  ToolMessage$outboundSchema.and(
    z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace InstructRequestMessages$ {
  /** @deprecated use `InstructRequestMessages$inboundSchema` instead. */
  export const inboundSchema = InstructRequestMessages$inboundSchema;
  /** @deprecated use `InstructRequestMessages$outboundSchema` instead. */
  export const outboundSchema = InstructRequestMessages$outboundSchema;
  /** @deprecated use `InstructRequestMessages$Outbound` instead. */
  export type Outbound = InstructRequestMessages$Outbound;
}

export function instructRequestMessagesToJSON(
  instructRequestMessages: InstructRequestMessages,
): string {
  return JSON.stringify(
    InstructRequestMessages$outboundSchema.parse(instructRequestMessages),
  );
}

export function instructRequestMessagesFromJSON(
  jsonString: string,
): SafeParseResult<InstructRequestMessages, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => InstructRequestMessages$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'InstructRequestMessages' from JSON`,
  );
}

/** @internal */
export const InstructRequest$inboundSchema: z.ZodType<
  InstructRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  messages: z.array(
    z.union([
      SystemMessage$inboundSchema.and(
        z.object({ role: z.literal("system") }).transform((v) => ({
          role: v.role,
        })),
      ),
      UserMessage$inboundSchema.and(
        z.object({ role: z.literal("user") }).transform((v) => ({
          role: v.role,
        })),
      ),
      AssistantMessage$inboundSchema.and(
        z.object({ role: z.literal("assistant") }).transform((v) => ({
          role: v.role,
        })),
      ),
      ToolMessage$inboundSchema.and(
        z.object({ role: z.literal("tool") }).transform((v) => ({
          role: v.role,
        })),
      ),
    ]),
  ),
});

/** @internal */
export type InstructRequest$Outbound = {
  messages: Array<
    | (SystemMessage$Outbound & { role: "system" })
    | (UserMessage$Outbound & { role: "user" })
    | (AssistantMessage$Outbound & { role: "assistant" })
    | (ToolMessage$Outbound & { role: "tool" })
  >;
};

/** @internal */
export const InstructRequest$outboundSchema: z.ZodType<
  InstructRequest$Outbound,
  z.ZodTypeDef,
  InstructRequest
> = z.object({
  messages: z.array(
    z.union([
      SystemMessage$outboundSchema.and(
        z.object({ role: z.literal("system") }).transform((v) => ({
          role: v.role,
        })),
      ),
      UserMessage$outboundSchema.and(
        z.object({ role: z.literal("user") }).transform((v) => ({
          role: v.role,
        })),
      ),
      AssistantMessage$outboundSchema.and(
        z.object({ role: z.literal("assistant") }).transform((v) => ({
          role: v.role,
        })),
      ),
      ToolMessage$outboundSchema.and(
        z.object({ role: z.literal("tool") }).transform((v) => ({
          role: v.role,
        })),
      ),
    ]),
  ),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace InstructRequest$ {
  /** @deprecated use `InstructRequest$inboundSchema` instead. */
  export const inboundSchema = InstructRequest$inboundSchema;
  /** @deprecated use `InstructRequest$outboundSchema` instead. */
  export const outboundSchema = InstructRequest$outboundSchema;
  /** @deprecated use `InstructRequest$Outbound` instead. */
  export type Outbound = InstructRequest$Outbound;
}

export function instructRequestToJSON(
  instructRequest: InstructRequest,
): string {
  return JSON.stringify(InstructRequest$outboundSchema.parse(instructRequest));
}

export function instructRequestFromJSON(
  jsonString: string,
): SafeParseResult<InstructRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => InstructRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'InstructRequest' from JSON`,
  );
}
