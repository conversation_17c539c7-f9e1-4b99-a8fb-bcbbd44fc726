/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { resolve, isAbsolute } from 'path';
import { z } from 'zod';
import { BaseTool } from '../../base/BaseTool.js';
const execAsync = promisify(exec);
const ShellExecuteParams = z.object({
    command: z.string().describe('Shell command to execute'),
    workingDirectory: z.string().optional().describe('Working directory for command execution'),
    timeout: z.number().optional().default(30000).describe('Command timeout in milliseconds'),
    shell: z.string().optional().describe('Shell to use (default: system default)'),
    env: z.record(z.string()).optional().describe('Environment variables'),
    captureOutput: z.boolean().optional().default(true).describe('Capture command output'),
    allowDangerous: z.boolean().optional().default(false).describe('Allow potentially dangerous commands'),
});
/**
 * Tool for executing shell commands
 */
export class ShellExecuteTool extends BaseTool {
    dangerousCommands = [
        'rm -rf',
        'del /s',
        'format',
        'fdisk',
        'mkfs',
        'dd if=',
        'sudo rm',
        'sudo del',
        '> /dev/',
        'chmod 777',
        'chown -R',
        'killall',
        'pkill -9',
        'shutdown',
        'reboot',
        'halt',
        'init 0',
        'init 6',
    ];
    constructor() {
        super('shell-execute', 'Shell Execute', 'Execute shell commands and return output', '1.0.0', 'shell', ShellExecuteParams);
    }
    async execute(params, context) {
        try {
            const { command, workingDirectory, timeout, shell, env, captureOutput, allowDangerous } = await this.validateAndParse(params);
            // Security check for dangerous commands
            if (!allowDangerous && this.isDangerousCommand(command)) {
                return this.error(`Potentially dangerous command detected: "${command}". ` +
                    'Set allowDangerous: true to execute anyway.');
            }
            // Resolve working directory
            const cwd = workingDirectory
                ? this.resolvePath(workingDirectory, context.workingDirectory)
                : context.workingDirectory || process.cwd();
            // Prepare execution environment
            const execEnv = {
                ...process.env,
                ...env,
            };
            // Execute command
            const result = await this.executeCommand(command, {
                cwd,
                timeout,
                ...(shell && { shell }),
                env: execEnv,
            });
            return this.success({
                type: 'text',
                content: this.formatResult(command, result, cwd),
                metadata: {
                    command,
                    workingDirectory: cwd,
                    exitCode: result.exitCode,
                    executionTime: result.executionTime,
                    shell: shell || null,
                    outputLength: result.stdout.length + result.stderr.length,
                },
            });
        }
        catch (error) {
            return this.error(this.handleError(error));
        }
    }
    getHelp() {
        return {
            description: 'Execute shell commands and capture their output',
            parameters: {
                command: {
                    type: 'string',
                    description: 'Shell command to execute',
                    required: true,
                },
                workingDirectory: {
                    type: 'string',
                    description: 'Working directory for command execution',
                    required: false,
                },
                timeout: {
                    type: 'number',
                    description: 'Command timeout in milliseconds',
                    required: false,
                    default: 30000,
                },
                shell: {
                    type: 'string',
                    description: 'Shell to use (e.g., "bash", "cmd", "powershell")',
                    required: false,
                },
                env: {
                    type: 'object',
                    description: 'Environment variables as key-value pairs',
                    required: false,
                },
                captureOutput: {
                    type: 'boolean',
                    description: 'Whether to capture command output',
                    required: false,
                    default: true,
                },
                allowDangerous: {
                    type: 'boolean',
                    description: 'Allow potentially dangerous commands',
                    required: false,
                    default: false,
                },
            },
            examples: [
                {
                    description: 'List directory contents',
                    parameters: {
                        command: 'ls -la'
                    },
                },
                {
                    description: 'Check Node.js version',
                    parameters: {
                        command: 'node --version'
                    },
                },
                {
                    description: 'Run command in specific directory',
                    parameters: {
                        command: 'npm install',
                        workingDirectory: './my-project'
                    },
                },
                {
                    description: 'Run with custom environment variables',
                    parameters: {
                        command: 'echo $MY_VAR',
                        env: { 'MY_VAR': 'Hello World' }
                    },
                },
                {
                    description: 'Run with extended timeout',
                    parameters: {
                        command: 'npm run build',
                        timeout: 120000
                    },
                },
            ],
        };
    }
    resolvePath(path, workingDirectory) {
        if (isAbsolute(path)) {
            return resolve(path);
        }
        return resolve(workingDirectory || process.cwd(), path);
    }
    isDangerousCommand(command) {
        const lowerCommand = command.toLowerCase();
        return this.dangerousCommands.some(dangerous => lowerCommand.includes(dangerous.toLowerCase()));
    }
    async executeCommand(command, options) {
        const startTime = Date.now();
        try {
            const { stdout, stderr } = await execAsync(command, {
                cwd: options.cwd,
                timeout: options.timeout,
                shell: options.shell,
                env: options.env,
                maxBuffer: 1024 * 1024 * 10, // 10MB buffer
            });
            return {
                stdout: stdout || '',
                stderr: stderr || '',
                exitCode: 0,
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                stdout: error.stdout || '',
                stderr: error.stderr || error.message || '',
                exitCode: error.code || 1,
                executionTime: Date.now() - startTime,
            };
        }
    }
    formatResult(command, result, cwd) {
        const lines = [`Shell Command Execution\n`];
        lines.push(`Command: ${command}`);
        lines.push(`Working Directory: ${cwd}`);
        lines.push(`Exit Code: ${result.exitCode}`);
        lines.push(`Execution Time: ${result.executionTime}ms`);
        lines.push('');
        if (result.stdout) {
            lines.push('--- STDOUT ---');
            lines.push(result.stdout);
            lines.push('');
        }
        if (result.stderr) {
            lines.push('--- STDERR ---');
            lines.push(result.stderr);
            lines.push('');
        }
        if (!result.stdout && !result.stderr) {
            lines.push('(No output)');
        }
        return lines.join('\n');
    }
}
//# sourceMappingURL=ShellExecuteTool.js.map