/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * UI component types
 */

export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: string;
    foreground: string;
    muted: string;
    accent: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      small: string;
      medium: string;
      large: string;
    };
  };
  spacing: {
    small: number;
    medium: number;
    large: number;
  };
}

export interface UIState {
  loading: boolean;
  error?: string;
  theme: string;
  screen: string;
}

/**
 * UI Configuration interface
 */
export interface UIConfiguration {
  theme: 'auto' | 'dark' | 'light';
  animations: boolean;
  verbose: boolean;
  showTimestamps: boolean;
  showProviderInfo: boolean;
  maxHistoryItems: number;
  autoScroll: boolean;
  confirmActions: boolean;
  shortcuts: {
    exit: string;
    help: string;
    refresh: string;
    clear: string;
    save: string;
  };
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  layout: {
    sidebar: boolean;
    statusBar: boolean;
    toolbar: boolean;
    minimap: boolean;
  };
  editor: {
    fontSize: number;
    fontFamily: string;
    lineNumbers: boolean;
    wordWrap: boolean;
    tabSize: number;
    insertSpaces: boolean;
  };
  terminal: {
    fontSize: number;
    fontFamily: string;
    cursorStyle: 'block' | 'line' | 'underline';
    scrollback: number;
  };
}
