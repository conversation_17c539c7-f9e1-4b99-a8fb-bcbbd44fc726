/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { blobLikeSchema } from "../../types/blobs.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FilesApiRoutesUploadFileMultiPartBodyParams = {
  /**
   * The File object (not file name) to be uploaded.
   *
   * @remarks
   *  To upload a file and specify a custom file name you should format your request as such:
   *  ```bash
   *  file=@path/to/your/file.jsonl;filename=custom_name.jsonl
   *  ```
   *  Otherwise, you can just keep the original file name:
   *  ```bash
   *  file=@path/to/your/file.jsonl
   *  ```
   */
  file: components.FileT | Blob;
  purpose?: components.FilePurpose | undefined;
};

/** @internal */
export const FilesApiRoutesUploadFileMultiPartBodyParams$inboundSchema:
  z.ZodType<
    FilesApiRoutesUploadFileMultiPartBodyParams,
    z.ZodTypeDef,
    unknown
  > = z.object({
    file: components.FileT$inboundSchema,
    purpose: components.FilePurpose$inboundSchema.optional(),
  });

/** @internal */
export type FilesApiRoutesUploadFileMultiPartBodyParams$Outbound = {
  file: components.FileT$Outbound | Blob;
  purpose?: string | undefined;
};

/** @internal */
export const FilesApiRoutesUploadFileMultiPartBodyParams$outboundSchema:
  z.ZodType<
    FilesApiRoutesUploadFileMultiPartBodyParams$Outbound,
    z.ZodTypeDef,
    FilesApiRoutesUploadFileMultiPartBodyParams
  > = z.object({
    file: components.FileT$outboundSchema.or(blobLikeSchema),
    purpose: components.FilePurpose$outboundSchema.optional(),
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilesApiRoutesUploadFileMultiPartBodyParams$ {
  /** @deprecated use `FilesApiRoutesUploadFileMultiPartBodyParams$inboundSchema` instead. */
  export const inboundSchema =
    FilesApiRoutesUploadFileMultiPartBodyParams$inboundSchema;
  /** @deprecated use `FilesApiRoutesUploadFileMultiPartBodyParams$outboundSchema` instead. */
  export const outboundSchema =
    FilesApiRoutesUploadFileMultiPartBodyParams$outboundSchema;
  /** @deprecated use `FilesApiRoutesUploadFileMultiPartBodyParams$Outbound` instead. */
  export type Outbound = FilesApiRoutesUploadFileMultiPartBodyParams$Outbound;
}

export function filesApiRoutesUploadFileMultiPartBodyParamsToJSON(
  filesApiRoutesUploadFileMultiPartBodyParams:
    FilesApiRoutesUploadFileMultiPartBodyParams,
): string {
  return JSON.stringify(
    FilesApiRoutesUploadFileMultiPartBodyParams$outboundSchema.parse(
      filesApiRoutesUploadFileMultiPartBodyParams,
    ),
  );
}

export function filesApiRoutesUploadFileMultiPartBodyParamsFromJSON(
  jsonString: string,
): SafeParseResult<
  FilesApiRoutesUploadFileMultiPartBodyParams,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      FilesApiRoutesUploadFileMultiPartBodyParams$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'FilesApiRoutesUploadFileMultiPartBodyParams' from JSON`,
  );
}
