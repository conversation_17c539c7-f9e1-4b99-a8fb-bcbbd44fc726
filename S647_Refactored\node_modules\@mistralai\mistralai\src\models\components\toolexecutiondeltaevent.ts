/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BuiltInConnectors,
  BuiltInConnectors$inboundSchema,
  BuiltInConnectors$outboundSchema,
} from "./builtinconnectors.js";

export const ToolExecutionDeltaEventType = {
  ToolExecutionDelta: "tool.execution.delta",
} as const;
export type ToolExecutionDeltaEventType = ClosedEnum<
  typeof ToolExecutionDeltaEventType
>;

export type ToolExecutionDeltaEvent = {
  type?: ToolExecutionDeltaEventType | undefined;
  createdAt?: Date | undefined;
  outputIndex?: number | undefined;
  id: string;
  name: BuiltInConnectors;
  arguments: string;
};

/** @internal */
export const ToolExecutionDeltaEventType$inboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionDeltaEventType
> = z.nativeEnum(ToolExecutionDeltaEventType);

/** @internal */
export const ToolExecutionDeltaEventType$outboundSchema: z.ZodNativeEnum<
  typeof ToolExecutionDeltaEventType
> = ToolExecutionDeltaEventType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionDeltaEventType$ {
  /** @deprecated use `ToolExecutionDeltaEventType$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionDeltaEventType$inboundSchema;
  /** @deprecated use `ToolExecutionDeltaEventType$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionDeltaEventType$outboundSchema;
}

/** @internal */
export const ToolExecutionDeltaEvent$inboundSchema: z.ZodType<
  ToolExecutionDeltaEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ToolExecutionDeltaEventType$inboundSchema.default(
    "tool.execution.delta",
  ),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  output_index: z.number().int().default(0),
  id: z.string(),
  name: BuiltInConnectors$inboundSchema,
  arguments: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "output_index": "outputIndex",
  });
});

/** @internal */
export type ToolExecutionDeltaEvent$Outbound = {
  type: string;
  created_at?: string | undefined;
  output_index: number;
  id: string;
  name: string;
  arguments: string;
};

/** @internal */
export const ToolExecutionDeltaEvent$outboundSchema: z.ZodType<
  ToolExecutionDeltaEvent$Outbound,
  z.ZodTypeDef,
  ToolExecutionDeltaEvent
> = z.object({
  type: ToolExecutionDeltaEventType$outboundSchema.default(
    "tool.execution.delta",
  ),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  outputIndex: z.number().int().default(0),
  id: z.string(),
  name: BuiltInConnectors$outboundSchema,
  arguments: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    outputIndex: "output_index",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolExecutionDeltaEvent$ {
  /** @deprecated use `ToolExecutionDeltaEvent$inboundSchema` instead. */
  export const inboundSchema = ToolExecutionDeltaEvent$inboundSchema;
  /** @deprecated use `ToolExecutionDeltaEvent$outboundSchema` instead. */
  export const outboundSchema = ToolExecutionDeltaEvent$outboundSchema;
  /** @deprecated use `ToolExecutionDeltaEvent$Outbound` instead. */
  export type Outbound = ToolExecutionDeltaEvent$Outbound;
}

export function toolExecutionDeltaEventToJSON(
  toolExecutionDeltaEvent: ToolExecutionDeltaEvent,
): string {
  return JSON.stringify(
    ToolExecutionDeltaEvent$outboundSchema.parse(toolExecutionDeltaEvent),
  );
}

export function toolExecutionDeltaEventFromJSON(
  jsonString: string,
): SafeParseResult<ToolExecutionDeltaEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolExecutionDeltaEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolExecutionDeltaEvent' from JSON`,
  );
}
