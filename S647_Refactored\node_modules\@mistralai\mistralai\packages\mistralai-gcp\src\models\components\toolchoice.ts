/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FunctionName,
  FunctionName$inboundSchema,
  FunctionName$Outbound,
  FunctionName$outboundSchema,
} from "./functionname.js";
import {
  ToolTypes,
  ToolTypes$inboundSchema,
  ToolTypes$outboundSchema,
} from "./tooltypes.js";

/**
 * ToolChoice is either a ToolChoiceEnum or a ToolChoice
 */
export type ToolChoice = {
  type?: ToolTypes | undefined;
  /**
   * this restriction of `Function` is used to select a specific function to call
   */
  function: FunctionName;
};

/** @internal */
export const ToolChoice$inboundSchema: z.ZodType<
  ToolChoice,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ToolTypes$inboundSchema.optional(),
  function: FunctionName$inboundSchema,
});

/** @internal */
export type ToolChoice$Outbound = {
  type?: string | undefined;
  function: FunctionName$Outbound;
};

/** @internal */
export const ToolChoice$outboundSchema: z.ZodType<
  ToolChoice$Outbound,
  z.ZodTypeDef,
  ToolChoice
> = z.object({
  type: ToolTypes$outboundSchema.optional(),
  function: FunctionName$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolChoice$ {
  /** @deprecated use `ToolChoice$inboundSchema` instead. */
  export const inboundSchema = ToolChoice$inboundSchema;
  /** @deprecated use `ToolChoice$outboundSchema` instead. */
  export const outboundSchema = ToolChoice$outboundSchema;
  /** @deprecated use `ToolChoice$Outbound` instead. */
  export type Outbound = ToolChoice$Outbound;
}

export function toolChoiceToJSON(toolChoice: ToolChoice): string {
  return JSON.stringify(ToolChoice$outboundSchema.parse(toolChoice));
}

export function toolChoiceFromJSON(
  jsonString: string,
): SafeParseResult<ToolChoice, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ToolChoice$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ToolChoice' from JSON`,
  );
}
