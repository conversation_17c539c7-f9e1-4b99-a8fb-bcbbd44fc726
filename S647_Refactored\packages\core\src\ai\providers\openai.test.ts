/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { OpenAIProviderConfig, ProviderId } from '@inkbytefo/s647-shared';
import { OpenAIProvider } from './openai.js';

// Mock OpenAI SDK
const mockOpenAI = {
  models: {
    list: vi.fn(),
  },
  chat: {
    completions: {
      create: vi.fn(),
    },
  },
  embeddings: {
    create: vi.fn(),
  },
};

vi.mock('openai', () => ({
  OpenAI: vi.fn(() => mockOpenAI),
}));

describe('OpenAIProvider', () => {
  let provider: OpenAIProvider;
  let config: OpenAIProviderConfig;
  const providerId = 'test-openai' as ProviderId;

  beforeEach(() => {
    config = {
      type: 'openai',
      apiKey: 'test-api-key',
      baseUrl: 'https://api.openai.com/v1',
      timeout: 30000,
      retries: 3,
    };
    provider = new OpenAIProvider(providerId, config);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create provider with correct properties', () => {
      expect(provider.id).toBe(providerId);
      expect(provider.type).toBe('openai');
      expect(provider.config).toBe(config);
      expect(provider.status).toBe('unknown');
    });
  });

  describe('initialize', () => {
    it('should initialize successfully with valid config', async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });

      const result = await provider.initialize();

      expect(result.success).toBe(true);
      expect(provider.status).toBe('available');
    });

    it('should fail initialization with invalid config', async () => {
      const invalidConfig = { ...config, apiKey: '' };
      const invalidProvider = new OpenAIProvider(providerId, invalidConfig);

      const result = await invalidProvider.initialize();

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
      expect(invalidProvider.status).toBe('error');
    });

    it('should handle API errors during initialization', async () => {
      mockOpenAI.models.list.mockRejectedValue(new Error('API Error'));

      const result = await provider.initialize();

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
      expect(provider.status).toBe('error');
    });
  });

  describe('isAvailable', () => {
    it('should return false when not initialized', async () => {
      const available = await provider.isAvailable();
      expect(available).toBe(false);
    });

    it('should return true when API is accessible', async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();

      const available = await provider.isAvailable();
      expect(available).toBe(true);
    });

    it('should return false when API is not accessible', async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();

      mockOpenAI.models.list.mockRejectedValue(new Error('API Error'));
      const available = await provider.isAvailable();
      expect(available).toBe(false);
    });
  });

  describe('getModels', () => {
    beforeEach(async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();
    });

    it('should return models list', async () => {
      const mockModels = [
        { id: 'gpt-4', object: 'model' },
        { id: 'gpt-3.5-turbo', object: 'model' },
      ];
      mockOpenAI.models.list.mockResolvedValue({ data: mockModels });

      const result = await provider.getModels();

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toHaveLength(2);
        expect(result.data[0].id).toBe('gpt-4');
        expect(result.data[0].provider).toBe('openai');
        expect(result.data[0].capabilities.chat).toBe(true);
      }
    });

    it('should handle API errors', async () => {
      mockOpenAI.models.list.mockRejectedValue(new Error('API Error'));

      const result = await provider.getModels();

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
    });

    it('should fail when not initialized', async () => {
      const uninitializedProvider = new OpenAIProvider(providerId, config);

      const result = await uninitializedProvider.getModels();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('not initialized');
    });
  });

  describe('createChatCompletion', () => {
    beforeEach(async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();
    });

    it('should create chat completion successfully', async () => {
      const mockResponse = {
        id: 'chatcmpl-123',
        object: 'chat.completion',
        created: **********,
        model: 'gpt-4',
        choices: [{
          index: 0,
          message: { role: 'assistant', content: 'Hello!' },
          finish_reason: 'stop',
        }],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
        },
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const request = {
        model: 'gpt-4',
        messages: [{ role: 'user' as const, content: 'Hello' }],
        temperature: 0.7,
        maxTokens: 100,
      };

      const result = await provider.createChatCompletion(request);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.id).toBe('chatcmpl-123');
        expect(result.data.choices[0].message.content).toBe('Hello!');
      }
    });

    it('should handle API errors', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'));

      const request = {
        model: 'gpt-4',
        messages: [{ role: 'user' as const, content: 'Hello' }],
      };

      const result = await provider.createChatCompletion(request);

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
    });

    it('should fail when not initialized', async () => {
      const uninitializedProvider = new OpenAIProvider(providerId, config);

      const request = {
        model: 'gpt-4',
        messages: [{ role: 'user' as const, content: 'Hello' }],
      };

      const result = await uninitializedProvider.createChatCompletion(request);

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('not initialized');
    });
  });

  describe('createChatCompletionStream', () => {
    beforeEach(async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();
    });

    it('should create streaming chat completion successfully', async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield {
            id: 'chatcmpl-123',
            object: 'chat.completion.chunk',
            created: **********,
            model: 'gpt-4',
            choices: [{
              index: 0,
              delta: { content: 'Hello' },
            }],
          };
        },
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(mockStream);

      const request = {
        model: 'gpt-4',
        messages: [{ role: 'user' as const, content: 'Hello' }],
        stream: true,
      };

      const result = await provider.createChatCompletionStream(request);

      expect(result.success).toBe(true);
      if (result.success) {
        const chunks = [];
        for await (const chunk of result.data) {
          chunks.push(chunk);
        }
        expect(chunks).toHaveLength(1);
        expect(chunks[0].choices[0].delta.content).toBe('Hello');
      }
    });

    it('should handle API errors', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'));

      const request = {
        model: 'gpt-4',
        messages: [{ role: 'user' as const, content: 'Hello' }],
        stream: true,
      };

      const result = await provider.createChatCompletionStream(request);

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
    });
  });

  describe('createEmbedding', () => {
    beforeEach(async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();
    });

    it('should create embedding successfully', async () => {
      const mockResponse = {
        object: 'list',
        data: [{
          object: 'embedding',
          index: 0,
          embedding: [0.1, 0.2, 0.3],
        }],
        model: 'text-embedding-ada-002',
        usage: {
          prompt_tokens: 5,
          total_tokens: 5,
        },
      };
      mockOpenAI.embeddings.create.mockResolvedValue(mockResponse);

      const request = {
        model: 'text-embedding-ada-002',
        input: 'Hello world',
      };

      const result = await provider.createEmbedding(request);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.data[0].embedding).toEqual([0.1, 0.2, 0.3]);
      }
    });

    it('should handle API errors', async () => {
      mockOpenAI.embeddings.create.mockRejectedValue(new Error('API Error'));

      const request = {
        model: 'text-embedding-ada-002',
        input: 'Hello world',
      };

      const result = await provider.createEmbedding(request);

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
    });
  });

  describe('countTokens', () => {
    beforeEach(async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });
      await provider.initialize();
    });

    it('should count tokens for string input', async () => {
      const result = await provider.countTokens('Hello world');

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toBeGreaterThan(0);
      }
    });

    it('should count tokens for message array input', async () => {
      const messages = [
        { role: 'user' as const, content: 'Hello' },
        { role: 'assistant' as const, content: 'Hi there!' },
      ];

      const result = await provider.countTokens(messages);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toBeGreaterThan(0);
      }
    });
  });

  describe('dispose', () => {
    it('should dispose provider successfully', async () => {
      await provider.dispose();
      expect(provider.status).toBe('unavailable');
    });
  });
});
