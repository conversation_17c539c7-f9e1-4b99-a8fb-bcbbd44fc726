/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Tool, ToolId, ToolParams, ToolContext, ToolResult, ToolHelp, AsyncResult, JsonValue } from '@inkbytefo/s647-shared';
import { z } from 'zod';
/**
 * Base implementation for all tools
 */
export declare abstract class BaseTool implements Tool {
    readonly id: ToolId;
    readonly name: string;
    readonly description: string;
    readonly version: string;
    readonly category: string;
    readonly schema: JsonValue;
    protected readonly parameterSchema: z.ZodSchema;
    constructor(id: ToolId, name: string, description: string, version: string, category: string, parameterSchema: z.ZodSchema, schema?: JsonValue);
    /**
     * Execute the tool with given parameters
     */
    abstract execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    /**
     * Validate tool parameters
     */
    validate(params: ToolParams): {
        valid: boolean;
        errors: string[];
    };
    /**
     * Get help information for the tool
     */
    abstract getHelp(): ToolHelp;
    /**
     * Generate JSON schema from Zod schema
     */
    protected generateSchema(): JsonValue;
    /**
     * Handle errors consistently
     */
    protected handleError(error: unknown): Error;
    /**
     * Create a successful result
     */
    protected success<T>(data: T): AsyncResult<T>;
    /**
     * Create an error result
     */
    protected error(error: Error | string): AsyncResult<never>;
    /**
     * Validate and parse parameters
     */
    protected validateAndParse<T>(params: ToolParams): Promise<T>;
}
//# sourceMappingURL=BaseTool.d.ts.map