/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default logging configuration
 */
export declare const DEFAULT_LOGGING: {
    level: "info";
    format: "text";
    output: "console";
    structured: boolean;
    includeTimestamp: boolean;
    includeLevel: boolean;
    includeSource: boolean;
    maxFileSize: number;
    maxFiles: number;
    datePattern: string;
};
/**
 * Logging configurations for different environments
 */
export declare const LOGGING_CONFIGS: {
    development: {
        level: "debug";
        includeSource: boolean;
        structured: boolean;
        format: "text";
        output: "console";
        includeTimestamp: boolean;
        includeLevel: boolean;
        maxFileSize: number;
        maxFiles: number;
        datePattern: string;
    };
    production: {
        level: "warn";
        format: "json";
        structured: boolean;
        includeSource: boolean;
        output: "console";
        includeTimestamp: boolean;
        includeLevel: boolean;
        maxFileSize: number;
        maxFiles: number;
        datePattern: string;
    };
    test: {
        level: "error";
        output: "file";
        structured: boolean;
        format: "text";
        includeTimestamp: boolean;
        includeLevel: boolean;
        includeSource: boolean;
        maxFileSize: number;
        maxFiles: number;
        datePattern: string;
    };
};
/**
 * Get logging configuration for environment
 */
export declare function getLoggingConfigForEnvironment(env: 'development' | 'production' | 'test'): {
    level: "debug";
    includeSource: boolean;
    structured: boolean;
    format: "text";
    output: "console";
    includeTimestamp: boolean;
    includeLevel: boolean;
    maxFileSize: number;
    maxFiles: number;
    datePattern: string;
} | {
    level: "warn";
    format: "json";
    structured: boolean;
    includeSource: boolean;
    output: "console";
    includeTimestamp: boolean;
    includeLevel: boolean;
    maxFileSize: number;
    maxFiles: number;
    datePattern: string;
} | {
    level: "error";
    output: "file";
    structured: boolean;
    format: "text";
    includeTimestamp: boolean;
    includeLevel: boolean;
    includeSource: boolean;
    maxFileSize: number;
    maxFiles: number;
    datePattern: string;
};
/**
 * Log level priorities
 */
export declare const LOG_LEVELS: {
    readonly debug: 0;
    readonly info: 1;
    readonly warn: 2;
    readonly error: 3;
};
/**
 * Check if log level should be logged
 */
export declare function shouldLog(currentLevel: keyof typeof LOG_LEVELS, messageLevel: keyof typeof LOG_LEVELS): boolean;
//# sourceMappingURL=logging.d.ts.map