/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Common utility types used across the application
 */

/**
 * Make all properties optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Make specific properties required
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Make specific properties optional
 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Extract the value type from a Promise
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T;

/**
 * Create a union of all possible paths through an object
 */
export type Paths<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? K | `${K}.${Paths<T[K]>}`
          : K
        : never;
    }[keyof T]
  : never;

/**
 * Get the type of a nested property by path
 */
export type PathValue<T, P extends Paths<T>> = P extends `${infer K}.${infer Rest}`
  ? K extends keyof T
    ? Rest extends Paths<T[K]>
      ? PathValue<T[K], Rest>
      : never
    : never
  : P extends keyof T
  ? T[P]
  : never;

/**
 * Result type for operations that can succeed or fail
 */
export type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

/**
 * Extended result type with warnings support
 */
export type ExtendedResult<T, E = Error> =
  | { success: true; data: T; warnings?: string[] }
  | { success: false; error: E; warnings?: string[] };

/**
 * Async result type
 */
export type AsyncResult<T, E = Error> = Promise<Result<T, E>>;

/**
 * Extended async result type with warnings support
 */
export type ExtendedAsyncResult<T, E = Error> = Promise<ExtendedResult<T, E>>;

/**
 * Event handler type
 */
export type EventHandler<T = unknown> = (event: T) => void | Promise<void>;

/**
 * Disposable resource interface
 */
export interface Disposable {
  dispose(): void | Promise<void>;
}

/**
 * Logger interface
 */
export interface Logger {
  debug(message: string, ...args: unknown[]): void;
  info(message: string, ...args: unknown[]): void;
  warn(message: string, ...args: unknown[]): void;
  error(message: string, ...args: unknown[]): void;
}

/**
 * Serializable types
 */
export type Serializable = 
  | string 
  | number 
  | boolean 
  | null 
  | undefined
  | Serializable[] 
  | { [key: string]: Serializable };

/**
 * JSON-compatible types
 */
export type JsonValue = 
  | string 
  | number 
  | boolean 
  | null 
  | JsonValue[] 
  | { [key: string]: JsonValue };

/**
 * Brand type for creating nominal types
 */
export type Brand<T, B> = T & { __brand: B };

/**
 * ID types
 */
export type UserId = Brand<string, 'UserId'>;
export type SessionId = Brand<string, 'SessionId'>;
export type RequestId = Brand<string, 'RequestId'>;
export type ToolId = string;
export type ProviderId = Brand<string, 'ProviderId'>;

/**
 * Timestamp type
 */
export type Timestamp = Brand<number, 'Timestamp'>;

/**
 * File path type
 */
export type FilePath = Brand<string, 'FilePath'>;

/**
 * URL type
 */
export type Url = Brand<string, 'Url'>;

/**
 * Version type
 */
export type Version = Brand<string, 'Version'>;

/**
 * Environment type
 */
export type Environment = 'development' | 'production' | 'test';

/**
 * Log level type
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Status type
 */
export type Status = 'idle' | 'loading' | 'success' | 'error';

/**
 * Priority type
 */
export type Priority = 'low' | 'medium' | 'high' | 'critical';

/**
 * Theme mode type
 */
export type ThemeMode = 'light' | 'dark' | 'auto';

/**
 * Language code type (ISO 639-1)
 */
export type LanguageCode = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'zh' | 'ja' | 'ko';

/**
 * Currency code type (ISO 4217)
 */
export type CurrencyCode = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY' | 'KRW';

/**
 * Country code type (ISO 3166-1 alpha-2)
 */
export type CountryCode = 'US' | 'GB' | 'DE' | 'FR' | 'IT' | 'ES' | 'PT' | 'RU' | 'CN' | 'JP' | 'KR';

/**
 * MIME type
 */
export type MimeType = 
  | 'text/plain'
  | 'text/html'
  | 'text/css'
  | 'text/javascript'
  | 'application/json'
  | 'application/xml'
  | 'application/pdf'
  | 'image/jpeg'
  | 'image/png'
  | 'image/gif'
  | 'image/svg+xml'
  | 'audio/mpeg'
  | 'video/mp4'
  | string;

/**
 * HTTP method type
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

/**
 * HTTP status code type
 */
export type HttpStatusCode = 
  | 200 | 201 | 202 | 204
  | 300 | 301 | 302 | 304
  | 400 | 401 | 403 | 404 | 409 | 422 | 429
  | 500 | 501 | 502 | 503 | 504;

/**
 * Pagination info
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

/**
 * Sort order
 */
export type SortOrder = 'asc' | 'desc';

/**
 * Sort criteria
 */
export interface SortCriteria<T> {
  field: keyof T;
  order: SortOrder;
}

/**
 * Filter criteria
 */
export interface FilterCriteria<T> {
  field: keyof T;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith';
  value: unknown;
}

/**
 * Search criteria
 */
export interface SearchCriteria<T> {
  query?: string;
  filters?: FilterCriteria<T>[];
  sort?: SortCriteria<T>[];
  pagination?: {
    page: number;
    limit: number;
  };
}
