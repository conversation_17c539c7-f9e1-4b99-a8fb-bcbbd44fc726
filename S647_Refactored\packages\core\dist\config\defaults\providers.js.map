{"version": 3, "file": "providers.js", "sourceRoot": "", "sources": ["../../../src/config/defaults/providers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAmC;IAC/D,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KAEX;IAED,SAAS,EAAE;QACT,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KAEX;IAED,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KACX;IAED,OAAO,EAAE;QACP,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KACX;IAED,UAAU,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,8BAA8B;QACvC,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KACX;IAED,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,QAAQ;QAClB,UAAU,EAAE,eAAe;KAC5B;IAED,KAAK,EAAE;QACL,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,wBAAwB;QAClC,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KACX;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,wBAAwB,CAAC,IAAY;IACnD,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,0BAA0B;IACxC,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CACzE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,OAAO,EAAE;QACP,IAAI,EAAE,QAAiB;QACvB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,CAAC;KACX;IACD,GAAG,iBAAiB;CACrB,CAAC"}