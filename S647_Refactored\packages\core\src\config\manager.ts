/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { promises as fs } from 'fs';
import { join, dirname } from 'path';

import type { Configuration, AsyncResult, ExtendedAsyncResult } from '@inkbytefo/s647-shared';
import { ConfigurationSchema } from '@inkbytefo/s647-shared';
import { ConfigLoaderRegistry } from './loaders/registry.js';
import type { ConfigLoaderOptions } from './loaders/types.js';

/**
 * Configuration manager
 * Handles loading, saving, and validation of configuration
 */
export class ConfigurationManager {
  private readonly loaderRegistry: ConfigLoaderRegistry;
  private cachedConfig?: Configuration | undefined;
  private configPath?: string | undefined;

  constructor() {
    this.loaderRegistry = new ConfigLoaderRegistry();
  }

  /**
   * Load configuration from all sources
   */
  public async load(options?: ConfigLoaderOptions): ExtendedAsyncResult<Configuration> {
    try {
      const result = await this.loaderRegistry.loadConfiguration(options);

      if (!result.success || !result.config) {
        return {
          success: false,
          error: result.error || new Error('Failed to load configuration'),
        };
      }

      // Validate the merged configuration
      const validationResult = await this.validate(result.config);
      if (!validationResult.success) {
        return {
          success: false,
          error: validationResult.error || new Error('Configuration validation failed'),
        };
      }

      // Cache the loaded configuration
      this.cachedConfig = result.config as Configuration;

      return {
        success: true,
        data: this.cachedConfig,
        ...(result.warnings && { warnings: result.warnings }),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error loading configuration'),
      };
    }
  }

  /**
   * Save configuration to file
   */
  public async save(config?: Configuration, path?: string): AsyncResult<void> {
    try {
      const configToSave = config || this.cachedConfig;
      if (!configToSave) {
        return {
          success: false,
          error: new Error('No configuration to save'),
        };
      }

      // Validate configuration before saving
      const validationResult = await this.validate(configToSave);
      if (!validationResult.success) {
        return {
          success: false,
          error: validationResult.error || new Error('Configuration validation failed'),
        };
      }

      // Determine save path
      const savePath = path || this.configPath || this.getDefaultConfigPath();

      // Ensure directory exists
      await this.ensureDirectoryExists(dirname(savePath));

      // Save configuration
      const configJson = JSON.stringify(configToSave, null, 2);
      await fs.writeFile(savePath, configJson, 'utf-8');

      // Update cached config and path
      this.cachedConfig = configToSave;
      this.configPath = savePath;

      return { success: true, data: undefined };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error saving configuration'),
      };
    }
  }

  /**
   * Validate configuration against schema
   */
  public async validate(config?: Partial<Configuration>): AsyncResult<boolean> {
    try {
      const configToValidate = config || this.cachedConfig;
      if (!configToValidate) {
        return {
          success: false,
          error: new Error('No configuration to validate'),
        };
      }

      // Validate using Zod schema
      const result = ConfigurationSchema.safeParse(configToValidate);

      if (!result.success) {
        const errorMessages = result.error.errors.map(err =>
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');

        return {
          success: false,
          error: new Error(`Configuration validation failed: ${errorMessages}`),
        };
      }

      return { success: true, data: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error validating configuration'),
      };
    }
  }

  /**
   * Get cached configuration
   */
  public getCached(): Configuration | undefined {
    return this.cachedConfig;
  }

  /**
   * Clear cached configuration
   */
  public clearCache(): void {
    this.cachedConfig = undefined;
    this.configPath = undefined;
  }

  /**
   * Get default configuration file path
   */
  private getDefaultConfigPath(): string {
    return join(process.cwd(), '.s647', 'config.json');
  }

  /**
   * Ensure directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      // Ignore error if directory already exists
      if ((error as any)?.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  /**
   * Get configuration loader registry
   */
  public getLoaderRegistry(): ConfigLoaderRegistry {
    return this.loaderRegistry;
  }

  /**
   * Reload configuration from sources
   */
  public async reload(options?: ConfigLoaderOptions): AsyncResult<Configuration> {
    this.clearCache();
    return this.load(options);
  }
}
