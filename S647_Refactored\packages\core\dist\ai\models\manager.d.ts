/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ModelInfo, AsyncResult } from '@inkbytefo/s647-shared';
import type { ModelManager as IModelManager } from '../interfaces/model.js';
/**
 * Model manager implementation
 */
export declare class ModelManager implements IModelManager {
    private models;
    getAvailableModels(): AsyncResult<ModelInfo[]>;
    getModelInfo(modelId: string): AsyncResult<ModelInfo>;
    validateModel(modelId: string): AsyncResult<boolean>;
    registerModel(model: ModelInfo): void;
    unregisterModel(modelId: string): void;
}
//# sourceMappingURL=manager.d.ts.map