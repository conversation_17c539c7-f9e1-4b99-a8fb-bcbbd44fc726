{"version": 3, "file": "WebFetchTool.js", "sourceRoot": "", "sources": ["../../../../src/tools/implementations/web/WebFetchTool.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAQ1C,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAElD,MAAM,cAAc,GAAG,CAAC,CAAC,MAAM,CAAC;IAC9B,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;IAC9C,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;IAC1G,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;IACzE,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;IAC5E,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC;IACzF,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,uBAAuB,CAAC;IACvF,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,gCAAgC,CAAC;IAC5F,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,gCAAgC,CAAC;CACnG,CAAC,CAAC;AAIH;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,QAAQ;IACxC;QACE,KAAK,CACH,WAAW,EACX,WAAW,EACX,8BAA8B,EAC9B,OAAO,EACP,KAAK,EACL,cAAc,CACf,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAClB,MAAkB,EAClB,OAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,GAClF,MAAM,IAAI,CAAC,gBAAgB,CAAiB,MAAM,CAAC,CAAC;YAEtD,sCAAsC;YACtC,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;YAEhE,wBAAwB;YACxB,MAAM,YAAY,GAAgB;gBAChC,MAAM;gBACN,OAAO,EAAE;oBACP,YAAY,EAAE,gBAAgB;oBAC9B,GAAG,OAAO;iBACX;gBACD,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBAChE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;gBAC/C,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B,CAAC;YAEF,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;YAEhD,gBAAgB;YAChB,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,sBAAsB;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC7D,IAAI,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,OAAO,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,KAAK,CAAC,uBAAuB,aAAa,gBAAgB,OAAO,SAAS,CAAC,CAAC;YAC1F,CAAC;YAED,uBAAuB;YACvB,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YAC/D,IAAI,OAAe,CAAC;YAEpB,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACnC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAEhC,kCAAkC;gBAClC,IAAI,OAAO,CAAC,MAAM,GAAG,OAAO,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,uBAAuB,OAAO,CAAC,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAC;gBAC3F,CAAC;gBAED,sCAAsC;gBACtC,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBACrD,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;YAEjE,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,YAAY;gBACrB,QAAQ,EAAE;oBACR,GAAG;oBACH,MAAM;oBACN,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,WAAW;oBACX,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAC/C,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,QAAQ,EAAE,QAAQ,CAAC,GAAG;iBACvB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAiB,MAAM,CAAC,CAAC;YACtF,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,YAAY,aAAa,CAAC,CAAC;YACnE,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,OAAO;YACL,WAAW,EAAE,sFAAsF;YACnG,UAAU,EAAE;gBACV,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+CAA+C;oBAC5D,QAAQ,EAAE,IAAI;iBACf;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,mDAAmD;oBAChE,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,yCAAyC;oBACtD,QAAQ,EAAE,KAAK;iBAChB;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,oCAAoC;oBACjD,QAAQ,EAAE,KAAK;iBAChB;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,iCAAiC;oBAC9C,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD,eAAe,EAAE;oBACf,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,kCAAkC;oBAC/C,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,IAAI;iBACd;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,sCAAsC;oBACnD,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,IAAI;iBACd;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,gCAAgC;oBAC7C,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,OAAO;iBACjB;aACF;YACD,QAAQ,EAAE;gBACR;oBACE,WAAW,EAAE,kBAAkB;oBAC/B,UAAU,EAAE;wBACV,GAAG,EAAE,qBAAqB;qBAC3B;iBACF;gBACD;oBACE,WAAW,EAAE,qBAAqB;oBAClC,UAAU,EAAE;wBACV,GAAG,EAAE,8BAA8B;wBACnC,OAAO,EAAE,EAAE,QAAQ,EAAE,kBAAkB,EAAE;qBAC1C;iBACF;gBACD;oBACE,WAAW,EAAE,qBAAqB;oBAClC,UAAU,EAAE;wBACV,GAAG,EAAE,gCAAgC;wBACrC,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;wBAC/C,IAAI,EAAE,kBAAkB;qBACzB;iBACF;gBACD;oBACE,WAAW,EAAE,kDAAkD;oBAC/D,UAAU,EAAE;wBACV,GAAG,EAAE,gCAAgC;wBACrC,OAAO,EAAE,KAAK;wBACd,WAAW,EAAE,KAAK;qBACnB;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,IAAI,CAAC;YACH,OAAO,UAAU,CAAC,IAAI,EAAE;gBACtB,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE;oBACT,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;oBAChD,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;oBACnC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE;oBACtC,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;iBACtC;aACF,CAAC,CAAC;QACL,CAAC;QAAC,MAAM,CAAC;YACP,oCAAoC;YACpC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAkB,EAAE,OAAe,EAAE,WAAmB;QAC7E,MAAM,KAAK,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAErC,KAAK,CAAC,IAAI,CAAC,QAAQ,WAAW,EAAE,CAAC,CAAC;QAClC,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,cAAc,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3C,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QAChE,KAAK,CAAC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC;QACjF,KAAK,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC;QACtD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEf,wBAAwB;QACxB,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QACtF,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,eAAe,CAAC,OAAgB;QACtC,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;CACF"}