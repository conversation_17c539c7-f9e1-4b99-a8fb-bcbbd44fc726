#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

/**
 * Start the S647 CLI application
 */
async function startApp() {
  console.log('🚀 Starting S647 Refactored...\n');

  // Check if build exists
  const cliDistPath = join(projectRoot, 'packages', 'cli', 'dist', 'index.js');
  
  if (!existsSync(cliDistPath)) {
    console.log('📦 Build not found, building first...');
    
    // Build the project
    const buildProcess = spawn('npm', ['run', 'build'], {
      cwd: projectRoot,
      stdio: 'inherit',
      shell: true
    });

    await new Promise((resolve, reject) => {
      buildProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Build completed successfully!\n');
          resolve();
        } else {
          console.error('❌ Build failed!');
          reject(new Error(`Build process exited with code ${code}`));
        }
      });
    });
  }

  // Start the CLI application
  console.log('🎯 Launching S647 CLI...\n');
  
  const cliProcess = spawn('node', [`"${cliDistPath}"`, ...process.argv.slice(2)], {
    cwd: projectRoot,
    stdio: 'inherit',
    shell: true
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n👋 Shutting down S647...');
    cliProcess.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down S647...');
    cliProcess.kill('SIGTERM');
    process.exit(0);
  });

  cliProcess.on('close', (code) => {
    if (code !== 0) {
      console.error(`\n❌ S647 CLI exited with code ${code}`);
      process.exit(code);
    }
    console.log('\n👋 S647 CLI stopped');
    process.exit(0);
  });
}

// Run the start script
startApp().catch((error) => {
  console.error('❌ Failed to start S647:', error.message);
  process.exit(1);
});
