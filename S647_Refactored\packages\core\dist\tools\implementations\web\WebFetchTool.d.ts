/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON>l<PERSON><PERSON>ms, ToolContext, ToolResult, Tool<PERSON>elp, AsyncResult } from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';
/**
 * Tool for fetching web content
 */
export declare class WebFetchTool extends BaseTool {
    constructor();
    execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    getHelp(): ToolHelp;
    private extractTextFromHtml;
    private formatResponse;
    private headersToObject;
}
//# sourceMappingURL=WebFetchTool.d.ts.map