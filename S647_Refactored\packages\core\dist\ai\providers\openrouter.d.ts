/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { OpenRouterProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * OpenRouter provider implementation
 * OpenRouter is OpenAI-compatible, so we use the OpenAI SDK with custom base URL
 */
export declare class OpenRouterProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: OpenRouterProviderConfig);
    get openrouterConfig(): OpenRouterProviderConfig;
    /**
     * Initialize the OpenRouter provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models from OpenRouter
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion (OpenAI-compatible)
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion (OpenAI-compatible)
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings (OpenAI-compatible)
     */
    createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
}
//# sourceMappingURL=openrouter.d.ts.map