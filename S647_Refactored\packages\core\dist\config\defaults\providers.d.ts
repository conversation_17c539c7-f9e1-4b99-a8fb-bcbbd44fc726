/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ProviderConfig } from '@inkbytefo/s647-shared';
/**
 * Default provider configurations
 */
export declare const DEFAULT_PROVIDERS: Record<string, ProviderConfig>;
/**
 * Get default provider configuration by type
 */
export declare function getDefaultProviderConfig(type: string): ProviderConfig | undefined;
/**
 * Get all enabled default providers
 */
export declare function getEnabledDefaultProviders(): Record<string, ProviderConfig>;
/**
 * Default provider configuration with a default provider
 */
export declare const DEFAULT_PROVIDER_CONFIG: {
    default: {
        type: "openai";
        enabled: boolean;
        timeout: number;
        retries: number;
    };
};
//# sourceMappingURL=providers.d.ts.map