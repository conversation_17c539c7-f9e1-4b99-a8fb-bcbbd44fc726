/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  MessageInputContentChunks,
  MessageInputContentChunks$inboundSchema,
  MessageInputContentChunks$Outbound,
  MessageInputContentChunks$outboundSchema,
} from "./messageinputcontentchunks.js";

export const ObjectT = {
  Entry: "entry",
} as const;
export type ObjectT = ClosedEnum<typeof ObjectT>;

export const MessageInputEntryType = {
  MessageInput: "message.input",
} as const;
export type MessageInputEntryType = ClosedEnum<typeof MessageInputEntryType>;

export const MessageInputEntryRole = {
  Assistant: "assistant",
  User: "user",
} as const;
export type MessageInputEntryRole = ClosedEnum<typeof MessageInputEntryRole>;

export type MessageInputEntryContent =
  | string
  | Array<MessageInputContentChunks>;

/**
 * Representation of an input message inside the conversation.
 */
export type MessageInputEntry = {
  object?: ObjectT | undefined;
  type?: MessageInputEntryType | undefined;
  createdAt?: Date | undefined;
  completedAt?: Date | null | undefined;
  id?: string | undefined;
  role: MessageInputEntryRole;
  content: string | Array<MessageInputContentChunks>;
  prefix?: boolean | undefined;
};

/** @internal */
export const ObjectT$inboundSchema: z.ZodNativeEnum<typeof ObjectT> = z
  .nativeEnum(ObjectT);

/** @internal */
export const ObjectT$outboundSchema: z.ZodNativeEnum<typeof ObjectT> =
  ObjectT$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ObjectT$ {
  /** @deprecated use `ObjectT$inboundSchema` instead. */
  export const inboundSchema = ObjectT$inboundSchema;
  /** @deprecated use `ObjectT$outboundSchema` instead. */
  export const outboundSchema = ObjectT$outboundSchema;
}

/** @internal */
export const MessageInputEntryType$inboundSchema: z.ZodNativeEnum<
  typeof MessageInputEntryType
> = z.nativeEnum(MessageInputEntryType);

/** @internal */
export const MessageInputEntryType$outboundSchema: z.ZodNativeEnum<
  typeof MessageInputEntryType
> = MessageInputEntryType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageInputEntryType$ {
  /** @deprecated use `MessageInputEntryType$inboundSchema` instead. */
  export const inboundSchema = MessageInputEntryType$inboundSchema;
  /** @deprecated use `MessageInputEntryType$outboundSchema` instead. */
  export const outboundSchema = MessageInputEntryType$outboundSchema;
}

/** @internal */
export const MessageInputEntryRole$inboundSchema: z.ZodNativeEnum<
  typeof MessageInputEntryRole
> = z.nativeEnum(MessageInputEntryRole);

/** @internal */
export const MessageInputEntryRole$outboundSchema: z.ZodNativeEnum<
  typeof MessageInputEntryRole
> = MessageInputEntryRole$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageInputEntryRole$ {
  /** @deprecated use `MessageInputEntryRole$inboundSchema` instead. */
  export const inboundSchema = MessageInputEntryRole$inboundSchema;
  /** @deprecated use `MessageInputEntryRole$outboundSchema` instead. */
  export const outboundSchema = MessageInputEntryRole$outboundSchema;
}

/** @internal */
export const MessageInputEntryContent$inboundSchema: z.ZodType<
  MessageInputEntryContent,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(MessageInputContentChunks$inboundSchema)]);

/** @internal */
export type MessageInputEntryContent$Outbound =
  | string
  | Array<MessageInputContentChunks$Outbound>;

/** @internal */
export const MessageInputEntryContent$outboundSchema: z.ZodType<
  MessageInputEntryContent$Outbound,
  z.ZodTypeDef,
  MessageInputEntryContent
> = z.union([z.string(), z.array(MessageInputContentChunks$outboundSchema)]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageInputEntryContent$ {
  /** @deprecated use `MessageInputEntryContent$inboundSchema` instead. */
  export const inboundSchema = MessageInputEntryContent$inboundSchema;
  /** @deprecated use `MessageInputEntryContent$outboundSchema` instead. */
  export const outboundSchema = MessageInputEntryContent$outboundSchema;
  /** @deprecated use `MessageInputEntryContent$Outbound` instead. */
  export type Outbound = MessageInputEntryContent$Outbound;
}

export function messageInputEntryContentToJSON(
  messageInputEntryContent: MessageInputEntryContent,
): string {
  return JSON.stringify(
    MessageInputEntryContent$outboundSchema.parse(messageInputEntryContent),
  );
}

export function messageInputEntryContentFromJSON(
  jsonString: string,
): SafeParseResult<MessageInputEntryContent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageInputEntryContent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageInputEntryContent' from JSON`,
  );
}

/** @internal */
export const MessageInputEntry$inboundSchema: z.ZodType<
  MessageInputEntry,
  z.ZodTypeDef,
  unknown
> = z.object({
  object: ObjectT$inboundSchema.default("entry"),
  type: MessageInputEntryType$inboundSchema.default("message.input"),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v))
    .optional(),
  completed_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  id: z.string().optional(),
  role: MessageInputEntryRole$inboundSchema,
  content: z.union([
    z.string(),
    z.array(MessageInputContentChunks$inboundSchema),
  ]),
  prefix: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "completed_at": "completedAt",
  });
});

/** @internal */
export type MessageInputEntry$Outbound = {
  object: string;
  type: string;
  created_at?: string | undefined;
  completed_at?: string | null | undefined;
  id?: string | undefined;
  role: string;
  content: string | Array<MessageInputContentChunks$Outbound>;
  prefix: boolean;
};

/** @internal */
export const MessageInputEntry$outboundSchema: z.ZodType<
  MessageInputEntry$Outbound,
  z.ZodTypeDef,
  MessageInputEntry
> = z.object({
  object: ObjectT$outboundSchema.default("entry"),
  type: MessageInputEntryType$outboundSchema.default("message.input"),
  createdAt: z.date().transform(v => v.toISOString()).optional(),
  completedAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  id: z.string().optional(),
  role: MessageInputEntryRole$outboundSchema,
  content: z.union([
    z.string(),
    z.array(MessageInputContentChunks$outboundSchema),
  ]),
  prefix: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    completedAt: "completed_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MessageInputEntry$ {
  /** @deprecated use `MessageInputEntry$inboundSchema` instead. */
  export const inboundSchema = MessageInputEntry$inboundSchema;
  /** @deprecated use `MessageInputEntry$outboundSchema` instead. */
  export const outboundSchema = MessageInputEntry$outboundSchema;
  /** @deprecated use `MessageInputEntry$Outbound` instead. */
  export type Outbound = MessageInputEntry$Outbound;
}

export function messageInputEntryToJSON(
  messageInputEntry: MessageInputEntry,
): string {
  return JSON.stringify(
    MessageInputEntry$outboundSchema.parse(messageInputEntry),
  );
}

export function messageInputEntryFromJSON(
  jsonString: string,
): SafeParseResult<MessageInputEntry, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MessageInputEntry$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MessageInputEntry' from JSON`,
  );
}
