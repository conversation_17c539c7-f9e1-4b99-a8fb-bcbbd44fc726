{"version": 3, "file": "telemetry.js", "sourceRoot": "", "sources": ["../../../src/config/defaults/telemetry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC/B,OAAO,EAAE,KAAK;IACd,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,KAAK;IACpB,kBAAkB,EAAE,KAAK;IACzB,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,SAAS,EAAE,GAAG;IACd,aAAa,EAAE,KAAK,EAAE,aAAa;IACnC,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,IAAI,EAAE,WAAW;CAC9B,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC/B,WAAW,EAAE;QACX,GAAG,iBAAiB;QACpB,OAAO,EAAE,KAAK;QACd,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,KAAK;QACpB,kBAAkB,EAAE,KAAK;KAC1B;IAED,UAAU,EAAE;QACV,GAAG,iBAAiB;QACpB,OAAO,EAAE,KAAK,EAAE,8BAA8B;QAC9C,YAAY,EAAE,IAAI;QAClB,aAAa,EAAE,IAAI;QACnB,kBAAkB,EAAE,IAAI;QACxB,SAAS,EAAE,IAAI;KAChB;IAED,IAAI,EAAE;QACJ,GAAG,iBAAiB;QACpB,OAAO,EAAE,KAAK;QACd,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,KAAK;QACpB,kBAAkB,EAAE,KAAK;KAC1B;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,gCAAgC,CAAC,GAA0C;IACzF,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC;AACrD,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACnC,WAAW;IACX,UAAU;IACV,kBAAkB;IAClB,eAAe;IACf,WAAW;IACX,gBAAgB;IAChB,oBAAoB;CACZ,CAAC;AAEX;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAAa;IAChD,OAAO,qBAAqB,CAAC,QAAQ,CAAC,KAAY,CAAC,CAAC;AACtD,CAAC"}