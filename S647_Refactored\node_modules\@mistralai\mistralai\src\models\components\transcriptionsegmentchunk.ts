/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const Type = {
  TranscriptionSegment: "transcription_segment",
} as const;
export type Type = ClosedEnum<typeof Type>;

export type TranscriptionSegmentChunk = {
  text: string;
  start: number;
  end: number;
  type?: Type | undefined;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const Type$inboundSchema: z.ZodNativeEnum<typeof Type> = z.nativeEnum(
  Type,
);

/** @internal */
export const Type$outboundSchema: z.ZodNativeEnum<typeof Type> =
  Type$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Type$ {
  /** @deprecated use `Type$inboundSchema` instead. */
  export const inboundSchema = Type$inboundSchema;
  /** @deprecated use `Type$outboundSchema` instead. */
  export const outboundSchema = Type$outboundSchema;
}

/** @internal */
export const TranscriptionSegmentChunk$inboundSchema: z.ZodType<
  TranscriptionSegmentChunk,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    text: z.string(),
    start: z.number(),
    end: z.number(),
    type: Type$inboundSchema.default("transcription_segment"),
  }).catchall(z.any()),
  "additionalProperties",
  true,
);

/** @internal */
export type TranscriptionSegmentChunk$Outbound = {
  text: string;
  start: number;
  end: number;
  type: string;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const TranscriptionSegmentChunk$outboundSchema: z.ZodType<
  TranscriptionSegmentChunk$Outbound,
  z.ZodTypeDef,
  TranscriptionSegmentChunk
> = z.object({
  text: z.string(),
  start: z.number(),
  end: z.number(),
  type: Type$outboundSchema.default("transcription_segment"),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionSegmentChunk$ {
  /** @deprecated use `TranscriptionSegmentChunk$inboundSchema` instead. */
  export const inboundSchema = TranscriptionSegmentChunk$inboundSchema;
  /** @deprecated use `TranscriptionSegmentChunk$outboundSchema` instead. */
  export const outboundSchema = TranscriptionSegmentChunk$outboundSchema;
  /** @deprecated use `TranscriptionSegmentChunk$Outbound` instead. */
  export type Outbound = TranscriptionSegmentChunk$Outbound;
}

export function transcriptionSegmentChunkToJSON(
  transcriptionSegmentChunk: TranscriptionSegmentChunk,
): string {
  return JSON.stringify(
    TranscriptionSegmentChunk$outboundSchema.parse(transcriptionSegmentChunk),
  );
}

export function transcriptionSegmentChunkFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionSegmentChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionSegmentChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionSegmentChunk' from JSON`,
  );
}
