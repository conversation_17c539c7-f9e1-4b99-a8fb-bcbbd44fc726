/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FilesApiRoutesGetSignedUrlRequest = {
  fileId: string;
  /**
   * Number of hours before the url becomes invalid. Defaults to 24h
   */
  expiry?: number | undefined;
};

/** @internal */
export const FilesApiRoutesGetSignedUrlRequest$inboundSchema: z.ZodType<
  FilesApiRoutesGetSignedUrlRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  file_id: z.string(),
  expiry: z.number().int().default(24),
}).transform((v) => {
  return remap$(v, {
    "file_id": "fileId",
  });
});

/** @internal */
export type FilesApiRoutesGetSignedUrlRequest$Outbound = {
  file_id: string;
  expiry: number;
};

/** @internal */
export const FilesApiRoutesGetSignedUrlRequest$outboundSchema: z.ZodType<
  FilesApiRoutesGetSignedUrlRequest$Outbound,
  z.ZodTypeDef,
  FilesApiRoutesGetSignedUrlRequest
> = z.object({
  fileId: z.string(),
  expiry: z.number().int().default(24),
}).transform((v) => {
  return remap$(v, {
    fileId: "file_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilesApiRoutesGetSignedUrlRequest$ {
  /** @deprecated use `FilesApiRoutesGetSignedUrlRequest$inboundSchema` instead. */
  export const inboundSchema = FilesApiRoutesGetSignedUrlRequest$inboundSchema;
  /** @deprecated use `FilesApiRoutesGetSignedUrlRequest$outboundSchema` instead. */
  export const outboundSchema =
    FilesApiRoutesGetSignedUrlRequest$outboundSchema;
  /** @deprecated use `FilesApiRoutesGetSignedUrlRequest$Outbound` instead. */
  export type Outbound = FilesApiRoutesGetSignedUrlRequest$Outbound;
}

export function filesApiRoutesGetSignedUrlRequestToJSON(
  filesApiRoutesGetSignedUrlRequest: FilesApiRoutesGetSignedUrlRequest,
): string {
  return JSON.stringify(
    FilesApiRoutesGetSignedUrlRequest$outboundSchema.parse(
      filesApiRoutesGetSignedUrlRequest,
    ),
  );
}

export function filesApiRoutesGetSignedUrlRequestFromJSON(
  jsonString: string,
): SafeParseResult<FilesApiRoutesGetSignedUrlRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FilesApiRoutesGetSignedUrlRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FilesApiRoutesGetSignedUrlRequest' from JSON`,
  );
}
