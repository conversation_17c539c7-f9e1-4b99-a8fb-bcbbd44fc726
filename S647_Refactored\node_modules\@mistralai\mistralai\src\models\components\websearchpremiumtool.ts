/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const WebSearchPremiumToolType = {
  WebSearchPremium: "web_search_premium",
} as const;
export type WebSearchPremiumToolType = ClosedEnum<
  typeof WebSearchPremiumToolType
>;

export type WebSearchPremiumTool = {
  type?: WebSearchPremiumToolType | undefined;
};

/** @internal */
export const WebSearchPremiumToolType$inboundSchema: z.ZodNativeEnum<
  typeof WebSearchPremiumToolType
> = z.nativeEnum(WebSearchPremiumToolType);

/** @internal */
export const WebSearchPremiumToolType$outboundSchema: z.ZodNativeEnum<
  typeof WebSearchPremiumToolType
> = WebSearchPremiumToolType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WebSearchPremiumToolType$ {
  /** @deprecated use `WebSearchPremiumToolType$inboundSchema` instead. */
  export const inboundSchema = WebSearchPremiumToolType$inboundSchema;
  /** @deprecated use `WebSearchPremiumToolType$outboundSchema` instead. */
  export const outboundSchema = WebSearchPremiumToolType$outboundSchema;
}

/** @internal */
export const WebSearchPremiumTool$inboundSchema: z.ZodType<
  WebSearchPremiumTool,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: WebSearchPremiumToolType$inboundSchema.default("web_search_premium"),
});

/** @internal */
export type WebSearchPremiumTool$Outbound = {
  type: string;
};

/** @internal */
export const WebSearchPremiumTool$outboundSchema: z.ZodType<
  WebSearchPremiumTool$Outbound,
  z.ZodTypeDef,
  WebSearchPremiumTool
> = z.object({
  type: WebSearchPremiumToolType$outboundSchema.default("web_search_premium"),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WebSearchPremiumTool$ {
  /** @deprecated use `WebSearchPremiumTool$inboundSchema` instead. */
  export const inboundSchema = WebSearchPremiumTool$inboundSchema;
  /** @deprecated use `WebSearchPremiumTool$outboundSchema` instead. */
  export const outboundSchema = WebSearchPremiumTool$outboundSchema;
  /** @deprecated use `WebSearchPremiumTool$Outbound` instead. */
  export type Outbound = WebSearchPremiumTool$Outbound;
}

export function webSearchPremiumToolToJSON(
  webSearchPremiumTool: WebSearchPremiumTool,
): string {
  return JSON.stringify(
    WebSearchPremiumTool$outboundSchema.parse(webSearchPremiumTool),
  );
}

export function webSearchPremiumToolFromJSON(
  jsonString: string,
): SafeParseResult<WebSearchPremiumTool, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => WebSearchPremiumTool$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'WebSearchPremiumTool' from JSON`,
  );
}
