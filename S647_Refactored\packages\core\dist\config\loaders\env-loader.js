/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Environment variable configuration loader
 * Loads configuration from environment variables
 */
export class EnvironmentConfigLoader {
    name = 'environment';
    priority = 75; // High priority
    prefix;
    constructor(prefix = 'S647_') {
        this.prefix = prefix;
    }
    /**
     * Load configuration from environment variables
     */
    async load(options) {
        try {
            const envPrefix = options?.envPrefix || this.prefix;
            const config = this.parseEnvironmentVariables(envPrefix);
            if (Object.keys(config).length === 0) {
                return {
                    success: false,
                    error: new Error('No environment variables found with prefix: ' + envPrefix),
                };
            }
            return {
                success: true,
                config,
                source: {
                    type: 'environment',
                    priority: this.priority,
                    timestamp: Date.now(),
                    metadata: {
                        loader: this.name,
                        prefix: envPrefix,
                        variableCount: Object.keys(config).length,
                    },
                },
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error : new Error('Unknown error loading environment config'),
            };
        }
    }
    /**
     * Check if this loader can load configuration
     */
    async canLoad(options) {
        const envPrefix = options?.envPrefix || this.prefix;
        return this.hasEnvironmentVariables(envPrefix);
    }
    /**
     * Parse environment variables into configuration object
     */
    parseEnvironmentVariables(prefix) {
        const config = {};
        for (const [key, value] of Object.entries(process.env)) {
            if (!key.startsWith(prefix) || !value) {
                continue;
            }
            // Remove prefix and convert to lowercase
            const configKey = key.slice(prefix.length).toLowerCase();
            // Parse nested keys (e.g., S647_PROVIDERS_OPENAI_API_KEY -> providers.openai.apiKey)
            const keyPath = this.parseKeyPath(configKey);
            this.setNestedValue(config, keyPath, this.parseValue(value));
        }
        return config;
    }
    /**
     * Parse key path from environment variable name
     */
    parseKeyPath(key) {
        return key.split('_').map(part => {
            // Convert snake_case to camelCase
            return part.replace(/([a-z])([A-Z])/g, '$1$2').toLowerCase();
        });
    }
    /**
     * Set nested value in object
     */
    setNestedValue(obj, path, value) {
        let current = obj;
        for (let i = 0; i < path.length - 1; i++) {
            const key = path[i];
            if (!key)
                continue;
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        const lastKey = path[path.length - 1];
        if (lastKey) {
            current[lastKey] = value;
        }
    }
    /**
     * Parse environment variable value
     */
    parseValue(value) {
        // Try to parse as JSON first
        try {
            return JSON.parse(value);
        }
        catch {
            // Fall back to string parsing
            const lowerValue = value.toLowerCase();
            // Boolean values
            if (lowerValue === 'true')
                return true;
            if (lowerValue === 'false')
                return false;
            // Number values
            if (/^\d+$/.test(value))
                return parseInt(value, 10);
            if (/^\d+\.\d+$/.test(value))
                return parseFloat(value);
            // Array values (comma-separated)
            if (value.includes(',')) {
                return value.split(',').map(item => item.trim());
            }
            // Default to string
            return value;
        }
    }
    /**
     * Check if environment variables with prefix exist
     */
    hasEnvironmentVariables(prefix) {
        return Object.keys(process.env).some(key => key.startsWith(prefix));
    }
}
//# sourceMappingURL=env-loader.js.map