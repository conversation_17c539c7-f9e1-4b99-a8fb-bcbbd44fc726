/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsDeleteV1Request = {
  libraryId: string;
  documentId: string;
};

/** @internal */
export const LibrariesDocumentsDeleteV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsDeleteV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  document_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "document_id": "documentId",
  });
});

/** @internal */
export type LibrariesDocumentsDeleteV1Request$Outbound = {
  library_id: string;
  document_id: string;
};

/** @internal */
export const LibrariesDocumentsDeleteV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsDeleteV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsDeleteV1Request
> = z.object({
  libraryId: z.string(),
  documentId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    documentId: "document_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsDeleteV1Request$ {
  /** @deprecated use `LibrariesDocumentsDeleteV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesDocumentsDeleteV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsDeleteV1Request$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsDeleteV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsDeleteV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsDeleteV1Request$Outbound;
}

export function librariesDocumentsDeleteV1RequestToJSON(
  librariesDocumentsDeleteV1Request: LibrariesDocumentsDeleteV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsDeleteV1Request$outboundSchema.parse(
      librariesDocumentsDeleteV1Request,
    ),
  );
}

export function librariesDocumentsDeleteV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesDocumentsDeleteV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesDocumentsDeleteV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesDocumentsDeleteV1Request' from JSON`,
  );
}
