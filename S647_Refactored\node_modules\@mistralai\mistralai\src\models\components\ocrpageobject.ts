/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  OCRImageObject,
  OCRImageObject$inboundSchema,
  OCRImageObject$Outbound,
  OCRImageObject$outboundSchema,
} from "./ocrimageobject.js";
import {
  OCRPageDimensions,
  OCRPageDimensions$inboundSchema,
  OCRPageDimensions$Outbound,
  OCRPageDimensions$outboundSchema,
} from "./ocrpagedimensions.js";

export type OCRPageObject = {
  /**
   * The page index in a pdf document starting from 0
   */
  index: number;
  /**
   * The markdown string response of the page
   */
  markdown: string;
  /**
   * List of all extracted images in the page
   */
  images: Array<OCRImageObject>;
  /**
   * The dimensions of the PDF Page's screenshot image
   */
  dimensions: OCRPageDimensions | null;
};

/** @internal */
export const OCRPageObject$inboundSchema: z.ZodType<
  OCRPageObject,
  z.ZodTypeDef,
  unknown
> = z.object({
  index: z.number().int(),
  markdown: z.string(),
  images: z.array(OCRImageObject$inboundSchema),
  dimensions: z.nullable(OCRPageDimensions$inboundSchema),
});

/** @internal */
export type OCRPageObject$Outbound = {
  index: number;
  markdown: string;
  images: Array<OCRImageObject$Outbound>;
  dimensions: OCRPageDimensions$Outbound | null;
};

/** @internal */
export const OCRPageObject$outboundSchema: z.ZodType<
  OCRPageObject$Outbound,
  z.ZodTypeDef,
  OCRPageObject
> = z.object({
  index: z.number().int(),
  markdown: z.string(),
  images: z.array(OCRImageObject$outboundSchema),
  dimensions: z.nullable(OCRPageDimensions$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OCRPageObject$ {
  /** @deprecated use `OCRPageObject$inboundSchema` instead. */
  export const inboundSchema = OCRPageObject$inboundSchema;
  /** @deprecated use `OCRPageObject$outboundSchema` instead. */
  export const outboundSchema = OCRPageObject$outboundSchema;
  /** @deprecated use `OCRPageObject$Outbound` instead. */
  export type Outbound = OCRPageObject$Outbound;
}

export function ocrPageObjectToJSON(ocrPageObject: OCRPageObject): string {
  return JSON.stringify(OCRPageObject$outboundSchema.parse(ocrPageObject));
}

export function ocrPageObjectFromJSON(
  jsonString: string,
): SafeParseResult<OCRPageObject, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OCRPageObject$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OCRPageObject' from JSON`,
  );
}
