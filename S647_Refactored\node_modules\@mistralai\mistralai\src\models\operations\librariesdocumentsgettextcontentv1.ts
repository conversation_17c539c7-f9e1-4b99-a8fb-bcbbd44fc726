/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsGetTextContentV1Request = {
  libraryId: string;
  documentId: string;
};

/** @internal */
export const LibrariesDocumentsGetTextContentV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsGetTextContentV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  document_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "document_id": "documentId",
  });
});

/** @internal */
export type LibrariesDocumentsGetTextContentV1Request$Outbound = {
  library_id: string;
  document_id: string;
};

/** @internal */
export const LibrariesDocumentsGetTextContentV1Request$outboundSchema:
  z.ZodType<
    LibrariesDocumentsGetTextContentV1Request$Outbound,
    z.ZodTypeDef,
    LibrariesDocumentsGetTextContentV1Request
  > = z.object({
    libraryId: z.string(),
    documentId: z.string(),
  }).transform((v) => {
    return remap$(v, {
      libraryId: "library_id",
      documentId: "document_id",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsGetTextContentV1Request$ {
  /** @deprecated use `LibrariesDocumentsGetTextContentV1Request$inboundSchema` instead. */
  export const inboundSchema =
    LibrariesDocumentsGetTextContentV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsGetTextContentV1Request$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsGetTextContentV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsGetTextContentV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsGetTextContentV1Request$Outbound;
}

export function librariesDocumentsGetTextContentV1RequestToJSON(
  librariesDocumentsGetTextContentV1Request:
    LibrariesDocumentsGetTextContentV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsGetTextContentV1Request$outboundSchema.parse(
      librariesDocumentsGetTextContentV1Request,
    ),
  );
}

export function librariesDocumentsGetTextContentV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<
  LibrariesDocumentsGetTextContentV1Request,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      LibrariesDocumentsGetTextContentV1Request$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'LibrariesDocumentsGetTextContentV1Request' from JSON`,
  );
}
