/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConfigLoader, ConfigLoaderOptions, LoadResult } from './types.js';
/**
 * Configuration loader registry
 * Manages multiple configuration loaders and merges their results
 */
export declare class ConfigLoaderRegistry {
    private readonly loaders;
    constructor();
    /**
     * Register a configuration loader
     */
    register(loader: ConfigLoader): void;
    /**
     * Unregister a configuration loader
     */
    unregister(name: string): boolean;
    /**
     * Get a configuration loader by name
     */
    get(name: string): ConfigLoader | undefined;
    /**
     * Get all registered loaders
     */
    getAll(): ConfigLoader[];
    /**
     * Load configuration from all loaders and merge results
     */
    loadConfiguration(options?: ConfigLoaderOptions): Promise<LoadResult>;
    /**
     * Merge configurations from multiple sources
     */
    private mergeConfigurations;
    /**
     * Deep merge two configuration objects
     */
    private deepMerge;
    /**
     * Check if value is a plain object
     */
    private isObject;
    /**
     * Register default configuration loaders
     */
    private registerDefaultLoaders;
}
//# sourceMappingURL=registry.d.ts.map