{"version": 3, "file": "openrouter.js", "sourceRoot": "", "sources": ["../../../src/ai/providers/openrouter.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAcH,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAEzD;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,YAAY;IAC1C,MAAM,CAAM,CAAC,yBAAyB;IAE9C,YAAY,EAAc,EAAE,MAAgC;QAC1D,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACpB,CAAC;IAED,IAAW,gBAAgB;QACzB,OAAO,IAAI,CAAC,MAAkC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,oDAAoD;YACpD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE1C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;gBACvB,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;gBACpC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,8BAA8B;gBACxE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,KAAK;gBAC/C,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC;gBAC9C,cAAc,EAAE;oBACd,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,kBAAkB;oBACnE,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI,MAAM;oBACnD,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO;iBACjC;aACF,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAE5B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,sCAAsC;YACtC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjD,MAAM,MAAM,GAAgB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBAC7D,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,EAAE;gBACd,QAAQ,EAAE,YAAqB;gBAC/B,YAAY,EAAE;oBACZ,IAAI,EAAE,IAAI;oBACV,UAAU,EAAE,IAAI;oBAChB,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;oBACzC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;oBACjE,eAAe,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;oBAChD,SAAS,EAAE,IAAI;oBACf,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;iBACtE;gBACD,aAAa,EAAE,KAAK,CAAC,cAAc,IAAI,IAAI;gBAC3C,SAAS,EAAE,KAAK,CAAC,qBAAqB,IAAI,IAAI;gBAC9C,OAAO,EAAE;oBACP,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM;oBAC5B,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU;iBAClC;aACF,CAAC,CAAC,CAAC;YAEJ,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAC/B,OAA8B;QAE9B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,KAAK,EAAE,OAAO,CAAC,IAAI;gBACnB,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;gBAC3C,gBAAgB,EAAE,OAAO,CAAC,eAAe;gBACzC,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,KAAK;gBACb,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,UAAU;gBAC/B,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAkC,EAAE,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B,CACrC,OAA8B;QAE9B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,KAAK,EAAE,OAAO,CAAC,IAAI;gBACnB,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;gBAC3C,gBAAgB,EAAE,OAAO,CAAC,eAAe;gBACzC,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,UAAU;gBAC/B,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAA4C,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAC1B,OAAyB;QAEzB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACnD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAA6B,EAAE,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,KAA6B;QAE7B,IAAI,CAAC;YACH,8DAA8D;YAC9D,0CAA0C;YAC1C,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK,QAAQ;gBACpC,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEnF,2DAA2D;YAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;CACF"}