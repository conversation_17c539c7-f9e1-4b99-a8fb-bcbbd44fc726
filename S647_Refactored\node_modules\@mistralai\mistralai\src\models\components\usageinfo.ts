/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type UsageInfo = {
  promptTokens?: number | undefined;
  completionTokens?: number | undefined;
  totalTokens?: number | undefined;
  promptAudioSeconds?: number | null | undefined;
  additionalProperties?: { [k: string]: any };
};

/** @internal */
export const UsageInfo$inboundSchema: z.ZodType<
  UsageInfo,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    prompt_tokens: z.number().int().default(0),
    completion_tokens: z.number().int().default(0),
    total_tokens: z.number().int().default(0),
    prompt_audio_seconds: z.nullable(z.number().int()).optional(),
  }).catchall(z.any()),
  "additionalProperties",
  true,
).transform((v) => {
  return remap$(v, {
    "prompt_tokens": "promptTokens",
    "completion_tokens": "completionTokens",
    "total_tokens": "totalTokens",
    "prompt_audio_seconds": "promptAudioSeconds",
  });
});

/** @internal */
export type UsageInfo$Outbound = {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  prompt_audio_seconds?: number | null | undefined;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const UsageInfo$outboundSchema: z.ZodType<
  UsageInfo$Outbound,
  z.ZodTypeDef,
  UsageInfo
> = z.object({
  promptTokens: z.number().int().default(0),
  completionTokens: z.number().int().default(0),
  totalTokens: z.number().int().default(0),
  promptAudioSeconds: z.nullable(z.number().int()).optional(),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      promptTokens: "prompt_tokens",
      completionTokens: "completion_tokens",
      totalTokens: "total_tokens",
      promptAudioSeconds: "prompt_audio_seconds",
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UsageInfo$ {
  /** @deprecated use `UsageInfo$inboundSchema` instead. */
  export const inboundSchema = UsageInfo$inboundSchema;
  /** @deprecated use `UsageInfo$outboundSchema` instead. */
  export const outboundSchema = UsageInfo$outboundSchema;
  /** @deprecated use `UsageInfo$Outbound` instead. */
  export type Outbound = UsageInfo$Outbound;
}

export function usageInfoToJSON(usageInfo: UsageInfo): string {
  return JSON.stringify(UsageInfo$outboundSchema.parse(usageInfo));
}

export function usageInfoFromJSON(
  jsonString: string,
): SafeParseResult<UsageInfo, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UsageInfo$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UsageInfo' from JSON`,
  );
}
