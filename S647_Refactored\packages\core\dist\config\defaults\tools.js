/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default tool configurations
 */
export const DEFAULT_TOOLS = {
    enabled: ['file', 'git', 'web', 'shell'],
    file: {
        enabled: true,
        timeout: 10000,
        retries: 2,
        options: {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedExtensions: [
                '.js', '.ts', '.jsx', '.tsx', '.vue', '.svelte',
                '.py', '.rb', '.php', '.java', '.c', '.cpp', '.cs', '.go', '.rs',
                '.html', '.css', '.scss', '.sass', '.less',
                '.json', '.yaml', '.yml', '.xml', '.toml', '.ini',
                '.md', '.txt', '.log', '.env',
                '.sql', '.graphql', '.proto',
                '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
            ],
            excludePatterns: [
                'node_modules/**',
                '.git/**',
                'dist/**',
                'build/**',
                '*.min.js',
                '*.bundle.js',
                '.DS_Store',
                'Thumbs.db',
            ],
        },
    },
    git: {
        enabled: true,
        timeout: 15000,
        retries: 2,
        options: {
            maxCommits: 100,
            maxDiffLines: 1000,
            includeUntracked: false,
            includeBinary: false,
            defaultBranch: 'main',
        },
    },
    web: {
        enabled: true,
        timeout: 30000,
        retries: 3,
        options: {
            maxResponseSize: 5 * 1024 * 1024, // 5MB
            followRedirects: true,
            maxRedirects: 5,
            userAgent: 'S647-CLI/2.0.0',
            allowedDomains: [], // Empty means all domains allowed
            blockedDomains: [
                'localhost',
                '127.0.0.1',
                '0.0.0.0',
                '::1',
            ],
        },
    },
    shell: {
        enabled: true,
        timeout: 60000,
        retries: 1,
        options: {
            allowedCommands: [
                'ls', 'dir', 'pwd', 'cd', 'cat', 'head', 'tail', 'grep', 'find',
                'git', 'npm', 'yarn', 'pnpm', 'pip', 'cargo', 'go', 'mvn', 'gradle',
                'docker', 'kubectl', 'terraform', 'ansible',
                'curl', 'wget', 'ping', 'nslookup', 'dig',
                'ps', 'top', 'htop', 'df', 'du', 'free', 'uptime',
            ],
            blockedCommands: [
                'rm', 'del', 'rmdir', 'rd', 'format', 'fdisk',
                'sudo', 'su', 'chmod', 'chown', 'passwd',
                'shutdown', 'reboot', 'halt', 'poweroff',
                'kill', 'killall', 'pkill',
            ],
            workingDirectory: process.cwd(),
            inheritEnv: true,
            shell: process.platform === 'win32' ? 'cmd.exe' : '/bin/bash',
        },
    },
    memory: {
        enabled: true,
        timeout: 5000,
        retries: 2,
        options: {
            maxEntries: 1000,
            maxEntrySize: 1024 * 1024, // 1MB
            persistToDisk: true,
            compressionEnabled: true,
            encryptionEnabled: false,
        },
    },
};
/**
 * Get default tool configuration by name
 */
export function getDefaultToolConfig(name) {
    return DEFAULT_TOOLS[name];
}
/**
 * Get all enabled default tools
 */
export function getEnabledDefaultTools() {
    return DEFAULT_TOOLS.enabled;
}
/**
 * Check if tool is enabled by default
 */
export function isToolEnabledByDefault(name) {
    return DEFAULT_TOOLS.enabled.includes(name);
}
//# sourceMappingURL=tools.js.map