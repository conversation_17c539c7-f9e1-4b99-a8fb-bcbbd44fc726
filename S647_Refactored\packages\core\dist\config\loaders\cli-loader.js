/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * CLI arguments configuration loader
 * Loads configuration from command line arguments
 */
export class CliConfigLoader {
    name = 'cli';
    priority = 100; // Highest priority
    /**
     * Load configuration from CLI arguments
     */
    async load(options) {
        try {
            const args = options?.args || {};
            const config = this.parseCliArguments(args);
            if (Object.keys(config).length === 0) {
                return {
                    success: false,
                    error: new Error('No configuration found in CLI arguments'),
                };
            }
            return {
                success: true,
                config,
                source: {
                    type: 'cli',
                    priority: this.priority,
                    timestamp: Date.now(),
                    metadata: {
                        loader: this.name,
                        argumentCount: Object.keys(config).length,
                    },
                },
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error : new Error('Unknown error loading CLI config'),
            };
        }
    }
    /**
     * Check if this loader can load configuration
     */
    async canLoad(options) {
        const args = options?.args || {};
        return this.hasConfigArguments(args);
    }
    /**
     * Parse CLI arguments into configuration object
     */
    parseCliArguments(args) {
        const config = {};
        // Map CLI arguments to configuration properties
        const argMappings = {
            // Core settings
            'provider': 'defaultProvider',
            'default-provider': 'defaultProvider',
            'environment': 'environment',
            'env': 'environment',
            'profile': 'profile',
            // UI settings
            'theme': 'ui.theme',
            'no-animations': 'ui.animations',
            'verbose': 'ui.verbose',
            'quiet': 'ui.verbose',
            // Logging settings
            'log-level': 'logging.level',
            'log-format': 'logging.format',
            'log-output': 'logging.output',
            // Tool settings
            'tools': 'tools.enabled',
            'enable-tools': 'tools.enabled',
            'disable-tools': 'tools.enabled',
            // Provider-specific settings
            'api-key': 'providers.default.apiKey',
            'base-url': 'providers.default.baseUrl',
            'timeout': 'providers.default.timeout',
            // Telemetry settings
            'telemetry': 'telemetry.enabled',
            'no-telemetry': 'telemetry.enabled',
        };
        for (const [argKey, configPath] of Object.entries(argMappings)) {
            if (argKey in args) {
                let value = args[argKey];
                // Handle special cases
                if (argKey === 'no-animations') {
                    value = !value; // Invert boolean
                }
                else if (argKey === 'quiet') {
                    value = !value; // Invert boolean for verbose
                }
                else if (argKey === 'no-telemetry') {
                    value = !value; // Invert boolean
                }
                else if (argKey === 'tools' || argKey === 'enable-tools') {
                    value = Array.isArray(value) ? value : value.split(',').map((s) => s.trim());
                }
                else if (argKey === 'disable-tools') {
                    // Handle disabled tools by filtering from default enabled tools
                    const disabledTools = Array.isArray(value) ? value : value.split(',').map((s) => s.trim());
                    // This would need to be handled in the merge logic
                    continue;
                }
                this.setNestedValue(config, configPath.split('.'), value);
            }
        }
        // Handle provider-specific arguments
        this.parseProviderArguments(args, config);
        return config;
    }
    /**
     * Parse provider-specific CLI arguments
     */
    parseProviderArguments(args, config) {
        const providerPrefixes = ['openai', 'anthropic', 'google', 'mistral', 'openrouter', 'custom', 'local'];
        for (const prefix of providerPrefixes) {
            const providerConfig = {};
            // Look for provider-specific arguments
            for (const [key, value] of Object.entries(args)) {
                if (key.startsWith(`${prefix}-`)) {
                    const providerKey = key.slice(prefix.length + 1);
                    // Map common provider arguments
                    switch (providerKey) {
                        case 'api-key':
                            providerConfig.apiKey = value;
                            break;
                        case 'base-url':
                            providerConfig.baseUrl = value;
                            break;
                        case 'timeout':
                            providerConfig.timeout = parseInt(value, 10);
                            break;
                        case 'enabled':
                            providerConfig.enabled = value;
                            break;
                        default:
                            providerConfig[providerKey] = value;
                    }
                }
            }
            if (Object.keys(providerConfig).length > 0) {
                if (!config.providers)
                    config.providers = {};
                config.providers[prefix] = providerConfig;
            }
        }
    }
    /**
     * Set nested value in object
     */
    setNestedValue(obj, path, value) {
        let current = obj;
        for (let i = 0; i < path.length - 1; i++) {
            const key = path[i];
            if (!key)
                continue;
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        const lastKey = path[path.length - 1];
        if (lastKey) {
            current[lastKey] = value;
        }
    }
    /**
     * Check if CLI arguments contain configuration
     */
    hasConfigArguments(args) {
        const configKeys = [
            'provider', 'default-provider', 'environment', 'env', 'profile',
            'theme', 'no-animations', 'verbose', 'quiet',
            'log-level', 'log-format', 'log-output',
            'tools', 'enable-tools', 'disable-tools',
            'api-key', 'base-url', 'timeout',
            'telemetry', 'no-telemetry'
        ];
        return configKeys.some(key => key in args) ||
            Object.keys(args).some(key => key.includes('-api-key') || key.includes('-base-url'));
    }
}
//# sourceMappingURL=cli-loader.js.map