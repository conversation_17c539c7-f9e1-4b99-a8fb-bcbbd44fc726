/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON>lParams, ToolContext, ToolResult, Tool<PERSON>elp, AsyncResult } from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';
/**
 * Tool for reading file contents
 */
export declare class FileReadTool extends BaseTool {
    constructor();
    execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    getHelp(): ToolHelp;
    private resolvePath;
}
//# sourceMappingURL=FileReadTool.d.ts.map