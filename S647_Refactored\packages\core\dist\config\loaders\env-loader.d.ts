/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConfigLoader, ConfigLoaderOptions, LoadResult } from './types.js';
/**
 * Environment variable configuration loader
 * Loads configuration from environment variables
 */
export declare class EnvironmentConfigLoader implements ConfigLoader {
    readonly name = "environment";
    readonly priority = 75;
    private readonly prefix;
    constructor(prefix?: string);
    /**
     * Load configuration from environment variables
     */
    load(options?: ConfigLoaderOptions): Promise<LoadResult>;
    /**
     * Check if this loader can load configuration
     */
    canLoad(options?: ConfigLoaderOptions): Promise<boolean>;
    /**
     * Parse environment variables into configuration object
     */
    private parseEnvironmentVariables;
    /**
     * Parse key path from environment variable name
     */
    private parseKeyPath;
    /**
     * Set nested value in object
     */
    private setNestedValue;
    /**
     * Parse environment variable value
     */
    private parseValue;
    /**
     * Check if environment variables with prefix exist
     */
    private hasEnvironmentVariables;
}
//# sourceMappingURL=env-loader.d.ts.map