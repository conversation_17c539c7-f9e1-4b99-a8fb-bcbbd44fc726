/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default security configuration
 */
export declare const DEFAULT_SECURITY: {
    encryptApiKeys: boolean;
    encryptionAlgorithm: string;
    keyDerivationIterations: number;
    sessionTimeout: number;
    maxFailedAttempts: number;
    lockoutDuration: number;
    allowedOrigins: never[];
    blockedIPs: never[];
    rateLimiting: {
        enabled: boolean;
        maxRequests: number;
        windowMs: number;
    };
    contentSecurityPolicy: {
        enabled: boolean;
        directives: {
            'default-src': string[];
            'script-src': string[];
            'style-src': string[];
            'img-src': string[];
            'connect-src': string[];
        };
    };
};
/**
 * Security configurations for different environments
 */
export declare const SECURITY_CONFIGS: {
    development: {
        encryptApiKeys: boolean;
        sessionTimeout: number;
        maxFailedAttempts: number;
        rateLimiting: {
            enabled: boolean;
            maxRequests: number;
            windowMs: number;
        };
        encryptionAlgorithm: string;
        keyDerivationIterations: number;
        lockoutDuration: number;
        allowedOrigins: never[];
        blockedIPs: never[];
        contentSecurityPolicy: {
            enabled: boolean;
            directives: {
                'default-src': string[];
                'script-src': string[];
                'style-src': string[];
                'img-src': string[];
                'connect-src': string[];
            };
        };
    };
    production: {
        encryptApiKeys: boolean;
        sessionTimeout: number;
        maxFailedAttempts: number;
        rateLimiting: {
            enabled: boolean;
            maxRequests: number;
            windowMs: number;
        };
        encryptionAlgorithm: string;
        keyDerivationIterations: number;
        lockoutDuration: number;
        allowedOrigins: never[];
        blockedIPs: never[];
        contentSecurityPolicy: {
            enabled: boolean;
            directives: {
                'default-src': string[];
                'script-src': string[];
                'style-src': string[];
                'img-src': string[];
                'connect-src': string[];
            };
        };
    };
    test: {
        encryptApiKeys: boolean;
        sessionTimeout: number;
        maxFailedAttempts: number;
        rateLimiting: {
            enabled: boolean;
            maxRequests: number;
            windowMs: number;
        };
        encryptionAlgorithm: string;
        keyDerivationIterations: number;
        lockoutDuration: number;
        allowedOrigins: never[];
        blockedIPs: never[];
        contentSecurityPolicy: {
            enabled: boolean;
            directives: {
                'default-src': string[];
                'script-src': string[];
                'style-src': string[];
                'img-src': string[];
                'connect-src': string[];
            };
        };
    };
};
/**
 * Get security configuration for environment
 */
export declare function getSecurityConfigForEnvironment(env: 'development' | 'production' | 'test'): typeof DEFAULT_SECURITY;
/**
 * Validate API key format
 */
export declare function isValidApiKeyFormat(apiKey: string): boolean;
/**
 * Sanitize sensitive data for logging
 */
export declare function sanitizeForLogging(data: any): any;
//# sourceMappingURL=security.d.ts.map