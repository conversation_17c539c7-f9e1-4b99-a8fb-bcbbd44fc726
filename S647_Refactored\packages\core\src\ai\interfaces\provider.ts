/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  Provider,
  ProviderConfig,
  ProviderType,
  ProviderStatus,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  ProviderId,
  AsyncResult,
  ChatMessage,
} from '@inkbytefo/s647-shared';

/**
 * Abstract base class for AI providers
 */
export abstract class BaseProvider implements Provider {
  public readonly id: ProviderId;
  public readonly type: ProviderType;
  public readonly config: ProviderConfig;
  protected _status: ProviderStatus = 'unknown';

  constructor(id: ProviderId, config: ProviderConfig) {
    this.id = id;
    this.type = config.type;
    this.config = config;
  }

  public get status(): ProviderStatus {
    return this._status;
  }

  /**
   * Initialize the provider
   */
  public abstract initialize(): AsyncResult<void>;

  /**
   * Check if the provider is available
   */
  public abstract isAvailable(): Promise<boolean>;

  /**
   * Get available models
   */
  public abstract getModels(): AsyncResult<ModelInfo[]>;

  /**
   * Create a chat completion
   */
  public abstract createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse>;

  /**
   * Create a streaming chat completion
   */
  public abstract createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>>;

  /**
   * Create embeddings
   */
  public abstract createEmbedding(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse>;

  /**
   * Count tokens for a given input
   */
  public abstract countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number>;

  /**
   * Dispose of the provider
   */
  public async dispose(): Promise<void> {
    this._status = 'unavailable';
  }

  /**
   * Validate the provider configuration
   */
  protected validateConfig(): void {
    if (!this.config.apiKey && this.config.type !== 'local') {
      throw new Error(`API key is required for ${this.config.type} provider`);
    }
  }

  /**
   * Handle provider errors
   */
  protected handleError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }
    return new Error(String(error));
  }

  /**
   * Set provider status
   */
  protected setStatus(status: ProviderStatus): void {
    this._status = status;
  }
}

/**
 * Provider factory interface
 */
export interface IProviderFactory {
  /**
   * Create a provider instance
   */
  create(id: ProviderId, config: ProviderConfig): AsyncResult<Provider>;

  /**
   * Get supported provider types
   */
  getSupportedTypes(): ProviderType[];

  /**
   * Validate provider configuration
   */
  validateConfig(config: ProviderConfig): AsyncResult<void>;
}

/**
 * Provider registry interface
 */
export interface IProviderRegistry {
  /**
   * Register a provider
   */
  register(provider: Provider): void;

  /**
   * Unregister a provider
   */
  unregister(id: ProviderId): void;

  /**
   * Get a provider by ID
   */
  get(id: ProviderId): Provider | undefined;

  /**
   * Get all providers
   */
  getAll(): Provider[];

  /**
   * Get providers by type
   */
  getByType(type: ProviderType): Provider[];

  /**
   * Get available providers
   */
  getAvailable(): Provider[];

  /**
   * Find the best provider for a model
   */
  findBestProvider(modelId: string): Provider | undefined;

  /**
   * Dispose all providers
   */
  dispose(): Promise<void>;
}
