/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Provider, ProviderConfig, ProviderType, ProviderStatus, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, ProviderId, AsyncResult, ChatMessage } from '@inkbytefo/s647-shared';
/**
 * Abstract base class for AI providers
 */
export declare abstract class BaseProvider implements Provider {
    readonly id: ProviderId;
    readonly type: ProviderType;
    readonly config: ProviderConfig;
    protected _status: ProviderStatus;
    constructor(id: ProviderId, config: ProviderConfig);
    get status(): ProviderStatus;
    /**
     * Initialize the provider
     */
    abstract initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    abstract isAvailable(): Promise<boolean>;
    /**
     * Get available models
     */
    abstract getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion
     */
    abstract createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    abstract createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings
     */
    abstract createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    abstract countTokens(input: string | ChatMessage[]): AsyncResult<number>;
    /**
     * Dispose of the provider
     */
    dispose(): Promise<void>;
    /**
     * Validate the provider configuration
     */
    protected validateConfig(): void;
    /**
     * Handle provider errors
     */
    protected handleError(error: unknown): Error;
    /**
     * Set provider status
     */
    protected setStatus(status: ProviderStatus): void;
}
/**
 * Provider factory interface
 */
export interface IProviderFactory {
    /**
     * Create a provider instance
     */
    create(id: ProviderId, config: ProviderConfig): AsyncResult<Provider>;
    /**
     * Get supported provider types
     */
    getSupportedTypes(): ProviderType[];
    /**
     * Validate provider configuration
     */
    validateConfig(config: ProviderConfig): AsyncResult<void>;
}
/**
 * Provider registry interface
 */
export interface IProviderRegistry {
    /**
     * Register a provider
     */
    register(provider: Provider): void;
    /**
     * Unregister a provider
     */
    unregister(id: ProviderId): void;
    /**
     * Get a provider by ID
     */
    get(id: ProviderId): Provider | undefined;
    /**
     * Get all providers
     */
    getAll(): Provider[];
    /**
     * Get providers by type
     */
    getByType(type: ProviderType): Provider[];
    /**
     * Get available providers
     */
    getAvailable(): Provider[];
    /**
     * Find the best provider for a model
     */
    findBestProvider(modelId: string): Provider | undefined;
    /**
     * Dispose all providers
     */
    dispose(): Promise<void>;
}
//# sourceMappingURL=provider.d.ts.map