/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { LocalProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * Local provider implementation
 * Supports local model servers like Ollama, LocalAI, etc.
 */
export declare class LocalProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: LocalProviderConfig);
    get localConfig(): LocalProviderConfig;
    /**
     * Initialize the Local provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models from local server
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion (OpenAI-compatible)
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion (OpenAI-compatible)
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings (if supported by local server)
     */
    createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
}
//# sourceMappingURL=local.d.ts.map