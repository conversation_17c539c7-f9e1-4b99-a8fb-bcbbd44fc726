/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default provider configurations
 */
export const DEFAULT_PROVIDERS = {
    openai: {
        type: 'openai',
        enabled: true,
        timeout: 30000,
        retries: 3,
    },
    anthropic: {
        type: 'anthropic',
        enabled: true,
        timeout: 30000,
        retries: 3,
    },
    google: {
        type: 'google',
        enabled: true,
        timeout: 30000,
        retries: 3,
    },
    mistral: {
        type: 'mistral',
        enabled: true,
        timeout: 30000,
        retries: 3,
    },
    openrouter: {
        type: 'openrouter',
        enabled: false,
        baseUrl: 'https://openrouter.ai/api/v1',
        timeout: 30000,
        retries: 3,
    },
    custom: {
        type: 'custom',
        enabled: false,
        timeout: 30000,
        retries: 3,
        authType: 'bearer',
        authHeader: 'Authorization',
    },
    local: {
        type: 'local',
        enabled: false,
        endpoint: 'http://localhost:11434',
        timeout: 60000,
        retries: 2,
    },
};
/**
 * Get default provider configuration by type
 */
export function getDefaultProviderConfig(type) {
    return DEFAULT_PROVIDERS[type];
}
/**
 * Get all enabled default providers
 */
export function getEnabledDefaultProviders() {
    return Object.fromEntries(Object.entries(DEFAULT_PROVIDERS).filter(([, config]) => config.enabled));
}
/**
 * Default provider configuration with a default provider
 */
export const DEFAULT_PROVIDER_CONFIG = {
    default: {
        type: 'openai',
        enabled: true,
        timeout: 30000,
        retries: 3,
    },
    ...DEFAULT_PROVIDERS,
};
//# sourceMappingURL=providers.js.map