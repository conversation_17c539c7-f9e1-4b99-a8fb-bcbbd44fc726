/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  GoogleProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * Google Gemini provider implementation
 */
export class GoogleProvider extends BaseProvider {
  private client: any; // GoogleGenAI client instance

  constructor(id: ProviderId, config: GoogleProviderConfig) {
    super(id, config);
  }

  public get googleConfig(): GoogleProviderConfig {
    return this.config as GoogleProviderConfig;
  }

  /**
   * Initialize the Google provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();

      // Initialize Google GenAI client
      const { GoogleGenAI } = await import('@google/genai');

      if (!this.googleConfig.apiKey) {
        throw new Error('Google API key is required');
      }

      this.client = new GoogleGenAI({
        apiKey: this.googleConfig.apiKey,
        ...(this.googleConfig.baseUrl && { baseURL: this.googleConfig.baseUrl }),
        ...(this.googleConfig.headers && { defaultHeaders: this.googleConfig.headers }),
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Test with a simple generation
      const model = this.client.models.get('gemini-2.5-flash');
      await model.generateContent({
        contents: 'Hi',
        config: {
          maxOutputTokens: 1,
        },
      });

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available models
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      // Google doesn't have a models endpoint, so we return known models
      const models: ModelInfo[] = [
        {
          id: 'gemini-2.5-flash',
          name: 'Gemini 2.5 Flash',
          provider: 'google' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 1000000,
          maxTokens: 8192,
        },
        {
          id: 'gemini-2.5-pro',
          name: 'Gemini 2.5 Pro',
          provider: 'google' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 2000000,
          maxTokens: 8192,
        },
        {
          id: 'gemini-1.5-flash',
          name: 'Gemini 1.5 Flash',
          provider: 'google' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 1000000,
          maxTokens: 8192,
        },
        {
          id: 'gemini-1.5-pro',
          name: 'Gemini 1.5 Pro',
          provider: 'google' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 2000000,
          maxTokens: 8192,
        },
      ];

      return { success: true, data: models };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const model = this.client.models.get(request.model);
      const { contents, systemInstruction } = this.convertMessages(request.messages);

      const response = await model.generateContent({
        contents,
        ...(systemInstruction && { systemInstruction }),
        config: {
          maxOutputTokens: request.maxTokens,
          temperature: request.temperature,
          topP: request.topP,
          stopSequences: Array.isArray(request.stop) ? request.stop : request.stop ? [request.stop] : undefined,
        },
        tools: request.tools ? this.convertTools(request.tools) : undefined,
      });

      // Convert response to OpenAI format
      const convertedResponse: ChatCompletionResponse = {
        id: `chatcmpl-${Date.now()}`,
        object: 'chat.completion',
        created: Date.now(),
        model: request.model,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response.text || '',
          },
          finishReason: this.convertFinishReason(response.candidates?.[0]?.finishReason),
        }],
        usage: {
          promptTokens: response.usageMetadata?.promptTokenCount || 0,
          completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata?.totalTokenCount || 0,
        },
      };

      return { success: true, data: convertedResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const model = this.client.models.get(request.model);
      const { contents, systemInstruction } = this.convertMessages(request.messages);

      const stream = await model.generateContentStream({
        contents,
        ...(systemInstruction && { systemInstruction }),
        config: {
          maxOutputTokens: request.maxTokens,
          temperature: request.temperature,
          topP: request.topP,
          stopSequences: Array.isArray(request.stop) ? request.stop : request.stop ? [request.stop] : undefined,
        },
        tools: request.tools ? this.convertTools(request.tools) : undefined,
      });

      // Convert stream to OpenAI format
      const convertedStream = this.convertStream(stream, request.model);

      return { success: true, data: convertedStream };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings (not supported by Gemini chat models)
   */
  public async createEmbedding(
    _request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    return {
      success: false,
      error: new Error('Embeddings are not supported by Gemini chat models. Use text-embedding models instead.'),
    };
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const model = this.client.models.get('gemini-2.5-flash'); // Use fastest model for counting
      const { contents } = this.convertMessages(
        typeof input === 'string'
          ? [{ role: 'user', content: input }]
          : input
      );

      const response = await model.countTokens({ contents });
      return { success: true, data: response.totalTokens || 0 };
    } catch (error) {
      // Fallback to estimation
      const text = typeof input === 'string'
        ? input
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');

      const estimatedTokens = Math.ceil(text.length / 4); // Rough estimation
      return { success: true, data: estimatedTokens };
    }
  }

  /**
   * Convert messages to Google format
   */
  private convertMessages(messages: ChatMessage[]): { contents: any[]; systemInstruction?: string } {
    let systemInstruction: string | undefined;
    const contents: any[] = [];

    for (const message of messages) {
      if (message.role === 'system') {
        systemInstruction = typeof message.content === 'string' ? message.content : '';
      } else {
        contents.push({
          role: message.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: typeof message.content === 'string' ? message.content : '' }],
        });
      }
    }

    return {
      contents,
      ...(systemInstruction && { systemInstruction })
    };
  }

  /**
   * Convert tools to Google format
   */
  private convertTools(tools: any[]): any[] {
    return tools.map(tool => ({
      functionDeclarations: [{
        name: tool.function.name,
        description: tool.function.description,
        parameters: tool.function.parameters,
      }],
    }));
  }

  /**
   * Convert finish reason to OpenAI format
   */
  private convertFinishReason(finishReason: string | undefined): 'stop' | 'length' | 'function_call' | 'tool_calls' | 'content_filter' {
    switch (finishReason) {
      case 'STOP':
        return 'stop';
      case 'MAX_TOKENS':
        return 'length';
      case 'SAFETY':
        return 'content_filter';
      case 'RECITATION':
        return 'content_filter';
      default:
        return 'stop';
    }
  }

  /**
   * Convert Google stream to OpenAI format
   */
  private async* convertStream(stream: any, model: string): AsyncIterable<ChatCompletionChunk> {
    const messageId = `chatcmpl-${Date.now()}`;

    for await (const chunk of stream) {
      if (chunk.text) {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {
              role: 'assistant',
              content: chunk.text,
            },
          }],
        };
      }

      // Handle final chunk
      if (chunk.candidates?.[0]?.finishReason) {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {},
            finishReason: this.convertFinishReason(chunk.candidates[0].finishReason),
          }],
        };
      }
    }
  }
}
