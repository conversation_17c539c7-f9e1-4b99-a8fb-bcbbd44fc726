/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobsApiRoutesBatchCancelBatchJobRequest = {
  jobId: string;
};

/** @internal */
export const JobsApiRoutesBatchCancelBatchJobRequest$inboundSchema: z.ZodType<
  JobsApiRoutesBatchCancelBatchJobRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  job_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "job_id": "jobId",
  });
});

/** @internal */
export type JobsApiRoutesBatchCancelBatchJobRequest$Outbound = {
  job_id: string;
};

/** @internal */
export const JobsApiRoutesBatchCancelBatchJobRequest$outboundSchema: z.ZodType<
  JobsApiRoutesBatchCancelBatchJobRequest$Outbound,
  z.ZodTypeDef,
  JobsApiRoutesBatchCancelBatchJobRequest
> = z.object({
  jobId: z.string(),
}).transform((v) => {
  return remap$(v, {
    jobId: "job_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesBatchCancelBatchJobRequest$ {
  /** @deprecated use `JobsApiRoutesBatchCancelBatchJobRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesBatchCancelBatchJobRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesBatchCancelBatchJobRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesBatchCancelBatchJobRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesBatchCancelBatchJobRequest$Outbound` instead. */
  export type Outbound = JobsApiRoutesBatchCancelBatchJobRequest$Outbound;
}

export function jobsApiRoutesBatchCancelBatchJobRequestToJSON(
  jobsApiRoutesBatchCancelBatchJobRequest:
    JobsApiRoutesBatchCancelBatchJobRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesBatchCancelBatchJobRequest$outboundSchema.parse(
      jobsApiRoutesBatchCancelBatchJobRequest,
    ),
  );
}

export function jobsApiRoutesBatchCancelBatchJobRequestFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesBatchCancelBatchJobRequest,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesBatchCancelBatchJobRequest$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesBatchCancelBatchJobRequest' from JSON`,
  );
}
