/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  AnthropicProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * Anthropic provider implementation
 */
export class AnthropicProvider extends BaseProvider {
  private client: any; // Anthropic client instance
  
  constructor(id: ProviderId, config: AnthropicProviderConfig) {
    super(id, config);
  }

  public get anthropicConfig(): AnthropicProviderConfig {
    return this.config as AnthropicProviderConfig;
  }

  /**
   * Initialize the Anthropic provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();
      
      // Initialize Anthropic client
      const { Anthropic } = await import('@anthropic-ai/sdk');
      this.client = new Anthropic({
        apiKey: this.anthropicConfig.apiKey,
        ...(this.anthropicConfig.baseUrl && { baseURL: this.anthropicConfig.baseUrl }),
        timeout: this.anthropicConfig.timeout || 30000,
        maxRetries: this.anthropicConfig.retries || 3,
        ...(this.anthropicConfig.headers && { defaultHeaders: this.anthropicConfig.headers }),
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Test with a simple message
      await this.client.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hi' }],
      });
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available models
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      // Anthropic doesn't have a models endpoint, so we return known models
      const models: ModelInfo[] = [
        {
          id: 'claude-3-opus-20240229',
          name: 'Claude 3 Opus',
          provider: 'anthropic' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 200000,
          maxTokens: 4096,
        },
        {
          id: 'claude-3-sonnet-20240229',
          name: 'Claude 3 Sonnet',
          provider: 'anthropic' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 200000,
          maxTokens: 4096,
        },
        {
          id: 'claude-3-haiku-20240307',
          name: 'Claude 3 Haiku',
          provider: 'anthropic' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: true,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: true,
          },
          contextLength: 200000,
          maxTokens: 4096,
        },
      ];

      return { success: true, data: models };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      // Convert messages to Anthropic format
      const { messages, system } = this.convertMessages(request.messages);

      const response = await this.client.messages.create({
        model: request.model,
        messages,
        system,
        max_tokens: request.maxTokens || 4096,
        temperature: request.temperature,
        top_p: request.topP,
        stop_sequences: Array.isArray(request.stop) ? request.stop : request.stop ? [request.stop] : undefined,
        stream: false,
        tools: request.tools ? this.convertTools(request.tools) : undefined,
      });

      // Convert response to OpenAI format
      const convertedResponse: ChatCompletionResponse = {
        id: response.id,
        object: 'chat.completion',
        created: Date.now(),
        model: request.model,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response.content[0]?.type === 'text' ? response.content[0].text : '',
          },
          finishReason: this.convertStopReason(response.stop_reason),
        }],
        usage: {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens,
        },
      };

      return { success: true, data: convertedResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      // Convert messages to Anthropic format
      const { messages, system } = this.convertMessages(request.messages);

      const stream = await this.client.messages.create({
        model: request.model,
        messages,
        system,
        max_tokens: request.maxTokens || 4096,
        temperature: request.temperature,
        top_p: request.topP,
        stop_sequences: Array.isArray(request.stop) ? request.stop : request.stop ? [request.stop] : undefined,
        stream: true,
        tools: request.tools ? this.convertTools(request.tools) : undefined,
      });

      // Convert stream to OpenAI format
      const convertedStream = this.convertStream(stream, request.model);

      return { success: true, data: convertedStream };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings (not supported by Anthropic)
   */
  public async createEmbedding(
    _request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    return {
      success: false,
      error: new Error('Embeddings are not supported by Anthropic'),
    };
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      // Use Anthropic's token counting
      const text = typeof input === 'string' 
        ? input 
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');
      
      const response = await this.client.messages.count_tokens({
        model: 'claude-3-haiku-20240307', // Use fastest model for counting
        messages: [{ role: 'user', content: text }],
      });

      return { success: true, data: response.input_tokens };
    } catch (error) {
      // Fallback to estimation
      const text = typeof input === 'string' 
        ? input 
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');
      
      const estimatedTokens = Math.ceil(text.length / 3.5); // Anthropic uses ~3.5 chars per token
      return { success: true, data: estimatedTokens };
    }
  }

  /**
   * Convert messages to Anthropic format
   */
  private convertMessages(messages: ChatMessage[]): { messages: any[]; system?: string } {
    let system: string | undefined;
    const convertedMessages: any[] = [];

    for (const message of messages) {
      if (message.role === 'system') {
        system = typeof message.content === 'string' ? message.content : '';
      } else {
        convertedMessages.push({
          role: message.role === 'assistant' ? 'assistant' : 'user',
          content: typeof message.content === 'string' ? message.content : '',
        });
      }
    }

    return {
      messages: convertedMessages,
      ...(system && { system })
    };
  }

  /**
   * Convert tools to Anthropic format
   */
  private convertTools(tools: any[]): any[] {
    return tools.map(tool => ({
      name: tool.function.name,
      description: tool.function.description,
      input_schema: tool.function.parameters,
    }));
  }

  /**
   * Convert stop reason to OpenAI format
   */
  private convertStopReason(stopReason: string | null): 'stop' | 'length' | 'function_call' | 'tool_calls' | 'content_filter' {
    switch (stopReason) {
      case 'end_turn':
        return 'stop';
      case 'max_tokens':
        return 'length';
      case 'tool_use':
        return 'tool_calls';
      default:
        return 'stop';
    }
  }

  /**
   * Convert Anthropic stream to OpenAI format
   */
  private async* convertStream(stream: any, model: string): AsyncIterable<ChatCompletionChunk> {
    let messageId = `chatcmpl-${Date.now()}`;
    
    for await (const chunk of stream) {
      if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text') {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {
              role: 'assistant',
              content: chunk.delta.text,
            },
          }],
        };
      } else if (chunk.type === 'message_stop') {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {},
            finishReason: 'stop',
          }],
        };
      }
    }
  }
}
