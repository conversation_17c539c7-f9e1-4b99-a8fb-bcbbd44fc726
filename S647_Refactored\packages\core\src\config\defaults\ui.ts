/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { UIConfiguration } from '@inkbytefo/s647-shared';

/**
 * Default UI configuration
 */
export const DEFAULT_UI: UIConfiguration = {
  theme: 'auto',
  animations: true,
  verbose: false,
  showTimestamps: true,
  showProviderInfo: true,
  maxHistoryItems: 100,
  autoScroll: true,
  confirmActions: true,
  shortcuts: {
    exit: 'ctrl+c',
    help: 'f1',
    refresh: 'f5',
    clear: 'ctrl+l',
    save: 'ctrl+s',
  },
  colors: {
    primary: '#007acc',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8',
  },
  layout: {
    sidebar: true,
    statusBar: true,
    toolbar: true,
    minimap: false,
  },
  editor: {
    fontSize: 14,
    fontFamily: 'monospace',
    lineNumbers: true,
    wordWrap: true,
    tabSize: 2,
    insertSpaces: true,
  },
  terminal: {
    fontSize: 12,
    fontFamily: 'monospace',
    cursorStyle: 'block',
    scrollback: 1000,
  },
};
