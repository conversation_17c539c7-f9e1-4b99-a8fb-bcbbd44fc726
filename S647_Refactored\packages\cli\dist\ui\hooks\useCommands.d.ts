/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { HistoryItem } from './useStreaming.js';
/**
 * Command interface
 */
export interface Command {
    name: string;
    description: string;
    usage: string;
    aliases?: string[];
    category: 'system' | 'ai' | 'file' | 'memory' | 'tools';
    handler: (args: string[], context: CommandContext) => Promise<CommandResult>;
}
/**
 * Command context
 */
export interface CommandContext {
    config: Configuration;
    logger: Logger;
    addHistoryItem: (item: HistoryItem) => void;
    clearHistory: () => void;
    getHistory: () => HistoryItem[];
    currentProvider: string;
    setCurrentProvider: (provider: string) => void;
}
/**
 * Command result
 */
export interface CommandResult {
    success: boolean;
    message: string;
    data?: any;
    silent?: boolean;
}
/**
 * Commands hook
 */
export declare function useCommands(config: Configuration, logger: Logger, addHistoryItem: (item: HistoryItem) => void, clearHistory: () => void, getHistory: () => HistoryItem[], currentProvider: string, setCurrentProvider: (provider: string) => void): {
    commands: Command[];
    executeCommand: (input: string) => Promise<CommandResult | null>;
    getCommandSuggestions: (partial: string) => string[];
};
//# sourceMappingURL=useCommands.d.ts.map