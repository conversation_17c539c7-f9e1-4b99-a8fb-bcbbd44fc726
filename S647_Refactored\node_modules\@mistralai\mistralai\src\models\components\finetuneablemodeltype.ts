/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const FineTuneableModelType = {
  Completion: "completion",
  Classifier: "classifier",
} as const;
export type FineTuneableModelType = ClosedEnum<typeof FineTuneableModelType>;

/** @internal */
export const FineTuneableModelType$inboundSchema: z.ZodNativeEnum<
  typeof FineTuneableModelType
> = z.nativeEnum(FineTuneableModelType);

/** @internal */
export const FineTuneableModelType$outboundSchema: z.ZodNativeEnum<
  typeof FineTuneableModelType
> = FineTuneableModelType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FineTuneableModelType$ {
  /** @deprecated use `FineTuneableModelType$inboundSchema` instead. */
  export const inboundSchema = FineTuneableModelType$inboundSchema;
  /** @deprecated use `FineTuneableModelType$outboundSchema` instead. */
  export const outboundSchema = FineTuneableModelType$outboundSchema;
}
