{"name": "@inkbytefo/s647-core-refactored", "version": "2.0.0", "description": "S647 Core - Modern AI workflow engine with enhanced multi-provider support", "repository": {"type": "git", "url": "git+https://github.com/inkbytefo/s647-refactored.git"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "node ../../scripts/build_package.js", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "test": "vitest run", "test:ci": "vitest run --coverage", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "files": ["dist", "README.md"], "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@google/genai": "^1.10.0", "@inkbytefo/s647-shared": "file:../shared", "@mistralai/mistralai": "^1.7.5", "@modelcontextprotocol/sdk": "^1.16.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-logs-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.203.0", "@opentelemetry/instrumentation-http": "^0.203.0", "@opentelemetry/sdk-node": "^0.203.0", "ajv": "^8.17.1", "chardet": "^2.1.0", "diff": "^8.0.2", "dotenv": "^16.3.1", "glob": "^10.4.5", "google-auth-library": "^10.1.0", "html-to-text": "^9.0.5", "https-proxy-agent": "^7.0.6", "ignore": "^7.0.5", "micromatch": "^4.0.8", "open": "^10.2.0", "openai": "^4.52.7", "shell-quote": "^1.8.3", "simple-git": "^3.28.0", "strip-ansi": "^7.1.0", "undici": "^7.12.0", "ws": "^8.18.0", "zod": "^3.22.4"}, "devDependencies": {"@types/diff": "^7.0.2", "@types/glob": "^8.1.0", "@types/html-to-text": "^9.0.4", "@types/micromatch": "^4.0.9", "@types/minimatch": "^5.1.2", "@types/shell-quote": "^1.7.5", "@types/ws": "^8.5.10", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "engines": {"node": ">=20"}}