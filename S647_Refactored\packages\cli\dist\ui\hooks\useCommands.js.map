{"version": 3, "file": "useCommands.js", "sourceRoot": "", "sources": ["../../../src/ui/hooks/useCommands.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAuCzD;;GAEG;AACH,MAAM,UAAU,WAAW,CACzB,MAAqB,EACrB,MAAc,EACd,cAA2C,EAC3C,YAAwB,EACxB,UAA+B,EAC/B,eAAuB,EACvB,kBAA8C;IAE9C,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAuB,IAAI,GAAG,EAAE,CAAC,CAAC;IAE1E,yBAAyB;IACzB,MAAM,OAAO,GAAmB;QAC9B,MAAM;QACN,MAAM;QACN,cAAc;QACd,YAAY;QACZ,UAAU;QACV,eAAe;QACf,kBAAkB;KACnB,CAAC;IAEF,kBAAkB;IAClB,MAAM,cAAc,GAAc;QAChC;YACE,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,yBAAyB;YACtC,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;YACnB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,KAAK,EAAE,IAAc,EAAE,EAAE;gBAChC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpB,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBAC1C,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO;4BACL,OAAO,EAAE,IAAI;4BACb,OAAO,EAAE,KAAK,OAAO,CAAC,IAAI,QAAQ,OAAO,CAAC,WAAW,cAAc,OAAO,CAAC,KAAK,iBAAiB,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;yBACxL,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,YAAY,WAAW,6CAA6C;yBAC9E,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,MAAM,UAAU,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAU,CAAC;gBACxE,IAAI,QAAQ,GAAG,6BAA6B,CAAC;gBAE7C,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC5B,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;oBAChG,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAChC,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC;wBACtF,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;4BAC7B,QAAQ,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,WAAW,IAAI,CAAC;wBAC1D,CAAC,CAAC,CAAC;wBACH,QAAQ,IAAI,IAAI,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,QAAQ,IAAI,0EAA0E,CAAC;gBAEvF,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,QAAQ;iBAClB,CAAC;YACJ,CAAC;SACF;QACD;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,oBAAoB;YACjC,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,CAAC,KAAK,CAAC;YAChB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,YAAY,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,yBAAyB;oBAClC,MAAM,EAAE,IAAI;iBACb,CAAC;YACJ,CAAC;SACF;QACD;YACE,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,sBAAsB;YACnC,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC;YACtB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;SACF;QACD;YACE,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,0BAA0B;YACvC,KAAK,EAAE,UAAU;YACjB,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,6FAA6F;iBACvG,CAAC;YACJ,CAAC;SACF;KACF,CAAC;IAEF,cAAc;IACd,MAAM,UAAU,GAAc;QAC5B;YACE,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,6BAA6B;YAC1C,KAAK,EAAE,YAAY;YACnB,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACnD,IAAI,OAAO,GAAG,oCAAoC,CAAC;gBAEnD,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE;oBACrC,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;oBAC5C,MAAM,OAAO,GAAG,IAAI,KAAK,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjE,OAAO,IAAI,GAAG,MAAM,MAAM,IAAI,KAAK,OAAO,IAAI,CAAC;oBAC/C,OAAO,IAAI,aAAa,QAAQ,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC;oBACxD,OAAO,IAAI,cAAc,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,MAAM,CAAC;gBAC3E,CAAC,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO;iBACR,CAAC;YACJ,CAAC;SACF;QACD;YACE,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,mCAAmC;YAChD,KAAK,EAAE,oBAAoB;YAC3B,OAAO,EAAE,CAAC,KAAK,CAAC;YAChB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,KAAK,EAAE,IAAc,EAAE,EAAE;gBAChC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,qEAAqE;qBAC/E,CAAC;gBACJ,CAAC;gBAED,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAEhD,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,aAAa,YAAY,uDAAuD;qBAC1F,CAAC;gBACJ,CAAC;gBAED,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACtB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,aAAa,YAAY,oDAAoD;qBACvF,CAAC;gBACJ,CAAC;gBAED,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBACjC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,8BAA8B,YAAY,IAAI;iBACxD,CAAC;YACJ,CAAC;SACF;KACF,CAAC;IAEF,gBAAgB;IAChB,MAAM,YAAY,GAAc;QAC9B;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,mBAAmB;YAChC,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,kDAAkD;gBAClD,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,oEAAoE;iBAC9E,CAAC;YACJ,CAAC;SACF;KACF,CAAC;IAEF,kBAAkB;IAClB,MAAM,cAAc,GAAc;QAChC;YACE,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,wBAAwB;YACrC,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE,CAAC,KAAK,CAAC;YAChB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,KAAK,EAAE,IAAc,EAAE,EAAE;gBAChC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpB,kBAAkB;oBAClB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC7B,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,mCAAmC,KAAK,iDAAiD;qBACnG,CAAC;gBACJ,CAAC;gBAED,oBAAoB;gBACpB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,qEAAqE;iBAC/E,CAAC;YACJ,CAAC;SACF;KACF,CAAC;IAEF,iBAAiB;IACjB,MAAM,aAAa,GAAc;QAC/B;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,sBAAsB;YACnC,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;gBAChD,IAAI,OAAO,GAAG,6BAA6B,CAAC;gBAE5C,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9B,OAAO,IAAI,iCAAiC,CAAC;gBAC/C,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAC1B,OAAO,IAAI,OAAO,IAAI,MAAM,CAAC;oBAC/B,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO;iBACR,CAAC;YACJ,CAAC;SACF;KACF,CAAC;IAEF,sBAAsB;IACtB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG;YAClB,GAAG,cAAc;YACjB,GAAG,UAAU;YACb,GAAG,YAAY;YACf,GAAG,cAAc;YACjB,GAAG,aAAa;SACjB,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,GAAG,EAAmB,CAAC;QAE9C,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5B,wBAAwB;YACxB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEtC,cAAc;YACd,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC9B,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,WAAW,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;IAE9B,4BAA4B;IAC5B,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,EAAE,KAAa,EAAiC,EAAE;QACxF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,CAAC,gBAAgB;QAC/B,CAAC;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE5B,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB,WAAW,0CAA0C;aACtF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,WAAW,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnB,mCAAmC;gBACnC,MAAM,WAAW,GAAgB;oBAC/B,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;oBACvB,IAAI,EAAE,QAAuB;oBAC7B,OAAO,EAAE,aAAa,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBACrD,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBACF,cAAc,CAAC,WAAW,CAAC,CAAC;gBAE5B,wBAAwB;gBACxB,MAAM,UAAU,GAAgB;oBAC9B,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC1B,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAE,QAAwB,CAAC,CAAC,CAAE,OAAuB;oBAC3E,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBACF,cAAc,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,8BAA8B,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;aACjG,CAAC;QACJ,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;IAEhD,0BAA0B;IAC1B,MAAM,qBAAqB,GAAG,WAAW,CAAC,CAAC,OAAe,EAAY,EAAE;QACtE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,QAAQ,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,0BAA0B;IAC7D,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,OAAO;QACL,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,oBAAoB;QAClF,cAAc;QACd,qBAAqB;KACtB,CAAC;AACJ,CAAC"}