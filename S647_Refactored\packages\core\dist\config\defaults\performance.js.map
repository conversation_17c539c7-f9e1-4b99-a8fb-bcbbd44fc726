{"version": 3, "file": "performance.js", "sourceRoot": "", "sources": ["../../../src/config/defaults/performance.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,qBAAqB,EAAE,EAAE;IACzB,cAAc,EAAE,KAAK,EAAE,aAAa;IACpC,UAAU,EAAE,IAAI,EAAE,WAAW;IAC7B,UAAU,EAAE,CAAC;IACb,OAAO,EAAE;QACP,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;QACpC,GAAG,EAAE,OAAO,EAAE,SAAS;QACvB,WAAW,EAAE,MAAM,EAAE,aAAa;KACnC;IACD,MAAM,EAAE;QACN,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;QACxC,WAAW,EAAE,GAAG,EAAE,MAAM;QACxB,iBAAiB,EAAE,IAAI;KACxB;IACD,UAAU,EAAE;QACV,SAAS,EAAE,IAAI;QACf,UAAU,EAAE,EAAE;QACd,OAAO,EAAE,KAAK;QACd,cAAc,EAAE,IAAI;KACrB;IACD,UAAU,EAAE;QACV,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;QACtC,kBAAkB,EAAE,CAAC;QACrB,UAAU,EAAE,EAAE,GAAG,IAAI,EAAE,OAAO;KAC/B;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,WAAW,EAAE;QACX,GAAG,mBAAmB;QACtB,qBAAqB,EAAE,CAAC;QACxB,cAAc,EAAE,KAAK,EAAE,WAAW;QAClC,OAAO,EAAE;YACP,GAAG,mBAAmB,CAAC,OAAO;YAC9B,OAAO,EAAE,KAAK,EAAE,iCAAiC;SAClD;QACD,MAAM,EAAE;YACN,GAAG,mBAAmB,CAAC,MAAM;YAC7B,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;YACxC,iBAAiB,EAAE,IAAI;SACxB;KACF;IAED,UAAU,EAAE;QACV,GAAG,mBAAmB;QACtB,qBAAqB,EAAE,EAAE;QACzB,cAAc,EAAE,KAAK,EAAE,aAAa;QACpC,OAAO,EAAE;YACP,GAAG,mBAAmB,CAAC,OAAO;YAC9B,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;SACrC;QACD,MAAM,EAAE;YACN,GAAG,mBAAmB,CAAC,MAAM;YAC7B,WAAW,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,MAAM;YACvC,iBAAiB,EAAE,KAAK;SACzB;KACF;IAED,IAAI,EAAE;QACJ,GAAG,mBAAmB;QACtB,qBAAqB,EAAE,CAAC;QACxB,cAAc,EAAE,IAAI,EAAE,YAAY;QAClC,OAAO,EAAE;YACP,GAAG,mBAAmB,CAAC,OAAO;YAC9B,OAAO,EAAE,KAAK;SACf;QACD,MAAM,EAAE;YACN,GAAG,mBAAmB,CAAC,MAAM;YAC7B,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;YACxC,iBAAiB,EAAE,KAAK;SACzB;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,kCAAkC,CAAC,GAA0C;IAC3F,OAAO,mBAAmB,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B;IACzC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;IAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAEjE,qDAAqD;IACrD,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;IAEzE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc;IAK5B,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACpC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;IACvC,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC;IAE5B,OAAO;QACL,IAAI;QACJ,KAAK;QACL,UAAU,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG;KACjC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,SAAS,GAAG,GAAG;IAC/C,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc,EAAE,CAAC;IACxC,OAAO,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC;AACtC,CAAC"}