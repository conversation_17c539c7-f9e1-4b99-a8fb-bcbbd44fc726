/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { BaseProvider } from '../interfaces/provider.js';
/**
 * OpenRouter provider implementation
 * OpenRouter is OpenAI-compatible, so we use the OpenAI SDK with custom base URL
 */
export class OpenRouterProvider extends BaseProvider {
    client; // OpenAI client instance
    constructor(id, config) {
        super(id, config);
    }
    get openrouterConfig() {
        return this.config;
    }
    /**
     * Initialize the OpenRouter provider
     */
    async initialize() {
        try {
            this.validateConfig();
            // Initialize OpenAI client with OpenRouter base URL
            const { OpenAI } = await import('openai');
            if (!this.openrouterConfig.apiKey) {
                throw new Error('OpenRouter API key is required');
            }
            this.client = new OpenAI({
                apiKey: this.openrouterConfig.apiKey,
                baseURL: this.openrouterConfig.baseUrl || 'https://openrouter.ai/api/v1',
                timeout: this.openrouterConfig.timeout || 30000,
                maxRetries: this.openrouterConfig.retries || 3,
                defaultHeaders: {
                    'HTTP-Referer': this.openrouterConfig.siteUrl || 'https://s647.dev',
                    'X-Title': this.openrouterConfig.siteName || 'S647',
                    ...this.openrouterConfig.headers,
                },
            });
            // Test the connection
            await this.isAvailable();
            this.setStatus('available');
            return { success: true, data: undefined };
        }
        catch (error) {
            this.setStatus('error');
            return { success: false, error: this.handleError(error) };
        }
    }
    /**
     * Check if the provider is available
     */
    async isAvailable() {
        try {
            if (!this.client) {
                return false;
            }
            // Test with a simple models list call
            await this.client.models.list();
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get available models from OpenRouter
     */
    async getModels() {
        try {
            if (!this.client) {
                throw new Error('Provider not initialized');
            }
            const response = await this.client.models.list();
            const models = response.data.map((model) => ({
                id: model.id,
                name: model.id,
                provider: 'openrouter',
                capabilities: {
                    chat: true,
                    completion: true,
                    embedding: model.id.includes('embedding'),
                    vision: model.id.includes('vision') || model.id.includes('gpt-4'),
                    functionCalling: !model.id.includes('embedding'),
                    streaming: true,
                    systemMessages: true,
                    multiModal: model.id.includes('vision') || model.id.includes('gpt-4'),
                },
                contextLength: model.context_length || 4096,
                maxTokens: model.max_completion_tokens || 4096,
                pricing: {
                    input: model.pricing?.prompt,
                    output: model.pricing?.completion,
                },
            }));
            return { success: true, data: models };
        }
        catch (error) {
            return { success: false, error: this.handleError(error) };
        }
    }
    /**
     * Create a chat completion (OpenAI-compatible)
     */
    async createChatCompletion(request) {
        try {
            if (!this.client) {
                throw new Error('Provider not initialized');
            }
            const response = await this.client.chat.completions.create({
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.maxTokens,
                top_p: request.topP,
                frequency_penalty: request.frequencyPenalty,
                presence_penalty: request.presencePenalty,
                stop: request.stop,
                stream: false,
                tools: request.tools,
                tool_choice: request.toolChoice,
                user: request.user,
            });
            return { success: true, data: response };
        }
        catch (error) {
            return { success: false, error: this.handleError(error) };
        }
    }
    /**
     * Create a streaming chat completion (OpenAI-compatible)
     */
    async createChatCompletionStream(request) {
        try {
            if (!this.client) {
                throw new Error('Provider not initialized');
            }
            const stream = await this.client.chat.completions.create({
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.maxTokens,
                top_p: request.topP,
                frequency_penalty: request.frequencyPenalty,
                presence_penalty: request.presencePenalty,
                stop: request.stop,
                stream: true,
                tools: request.tools,
                tool_choice: request.toolChoice,
                user: request.user,
            });
            return { success: true, data: stream };
        }
        catch (error) {
            return { success: false, error: this.handleError(error) };
        }
    }
    /**
     * Create embeddings (OpenAI-compatible)
     */
    async createEmbedding(request) {
        try {
            if (!this.client) {
                throw new Error('Provider not initialized');
            }
            const response = await this.client.embeddings.create({
                model: request.model,
                input: request.input,
                user: request.user,
            });
            return { success: true, data: response };
        }
        catch (error) {
            return { success: false, error: this.handleError(error) };
        }
    }
    /**
     * Count tokens for a given input
     */
    async countTokens(input) {
        try {
            // OpenRouter doesn't have a dedicated token counting endpoint
            // Use estimation based on character count
            const text = typeof input === 'string'
                ? input
                : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');
            // Use OpenAI's estimation (roughly 4 characters per token)
            const estimatedTokens = Math.ceil(text.length / 4);
            return { success: true, data: estimatedTokens };
        }
        catch (error) {
            return { success: false, error: this.handleError(error) };
        }
    }
}
//# sourceMappingURL=openrouter.js.map