/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
/**
 * File reference interface
 */
export interface FileReference {
    path: string;
    content: string;
    size: number;
    lastModified: Date;
    encoding: string;
    type: 'text' | 'binary' | 'image';
    error?: string;
}
/**
 * File suggestion interface
 */
export interface FileSuggestion {
    path: string;
    type: 'file' | 'directory';
    size?: number;
    lastModified?: Date;
    isGitIgnored?: boolean;
}
/**
 * File integration hook
 */
export declare function useFileIntegration(config: Configuration, logger: Logger): {
    readFileContent: (filePath: string) => Promise<FileReference>;
    getFileSuggestions: (partialPath: string) => Promise<FileSuggestion[]>;
    processFileReferences: (text: string) => Promise<string>;
    clearCache: () => void;
    getCacheStats: () => {
        fileCount: number;
        totalSize: number;
        recentFilesCount: number;
    };
    recentFiles: string[];
    isLoading: boolean;
    isFileAllowed: (filePath: string) => boolean;
    isFileSizeAllowed: (size: number) => boolean;
};
//# sourceMappingURL=useFileIntegration.d.ts.map