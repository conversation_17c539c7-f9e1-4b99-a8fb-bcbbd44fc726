/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolContext, ToolResult, Tool<PERSON>elp, AsyncResult } from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';
/**
 * Tool for executing shell commands
 */
export declare class ShellExecuteTool extends BaseTool {
    private readonly dangerousCommands;
    constructor();
    execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    getHelp(): ToolHelp;
    private resolvePath;
    private isDangerousCommand;
    private executeCommand;
    private formatResult;
}
//# sourceMappingURL=ShellExecuteTool.d.ts.map