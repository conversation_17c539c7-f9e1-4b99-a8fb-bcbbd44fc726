/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON>lParams, ToolContext, ToolResult, Tool<PERSON>elp, AsyncResult } from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';
/**
 * Tool for searching the web
 * Note: This is a placeholder implementation. In production, you would integrate with
 * a search API like Google Custom Search, Bing Search API, or DuckDuckGo API.
 */
export declare class WebSearchTool extends BaseTool {
    constructor();
    execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    getHelp(): ToolHelp;
    private performSearch;
    private formatResults;
}
//# sourceMappingURL=WebSearchTool.d.ts.map