/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type ProcessingStatusOut = {
  documentId: string;
  processingStatus: string;
};

/** @internal */
export const ProcessingStatusOut$inboundSchema: z.ZodType<
  ProcessingStatusOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  document_id: z.string(),
  processing_status: z.string(),
}).transform((v) => {
  return remap$(v, {
    "document_id": "documentId",
    "processing_status": "processingStatus",
  });
});

/** @internal */
export type ProcessingStatusOut$Outbound = {
  document_id: string;
  processing_status: string;
};

/** @internal */
export const ProcessingStatusOut$outboundSchema: z.ZodType<
  ProcessingStatusOut$Outbound,
  z.ZodTypeDef,
  ProcessingStatusOut
> = z.object({
  documentId: z.string(),
  processingStatus: z.string(),
}).transform((v) => {
  return remap$(v, {
    documentId: "document_id",
    processingStatus: "processing_status",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProcessingStatusOut$ {
  /** @deprecated use `ProcessingStatusOut$inboundSchema` instead. */
  export const inboundSchema = ProcessingStatusOut$inboundSchema;
  /** @deprecated use `ProcessingStatusOut$outboundSchema` instead. */
  export const outboundSchema = ProcessingStatusOut$outboundSchema;
  /** @deprecated use `ProcessingStatusOut$Outbound` instead. */
  export type Outbound = ProcessingStatusOut$Outbound;
}

export function processingStatusOutToJSON(
  processingStatusOut: ProcessingStatusOut,
): string {
  return JSON.stringify(
    ProcessingStatusOut$outboundSchema.parse(processingStatusOut),
  );
}

export function processingStatusOutFromJSON(
  jsonString: string,
): SafeParseResult<ProcessingStatusOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ProcessingStatusOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ProcessingStatusOut' from JSON`,
  );
}
