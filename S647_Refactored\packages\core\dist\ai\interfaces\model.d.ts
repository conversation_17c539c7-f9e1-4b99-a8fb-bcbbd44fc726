/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ModelInfo, AsyncResult } from '@inkbytefo/s647-shared';
/**
 * Model interface definitions
 */
export interface ModelManager {
    getAvailableModels(): AsyncResult<ModelInfo[]>;
    getModelInfo(modelId: string): AsyncResult<ModelInfo>;
    validateModel(modelId: string): AsyncResult<boolean>;
}
//# sourceMappingURL=model.d.ts.map