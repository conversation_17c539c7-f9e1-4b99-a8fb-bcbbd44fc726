/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Log levels
 */
export type LoggerLevel = 'debug' | 'info' | 'warn' | 'error';
/**
 * Log formats
 */
export type LogFormat = 'text' | 'json';
/**
 * Log outputs
 */
export type LogOutput = 'console' | 'file';
/**
 * Logger configuration
 */
export interface LoggerConfig {
    level: LoggerLevel;
    format: LogFormat;
    output: LogOutput;
    includeTimestamp?: boolean;
    includeLevel?: boolean;
    includeSource?: boolean;
    filePath?: string;
}
/**
 * Logger interface
 */
export interface ILogger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    log(level: LoggerLevel, message: string, ...args: any[]): void;
}
/**
 * Console logger implementation
 */
export declare class ConsoleLogger implements ILogger {
    protected config: LoggerConfig;
    constructor(config: LoggerConfig);
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    log(level: LoggerLevel, message: string, ...args: any[]): void;
    protected formatMessage(level: LoggerLevel, message: string, ...args: any[]): string;
}
/**
 * Create a logger instance
 */
export declare function createLogger(config: LoggerConfig): ILogger;
/**
 * Default logger instance
 */
export declare const defaultLogger: ILogger;
/**
 * Get log level color for console output
 */
export declare function getLogLevelColor(level: LoggerLevel): string;
/**
 * Reset color code
 */
export declare const RESET_COLOR = "\u001B[0m";
/**
 * Colored console logger
 */
export declare class ColoredConsoleLogger extends ConsoleLogger {
    log(level: LoggerLevel, message: string, ...args: any[]): void;
}
/**
 * Create a colored logger instance
 */
export declare function createColoredLogger(config: LoggerConfig): ILogger;
//# sourceMappingURL=logger.d.ts.map