/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

/**
 * CLI arguments interface
 */
export interface CliArgs {
  // Mode options
  interactive?: boolean;
  nonInteractive?: boolean;
  
  // Command options
  command?: string;
  input?: string;
  output?: string;
  
  // Provider options
  provider?: string;
  model?: string;
  
  // Configuration options
  config?: string;
  profile?: string;
  
  // Behavior options
  debug?: boolean;
  verbose?: boolean;
  quiet?: boolean;
  yes?: boolean;
  
  // File options
  file?: string[];
  exclude?: string[];
  include?: string[];
  
  // Output options
  format?: 'text' | 'json' | 'yaml' | 'markdown';
  stream?: boolean;
  
  // Advanced options
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  
  // Help and version
  help?: boolean;
  version?: boolean;
}

/**
 * Parse command line arguments
 */
export function parseArguments(): CliArgs {
  return yargs(hideBin(process.argv))
    .scriptName('s647')
    .usage('$0 [options] [command]')
    .example('$0', 'Start interactive mode')
    .example('$0 "Analyze this codebase"', 'Run a single command')
    .example('$0 --file src/**/*.ts "Review this code"', 'Analyze specific files')
    .example('$0 config init', 'Initialize configuration')
    
    // Mode options
    .option('interactive', {
      alias: 'i',
      type: 'boolean',
      description: 'Force interactive mode',
      default: false,
    })
    .option('non-interactive', {
      alias: 'n',
      type: 'boolean',
      description: 'Run in non-interactive mode',
      default: false,
    })
    
    // Command options
    .option('input', {
      type: 'string',
      description: 'Input text or file path',
    })
    .option('output', {
      alias: 'o',
      type: 'string',
      description: 'Output file path',
    })
    
    // Provider options
    .option('provider', {
      alias: 'p',
      type: 'string',
      description: 'AI provider to use (openai, anthropic, google, etc.)',
    })
    .option('model', {
      alias: 'm',
      type: 'string',
      description: 'AI model to use',
    })
    
    // Configuration options
    .option('config', {
      alias: 'c',
      type: 'string',
      description: 'Configuration file path',
    })
    .option('profile', {
      type: 'string',
      description: 'Configuration profile to use',
    })
    
    // Behavior options
    .option('debug', {
      alias: 'd',
      type: 'boolean',
      description: 'Enable debug mode',
      default: false,
    })
    .option('verbose', {
      alias: 'v',
      type: 'boolean',
      description: 'Enable verbose output',
      default: false,
    })
    .option('quiet', {
      alias: 'q',
      type: 'boolean',
      description: 'Suppress non-essential output',
      default: false,
    })
    .option('yes', {
      alias: 'y',
      type: 'boolean',
      description: 'Automatically answer yes to prompts',
      default: false,
    })
    
    // File options
    .option('file', {
      alias: 'f',
      type: 'array',
      description: 'Files to include in the context',
    })
    .option('exclude', {
      type: 'array',
      description: 'Patterns to exclude from file search',
    })
    .option('include', {
      type: 'array',
      description: 'Patterns to include in file search',
    })
    
    // Output options
    .option('format', {
      type: 'string',
      choices: ['text', 'json', 'yaml', 'markdown'],
      description: 'Output format',
      default: 'text',
    })
    .option('stream', {
      type: 'boolean',
      description: 'Enable streaming output',
      default: false,
    })
    
    // Advanced options
    .option('temperature', {
      alias: 't',
      type: 'number',
      description: 'AI temperature (0.0 to 2.0)',
      default: 0.7,
    })
    .option('max-tokens', {
      type: 'number',
      description: 'Maximum tokens to generate',
    })
    .option('timeout', {
      type: 'number',
      description: 'Request timeout in seconds',
      default: 30,
    })
    
    // Commands
    .command('config', 'Configuration management', (yargs) => {
      return yargs
        .command('init', 'Initialize configuration')
        .command('show', 'Show current configuration')
        .command('validate', 'Validate configuration')
        .command('reset', 'Reset configuration to defaults');
    })
    .command('providers', 'Provider management', (yargs) => {
      return yargs
        .command('list', 'List available providers')
        .command('test', 'Test provider connections')
        .command('models', 'List available models');
    })
    .command('tools', 'Tool management', (yargs) => {
      return yargs
        .command('list', 'List available tools')
        .command('test', 'Test tool functionality');
    })
    
    // Global options
    .help('help')
    .alias('help', 'h')
    .version()
    .alias('version', 'V')
    
    // Validation
    .check((argv) => {
      if (argv.interactive && argv.nonInteractive) {
        throw new Error('Cannot use both --interactive and --non-interactive');
      }
      
      if (argv.temperature !== undefined && ((argv.temperature as number) < 0 || (argv.temperature as number) > 2)) {
        throw new Error('Temperature must be between 0.0 and 2.0');
      }
      
      if (argv.maxTokens !== undefined && (argv.maxTokens as number) <= 0) {
        throw new Error('Max tokens must be greater than 0');
      }
      
      if (argv.timeout !== undefined && (argv.timeout as number) <= 0) {
        throw new Error('Timeout must be greater than 0');
      }
      
      return true;
    })
    
    // Parse and return
    .parseSync() as CliArgs;
}

/**
 * Get the command from arguments
 */
export function getCommand(args: CliArgs): string | undefined {
  const positional = process.argv.slice(2).filter(arg => !arg.startsWith('-'));
  
  // Skip known commands
  const knownCommands = ['config', 'providers', 'tools'];
  if (positional.length > 0 && positional[0] && !knownCommands.includes(positional[0])) {
    return positional.join(' ');
  }
  
  return args.input;
}

/**
 * Check if running in non-interactive mode
 */
export function isNonInteractive(args: CliArgs): boolean {
  return Boolean(
    args.nonInteractive ||
    args.command ||
    args.input ||
    process.env.CI ||
    !process.stdin.isTTY
  );
}
