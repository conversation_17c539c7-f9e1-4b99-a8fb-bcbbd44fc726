/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ModelCapabilities,
  ModelCapabilities$inboundSchema,
  ModelCapabilities$Outbound,
  ModelCapabilities$outboundSchema,
} from "./modelcapabilities.js";

export const FTModelCardType = {
  FineTuned: "fine-tuned",
} as const;
export type FTModelCardType = ClosedEnum<typeof FTModelCardType>;

/**
 * Extra fields for fine-tuned models.
 */
export type FTModelCard = {
  id: string;
  object?: string | undefined;
  created?: number | undefined;
  ownedBy?: string | undefined;
  capabilities: ModelCapabilities;
  name?: string | null | undefined;
  description?: string | null | undefined;
  maxContextLength?: number | undefined;
  aliases?: Array<string> | undefined;
  deprecation?: Date | null | undefined;
  deprecationReplacementModel?: string | null | undefined;
  defaultModelTemperature?: number | null | undefined;
  type?: "fine-tuned" | undefined;
  job: string;
  root: string;
  archived?: boolean | undefined;
};

/** @internal */
export const FTModelCardType$inboundSchema: z.ZodNativeEnum<
  typeof FTModelCardType
> = z.nativeEnum(FTModelCardType);

/** @internal */
export const FTModelCardType$outboundSchema: z.ZodNativeEnum<
  typeof FTModelCardType
> = FTModelCardType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FTModelCardType$ {
  /** @deprecated use `FTModelCardType$inboundSchema` instead. */
  export const inboundSchema = FTModelCardType$inboundSchema;
  /** @deprecated use `FTModelCardType$outboundSchema` instead. */
  export const outboundSchema = FTModelCardType$outboundSchema;
}

/** @internal */
export const FTModelCard$inboundSchema: z.ZodType<
  FTModelCard,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  object: z.string().default("model"),
  created: z.number().int().optional(),
  owned_by: z.string().default("mistralai"),
  capabilities: ModelCapabilities$inboundSchema,
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
  max_context_length: z.number().int().default(32768),
  aliases: z.array(z.string()).optional(),
  deprecation: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  deprecation_replacement_model: z.nullable(z.string()).optional(),
  default_model_temperature: z.nullable(z.number()).optional(),
  type: z.literal("fine-tuned").default("fine-tuned"),
  job: z.string(),
  root: z.string(),
  archived: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    "owned_by": "ownedBy",
    "max_context_length": "maxContextLength",
    "deprecation_replacement_model": "deprecationReplacementModel",
    "default_model_temperature": "defaultModelTemperature",
  });
});

/** @internal */
export type FTModelCard$Outbound = {
  id: string;
  object: string;
  created?: number | undefined;
  owned_by: string;
  capabilities: ModelCapabilities$Outbound;
  name?: string | null | undefined;
  description?: string | null | undefined;
  max_context_length: number;
  aliases?: Array<string> | undefined;
  deprecation?: string | null | undefined;
  deprecation_replacement_model?: string | null | undefined;
  default_model_temperature?: number | null | undefined;
  type: "fine-tuned";
  job: string;
  root: string;
  archived: boolean;
};

/** @internal */
export const FTModelCard$outboundSchema: z.ZodType<
  FTModelCard$Outbound,
  z.ZodTypeDef,
  FTModelCard
> = z.object({
  id: z.string(),
  object: z.string().default("model"),
  created: z.number().int().optional(),
  ownedBy: z.string().default("mistralai"),
  capabilities: ModelCapabilities$outboundSchema,
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
  maxContextLength: z.number().int().default(32768),
  aliases: z.array(z.string()).optional(),
  deprecation: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  deprecationReplacementModel: z.nullable(z.string()).optional(),
  defaultModelTemperature: z.nullable(z.number()).optional(),
  type: z.literal("fine-tuned").default("fine-tuned"),
  job: z.string(),
  root: z.string(),
  archived: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    ownedBy: "owned_by",
    maxContextLength: "max_context_length",
    deprecationReplacementModel: "deprecation_replacement_model",
    defaultModelTemperature: "default_model_temperature",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FTModelCard$ {
  /** @deprecated use `FTModelCard$inboundSchema` instead. */
  export const inboundSchema = FTModelCard$inboundSchema;
  /** @deprecated use `FTModelCard$outboundSchema` instead. */
  export const outboundSchema = FTModelCard$outboundSchema;
  /** @deprecated use `FTModelCard$Outbound` instead. */
  export type Outbound = FTModelCard$Outbound;
}

export function ftModelCardToJSON(ftModelCard: FTModelCard): string {
  return JSON.stringify(FTModelCard$outboundSchema.parse(ftModelCard));
}

export function ftModelCardFromJSON(
  jsonString: string,
): SafeParseResult<FTModelCard, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FTModelCard$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FTModelCard' from JSON`,
  );
}
