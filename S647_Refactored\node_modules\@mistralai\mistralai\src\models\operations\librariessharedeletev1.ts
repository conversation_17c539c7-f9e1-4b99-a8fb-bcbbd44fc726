/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesShareDeleteV1Request = {
  libraryId: string;
  sharingDelete: components.SharingDelete;
};

/** @internal */
export const LibrariesShareDeleteV1Request$inboundSchema: z.ZodType<
  LibrariesShareDeleteV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  SharingDelete: components.SharingDelete$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "SharingDelete": "sharingDelete",
  });
});

/** @internal */
export type LibrariesShareDeleteV1Request$Outbound = {
  library_id: string;
  SharingDelete: components.SharingDelete$Outbound;
};

/** @internal */
export const LibrariesShareDeleteV1Request$outboundSchema: z.ZodType<
  LibrariesShareDeleteV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesShareDeleteV1Request
> = z.object({
  libraryId: z.string(),
  sharingDelete: components.SharingDelete$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    sharingDelete: "SharingDelete",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesShareDeleteV1Request$ {
  /** @deprecated use `LibrariesShareDeleteV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesShareDeleteV1Request$inboundSchema;
  /** @deprecated use `LibrariesShareDeleteV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesShareDeleteV1Request$outboundSchema;
  /** @deprecated use `LibrariesShareDeleteV1Request$Outbound` instead. */
  export type Outbound = LibrariesShareDeleteV1Request$Outbound;
}

export function librariesShareDeleteV1RequestToJSON(
  librariesShareDeleteV1Request: LibrariesShareDeleteV1Request,
): string {
  return JSON.stringify(
    LibrariesShareDeleteV1Request$outboundSchema.parse(
      librariesShareDeleteV1Request,
    ),
  );
}

export function librariesShareDeleteV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesShareDeleteV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesShareDeleteV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesShareDeleteV1Request' from JSON`,
  );
}
