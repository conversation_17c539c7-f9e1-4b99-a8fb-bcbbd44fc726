/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default telemetry configuration
 */
export const DEFAULT_TELEMETRY = {
    enabled: false,
    collectUsage: false,
    collectErrors: false,
    collectPerformance: false,
    anonymize: true,
    endpoint: undefined,
    apiKey: undefined,
    batchSize: 100,
    flushInterval: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 1000, // 1 second
};
/**
 * Telemetry configurations for different environments
 */
export const TELEMETRY_CONFIGS = {
    development: {
        ...DEFAULT_TELEMETRY,
        enabled: false,
        collectUsage: false,
        collectErrors: false,
        collectPerformance: false,
    },
    production: {
        ...DEFAULT_TELEMETRY,
        enabled: false, // User must explicitly enable
        collectUsage: true,
        collectErrors: true,
        collectPerformance: true,
        anonymize: true,
    },
    test: {
        ...DEFAULT_TELEMETRY,
        enabled: false,
        collectUsage: false,
        collectErrors: false,
        collectPerformance: false,
    },
};
/**
 * Get telemetry configuration for environment
 */
export function getTelemetryConfigForEnvironment(env) {
    return TELEMETRY_CONFIGS[env] || DEFAULT_TELEMETRY;
}
/**
 * Privacy-safe telemetry data types
 */
export const SAFE_TELEMETRY_EVENTS = [
    'app_start',
    'app_stop',
    'command_executed',
    'provider_used',
    'tool_used',
    'error_occurred',
    'performance_metric',
];
/**
 * Check if telemetry event is safe to collect
 */
export function isSafeTelemetryEvent(event) {
    return SAFE_TELEMETRY_EVENTS.includes(event);
}
//# sourceMappingURL=telemetry.js.map