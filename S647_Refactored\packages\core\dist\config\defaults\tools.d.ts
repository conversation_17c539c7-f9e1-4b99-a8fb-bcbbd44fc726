/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default tool configurations
 */
export declare const DEFAULT_TOOLS: {
    enabled: string[];
    file: {
        enabled: boolean;
        timeout: number;
        retries: number;
        options: {
            maxFileSize: number;
            allowedExtensions: string[];
            excludePatterns: string[];
        };
    };
    git: {
        enabled: boolean;
        timeout: number;
        retries: number;
        options: {
            maxCommits: number;
            maxDiffLines: number;
            includeUntracked: boolean;
            includeBinary: boolean;
            defaultBranch: string;
        };
    };
    web: {
        enabled: boolean;
        timeout: number;
        retries: number;
        options: {
            maxResponseSize: number;
            followRedirects: boolean;
            maxRedirects: number;
            userAgent: string;
            allowedDomains: never[];
            blockedDomains: string[];
        };
    };
    shell: {
        enabled: boolean;
        timeout: number;
        retries: number;
        options: {
            allowedCommands: string[];
            blockedCommands: string[];
            workingDirectory: string;
            inheritEnv: boolean;
            shell: string;
        };
    };
    memory: {
        enabled: boolean;
        timeout: number;
        retries: number;
        options: {
            maxEntries: number;
            maxEntrySize: number;
            persistToDisk: boolean;
            compressionEnabled: boolean;
            encryptionEnabled: boolean;
        };
    };
};
/**
 * Get default tool configuration by name
 */
export declare function getDefaultToolConfig(name: string): any;
/**
 * Get all enabled default tools
 */
export declare function getEnabledDefaultTools(): string[];
/**
 * Check if tool is enabled by default
 */
export declare function isToolEnabledByDefault(name: string): boolean;
//# sourceMappingURL=tools.d.ts.map