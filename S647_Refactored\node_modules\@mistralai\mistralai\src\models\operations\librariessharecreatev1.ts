/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesShareCreateV1Request = {
  libraryId: string;
  sharingIn: components.SharingIn;
};

/** @internal */
export const LibrariesShareCreateV1Request$inboundSchema: z.ZodType<
  LibrariesShareCreateV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  SharingIn: components.SharingIn$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "SharingIn": "sharingIn",
  });
});

/** @internal */
export type LibrariesShareCreateV1Request$Outbound = {
  library_id: string;
  SharingIn: components.SharingIn$Outbound;
};

/** @internal */
export const LibrariesShareCreateV1Request$outboundSchema: z.ZodType<
  LibrariesShareCreateV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesShareCreateV1Request
> = z.object({
  libraryId: z.string(),
  sharingIn: components.SharingIn$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    sharingIn: "SharingIn",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesShareCreateV1Request$ {
  /** @deprecated use `LibrariesShareCreateV1Request$inboundSchema` instead. */
  export const inboundSchema = LibrariesShareCreateV1Request$inboundSchema;
  /** @deprecated use `LibrariesShareCreateV1Request$outboundSchema` instead. */
  export const outboundSchema = LibrariesShareCreateV1Request$outboundSchema;
  /** @deprecated use `LibrariesShareCreateV1Request$Outbound` instead. */
  export type Outbound = LibrariesShareCreateV1Request$Outbound;
}

export function librariesShareCreateV1RequestToJSON(
  librariesShareCreateV1Request: LibrariesShareCreateV1Request,
): string {
  return JSON.stringify(
    LibrariesShareCreateV1Request$outboundSchema.parse(
      librariesShareCreateV1Request,
    ),
  );
}

export function librariesShareCreateV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<LibrariesShareCreateV1Request, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LibrariesShareCreateV1Request$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LibrariesShareCreateV1Request' from JSON`,
  );
}
