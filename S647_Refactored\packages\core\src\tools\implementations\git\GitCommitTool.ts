/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { simpleGit } from 'simple-git';
import type { SimpleGit } from 'simple-git';
import { resolve, isAbsolute } from 'path';
import { z } from 'zod';
import type {
  <PERSON>lParams,
  ToolContext,
  ToolResult,
  ToolHelp,
  AsyncResult,
} from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';

const GitCommitParams = z.object({
  repository: z.string().optional().describe('Path to git repository (default: current directory)'),
  message: z.string().describe('Commit message'),
  files: z.array(z.string()).optional().describe('Specific files to commit (default: all staged files)'),
  addAll: z.boolean().optional().default(false).describe('Add all modified files before committing'),
  author: z.string().optional().describe('Author in format "Name <email>"'),
  allowEmpty: z.boolean().optional().default(false).describe('Allow empty commits'),
});

type GitCommitParams = z.infer<typeof GitCommitParams>;

/**
 * Tool for creating git commits
 */
export class GitCommitTool extends BaseTool {
  constructor() {
    super(
      'git-commit',
      'Git Commit',
      'Create a git commit with specified message and files',
      '1.0.0',
      'git',
      GitCommitParams
    );
  }

  public async execute(
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    try {
      const { repository, message, files, addAll, author, allowEmpty } = 
        await this.validateAndParse<GitCommitParams>(params);

      // Resolve repository path
      const repoPath = this.resolvePath(repository || '.', context.workingDirectory);

      // Initialize git instance
      const git: SimpleGit = simpleGit(repoPath);

      // Check if it's a git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        return this.error(`Not a git repository: ${repoPath}`);
      }

      // Add files if specified
      if (addAll) {
        await git.add('.');
      } else if (files && files.length > 0) {
        await git.add(files);
      }

      // Check if there are changes to commit
      const status = await git.status();
      if (status.staged.length === 0 && !allowEmpty) {
        return this.error('No changes staged for commit. Use addAll: true or specify files to add.');
      }

      // Create commit
      const result = await git.commit(message);

      // Get commit info
      const log = await git.log({ maxCount: 1 });
      const latestCommit = log.latest;

      return this.success({
        type: 'text',
        content: this.formatCommitResult(result, latestCommit, repoPath),
        metadata: {
          repository: repoPath,
          commitHash: result.commit,
          branch: result.branch,
          summary: result.summary,
          author: latestCommit?.author_name || null,
          authorEmail: latestCommit?.author_email || null,
          date: latestCommit?.date || null,
          filesChanged: status.staged.length,
        },
      });
    } catch (error) {
      return this.error(this.handleError(error));
    }
  }

  public getHelp(): ToolHelp {
    return {
      description: 'Create a git commit with specified message and files',
      parameters: {
        repository: {
          type: 'string',
          description: 'Path to git repository (relative or absolute)',
          required: false,
          default: '.',
        },
        message: {
          type: 'string',
          description: 'Commit message',
          required: true,
        },
        files: {
          type: 'array',
          description: 'Specific files to commit (array of file paths)',
          required: false,
        },
        addAll: {
          type: 'boolean',
          description: 'Add all modified files before committing',
          required: false,
          default: false,
        },
        author: {
          type: 'string',
          description: 'Author in format "Name <email>"',
          required: false,
        },
        allowEmpty: {
          type: 'boolean',
          description: 'Allow empty commits',
          required: false,
          default: false,
        },
      },
      examples: [
        {
          description: 'Commit staged files with message',
          parameters: { 
            message: 'Fix bug in user authentication' 
          },
        },
        {
          description: 'Add all files and commit',
          parameters: { 
            message: 'Update documentation',
            addAll: true 
          },
        },
        {
          description: 'Commit specific files',
          parameters: { 
            message: 'Update package.json',
            files: ['package.json', 'package-lock.json']
          },
        },
        {
          description: 'Commit with custom author',
          parameters: { 
            message: 'Initial commit',
            author: 'John Doe <<EMAIL>>',
            addAll: true
          },
        },
      ],
    };
  }

  private resolvePath(path: string, workingDirectory?: string): string {
    if (isAbsolute(path)) {
      return resolve(path);
    }
    return resolve(workingDirectory || process.cwd(), path);
  }

  private formatCommitResult(result: any, latestCommit: any, repoPath: string): string {
    const lines = [`Git Commit Created in ${repoPath}\n`];

    lines.push(`Commit: ${result.commit}`);
    lines.push(`Branch: ${result.branch}`);
    lines.push(`Summary: ${result.summary}`);

    if (latestCommit) {
      lines.push(`Author: ${latestCommit.author_name} <${latestCommit.author_email}>`);
      lines.push(`Date: ${latestCommit.date}`);
      lines.push(`Message: ${latestCommit.message}`);
    }

    return lines.join('\n');
  }
}
