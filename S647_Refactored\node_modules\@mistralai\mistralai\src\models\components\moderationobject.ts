/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type ModerationObject = {
  /**
   * Moderation result thresholds
   */
  categories?: { [k: string]: boolean } | undefined;
  /**
   * Moderation result
   */
  categoryScores?: { [k: string]: number } | undefined;
};

/** @internal */
export const ModerationObject$inboundSchema: z.ZodType<
  ModerationObject,
  z.ZodTypeDef,
  unknown
> = z.object({
  categories: z.record(z.boolean()).optional(),
  category_scores: z.record(z.number()).optional(),
}).transform((v) => {
  return remap$(v, {
    "category_scores": "categoryScores",
  });
});

/** @internal */
export type ModerationObject$Outbound = {
  categories?: { [k: string]: boolean } | undefined;
  category_scores?: { [k: string]: number } | undefined;
};

/** @internal */
export const ModerationObject$outboundSchema: z.ZodType<
  ModerationObject$Outbound,
  z.ZodTypeDef,
  ModerationObject
> = z.object({
  categories: z.record(z.boolean()).optional(),
  categoryScores: z.record(z.number()).optional(),
}).transform((v) => {
  return remap$(v, {
    categoryScores: "category_scores",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModerationObject$ {
  /** @deprecated use `ModerationObject$inboundSchema` instead. */
  export const inboundSchema = ModerationObject$inboundSchema;
  /** @deprecated use `ModerationObject$outboundSchema` instead. */
  export const outboundSchema = ModerationObject$outboundSchema;
  /** @deprecated use `ModerationObject$Outbound` instead. */
  export type Outbound = ModerationObject$Outbound;
}

export function moderationObjectToJSON(
  moderationObject: ModerationObject,
): string {
  return JSON.stringify(
    ModerationObject$outboundSchema.parse(moderationObject),
  );
}

export function moderationObjectFromJSON(
  jsonString: string,
): SafeParseResult<ModerationObject, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ModerationObject$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ModerationObject' from JSON`,
  );
}
