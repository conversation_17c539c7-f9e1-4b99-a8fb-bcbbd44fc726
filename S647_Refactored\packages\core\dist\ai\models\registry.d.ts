/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ModelInfo } from '@inkbytefo/s647-shared';
/**
 * Model registry for managing available models
 */
export declare class ModelRegistry {
    private models;
    register(model: ModelInfo): void;
    unregister(modelId: string): void;
    get(modelId: string): ModelInfo | undefined;
    getAll(): ModelInfo[];
    has(modelId: string): boolean;
    clear(): void;
}
//# sourceMappingURL=registry.d.ts.map