/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Provider, ProviderType, ProviderId } from '@inkbytefo/s647-shared';
import type { IProviderRegistry } from '../interfaces/provider.js';
/**
 * Provider registry implementation
 */
export declare class ProviderRegistry implements IProviderRegistry {
    private providers;
    private static instance;
    /**
     * Get the singleton instance
     */
    static getInstance(): ProviderRegistry;
    /**
     * Register a provider
     */
    register(provider: Provider): void;
    /**
     * Unregister a provider
     */
    unregister(id: ProviderId): void;
    /**
     * Get a provider by ID
     */
    get(id: ProviderId): Provider | undefined;
    /**
     * Get all providers
     */
    getAll(): Provider[];
    /**
     * Get providers by type
     */
    getByType(type: ProviderType): Provider[];
    /**
     * Get available providers
     */
    getAvailable(): Provider[];
    /**
     * Find the best provider for a model
     */
    findBestProvider(modelId: string): Provider | undefined;
    /**
     * Dispose all providers
     */
    dispose(): Promise<void>;
    /**
     * Check if a provider supports a specific model
     */
    private providerSupportsModel;
    /**
     * Get provider statistics
     */
    getStats(): {
        total: number;
        available: number;
        byType: Record<ProviderType, number>;
    };
    /**
     * Clear all providers (for testing)
     */
    clear(): void;
}
//# sourceMappingURL=registry.d.ts.map