import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
export declare const Source: {
    readonly Upload: "upload";
    readonly Repository: "repository";
    readonly Mistral: "mistral";
};
export type Source = OpenEnum<typeof Source>;
/** @internal */
export declare const Source$inboundSchema: z.ZodType<Source, z.ZodTypeDef, unknown>;
/** @internal */
export declare const Source$outboundSchema: z.ZodType<Source, z.ZodTypeDef, Source>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace Source$ {
    /** @deprecated use `Source$inboundSchema` instead. */
    const inboundSchema: z.ZodType<Source, z.ZodTypeDef, unknown>;
    /** @deprecated use `Source$outboundSchema` instead. */
    const outboundSchema: z.ZodType<Source, z.ZodTypeDef, Source>;
}
//# sourceMappingURL=source.d.ts.map