/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  CustomProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * Custom provider implementation
 * Supports user-defined endpoints with OpenAI-compatible APIs
 */
export class CustomProvider extends BaseProvider {
  private client: any; // OpenAI client instance

  constructor(id: ProviderId, config: CustomProviderConfig) {
    super(id, config);
  }

  public get customConfig(): CustomProviderConfig {
    return this.config as CustomProviderConfig;
  }

  /**
   * Initialize the Custom provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();

      // Initialize OpenAI client with custom base URL
      const { OpenAI } = await import('openai');

      if (!this.customConfig.baseUrl) {
        throw new Error('Custom provider base URL is required');
      }

      const headers: Record<string, string> = {
        ...this.customConfig.headers,
      };

      // Handle different authentication types
      if (this.customConfig.authType === 'bearer' && this.customConfig.apiKey) {
        headers['Authorization'] = `Bearer ${this.customConfig.apiKey}`;
      } else if (this.customConfig.authType === 'api-key' && this.customConfig.apiKey) {
        const headerName = this.customConfig.authHeader || 'X-API-Key';
        headers[headerName] = this.customConfig.apiKey;
      } else if (this.customConfig.authType === 'basic' && this.customConfig.apiKey) {
        headers['Authorization'] = `Basic ${btoa(this.customConfig.apiKey)}`;
      }

      this.client = new OpenAI({
        apiKey: this.customConfig.apiKey || 'dummy-key',
        baseURL: this.customConfig.baseUrl,
        timeout: this.customConfig.timeout || 30000,
        maxRetries: this.customConfig.retries || 3,
        defaultHeaders: headers,
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Try to list models or make a simple request
      try {
        await this.client.models.list();
      } catch {
        // If models endpoint doesn't exist, try a simple chat completion
        await this.client.chat.completions.create({
          model: 'test',
          messages: [{ role: 'user', content: 'Hi' }],
          max_tokens: 1,
        });
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available models from custom endpoint
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      // Try to get models from the endpoint
      try {
        const response = await this.client.models.list();
        const models: ModelInfo[] = response.data.map((model: any) => ({
          id: model.id,
          name: model.id,
          provider: 'custom' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: model.id.includes('embedding'),
            vision: model.id.includes('vision'),
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: model.id.includes('vision'),
          },
          contextLength: model.context_length || 4096,
          maxTokens: model.max_completion_tokens || 4096,
        }));

        return { success: true, data: models };
      } catch {
        // If models endpoint doesn't exist, return configured models or default
        const models: ModelInfo[] = this.customConfig.models || [{
          id: 'default',
          name: 'Default Model',
          provider: 'custom' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: false,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: false,
          },
          contextLength: 4096,
          maxTokens: 4096,
        }];

        return { success: true, data: models };
      }
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion (OpenAI-compatible)
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: false,
        tools: request.tools,
        tool_choice: request.toolChoice,
        user: request.user,
      });

      return { success: true, data: response as ChatCompletionResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion (OpenAI-compatible)
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const stream = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: true,
        tools: request.tools,
        tool_choice: request.toolChoice,
        user: request.user,
      });

      return { success: true, data: stream as AsyncIterable<ChatCompletionChunk> };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings (OpenAI-compatible)
   */
  public async createEmbedding(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.embeddings.create({
        model: request.model,
        input: request.input,
        user: request.user,
      });

      return { success: true, data: response as EmbeddingResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      // Custom endpoints typically don't have token counting
      // Use estimation based on character count
      const text = typeof input === 'string'
        ? input
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');

      // Use OpenAI's estimation (roughly 4 characters per token)
      const estimatedTokens = Math.ceil(text.length / 4);
      return { success: true, data: estimatedTokens };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }
}
