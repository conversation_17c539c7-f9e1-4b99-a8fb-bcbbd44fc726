/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type JobsApiRoutesFineTuningCancelFineTuningJobRequest = {
  /**
   * The ID of the job to cancel.
   */
  jobId: string;
};

/**
 * OK
 */
export type JobsApiRoutesFineTuningCancelFineTuningJobResponse =
  | (components.ClassifierDetailedJobOut & { jobType: "classifier" })
  | (components.CompletionDetailedJobOut & { jobType: "completion" });

/** @internal */
export const JobsApiRoutesFineTuningCancelFineTuningJobRequest$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningCancelFineTuningJobRequest,
    z.ZodTypeDef,
    unknown
  > = z.object({
    job_id: z.string(),
  }).transform((v) => {
    return remap$(v, {
      "job_id": "jobId",
    });
  });

/** @internal */
export type JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound = {
  job_id: string;
};

/** @internal */
export const JobsApiRoutesFineTuningCancelFineTuningJobRequest$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningCancelFineTuningJobRequest
  > = z.object({
    jobId: z.string(),
  }).transform((v) => {
    return remap$(v, {
      jobId: "job_id",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningCancelFineTuningJobRequest$ {
  /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobRequest$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningCancelFineTuningJobRequest$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobRequest$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningCancelFineTuningJobRequest$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningCancelFineTuningJobRequest$Outbound;
}

export function jobsApiRoutesFineTuningCancelFineTuningJobRequestToJSON(
  jobsApiRoutesFineTuningCancelFineTuningJobRequest:
    JobsApiRoutesFineTuningCancelFineTuningJobRequest,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningCancelFineTuningJobRequest$outboundSchema.parse(
      jobsApiRoutesFineTuningCancelFineTuningJobRequest,
    ),
  );
}

export function jobsApiRoutesFineTuningCancelFineTuningJobRequestFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningCancelFineTuningJobRequest,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningCancelFineTuningJobRequest$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningCancelFineTuningJobRequest' from JSON`,
  );
}

/** @internal */
export const JobsApiRoutesFineTuningCancelFineTuningJobResponse$inboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningCancelFineTuningJobResponse,
    z.ZodTypeDef,
    unknown
  > = z.union([
    components.ClassifierDetailedJobOut$inboundSchema.and(
      z.object({ job_type: z.literal("classifier") }).transform((v) => ({
        jobType: v.job_type,
      })),
    ),
    components.CompletionDetailedJobOut$inboundSchema.and(
      z.object({ job_type: z.literal("completion") }).transform((v) => ({
        jobType: v.job_type,
      })),
    ),
  ]);

/** @internal */
export type JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound =
  | (components.ClassifierDetailedJobOut$Outbound & { job_type: "classifier" })
  | (components.CompletionDetailedJobOut$Outbound & { job_type: "completion" });

/** @internal */
export const JobsApiRoutesFineTuningCancelFineTuningJobResponse$outboundSchema:
  z.ZodType<
    JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound,
    z.ZodTypeDef,
    JobsApiRoutesFineTuningCancelFineTuningJobResponse
  > = z.union([
    components.ClassifierDetailedJobOut$outboundSchema.and(
      z.object({ jobType: z.literal("classifier") }).transform((v) => ({
        job_type: v.jobType,
      })),
    ),
    components.CompletionDetailedJobOut$outboundSchema.and(
      z.object({ jobType: z.literal("completion") }).transform((v) => ({
        job_type: v.jobType,
      })),
    ),
  ]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace JobsApiRoutesFineTuningCancelFineTuningJobResponse$ {
  /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobResponse$inboundSchema` instead. */
  export const inboundSchema =
    JobsApiRoutesFineTuningCancelFineTuningJobResponse$inboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobResponse$outboundSchema` instead. */
  export const outboundSchema =
    JobsApiRoutesFineTuningCancelFineTuningJobResponse$outboundSchema;
  /** @deprecated use `JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound` instead. */
  export type Outbound =
    JobsApiRoutesFineTuningCancelFineTuningJobResponse$Outbound;
}

export function jobsApiRoutesFineTuningCancelFineTuningJobResponseToJSON(
  jobsApiRoutesFineTuningCancelFineTuningJobResponse:
    JobsApiRoutesFineTuningCancelFineTuningJobResponse,
): string {
  return JSON.stringify(
    JobsApiRoutesFineTuningCancelFineTuningJobResponse$outboundSchema.parse(
      jobsApiRoutesFineTuningCancelFineTuningJobResponse,
    ),
  );
}

export function jobsApiRoutesFineTuningCancelFineTuningJobResponseFromJSON(
  jsonString: string,
): SafeParseResult<
  JobsApiRoutesFineTuningCancelFineTuningJobResponse,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      JobsApiRoutesFineTuningCancelFineTuningJobResponse$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'JobsApiRoutesFineTuningCancelFineTuningJobResponse' from JSON`,
  );
}
