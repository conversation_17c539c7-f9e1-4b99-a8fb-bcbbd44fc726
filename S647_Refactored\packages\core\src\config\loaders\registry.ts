/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { Configuration } from '@inkbytefo/s647-shared';
import type { ConfigLoader, ConfigLoaderOptions, LoadResult, MergeOptions } from './types.js';
import { DefaultConfigLoader } from './default-loader.js';
import { FileConfigLoader } from './file-loader.js';
import { EnvironmentConfigLoader } from './env-loader.js';
import { CliConfigLoader } from './cli-loader.js';

/**
 * Configuration loader registry
 * Manages multiple configuration loaders and merges their results
 */
export class ConfigLoaderRegistry {
  private readonly loaders: Map<string, ConfigLoader> = new Map();

  constructor() {
    // Register default loaders
    this.registerDefaultLoaders();
  }

  /**
   * Register a configuration loader
   */
  public register(loader: ConfigLoader): void {
    this.loaders.set(loader.name, loader);
  }

  /**
   * Unregister a configuration loader
   */
  public unregister(name: string): boolean {
    return this.loaders.delete(name);
  }

  /**
   * Get a configuration loader by name
   */
  public get(name: string): ConfigLoader | undefined {
    return this.loaders.get(name);
  }

  /**
   * Get all registered loaders
   */
  public getAll(): ConfigLoader[] {
    return Array.from(this.loaders.values());
  }

  /**
   * Load configuration from all loaders and merge results
   */
  public async loadConfiguration(options?: ConfigLoaderOptions): Promise<LoadResult> {
    const results: LoadResult[] = [];
    const warnings: string[] = [];
    
    // Get loaders sorted by priority (highest first)
    const sortedLoaders = this.getAll().sort((a, b) => b.priority - a.priority);
    
    // Load from each loader
    for (const loader of sortedLoaders) {
      try {
        if (await loader.canLoad(options)) {
          const result = await loader.load(options);
          results.push(result);
          
          if (result.warnings) {
            warnings.push(...result.warnings);
          }
        }
      } catch (error) {
        warnings.push(`Loader ${loader.name} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Merge configurations
    const mergedConfig = this.mergeConfigurations(results);
    
    return {
      success: true,
      config: mergedConfig,
      source: {
        type: 'file', // Primary source type
        priority: 0,
        timestamp: Date.now(),
        metadata: {
          loaderCount: results.length,
          sources: results.map(r => r.source).filter(Boolean),
        },
      },
      ...(warnings.length > 0 && { warnings }),
    };
  }

  /**
   * Merge configurations from multiple sources
   */
  private mergeConfigurations(results: LoadResult[], options?: MergeOptions): Partial<Configuration> {
    const merged: any = {};
    
    // Start with lowest priority and work up
    const sortedResults = results
      .filter(r => r.success && r.config)
      .sort((a, b) => (a.source?.priority || 0) - (b.source?.priority || 0));
    
    for (const result of sortedResults) {
      if (result.config) {
        this.deepMerge(merged, result.config, options);
      }
    }
    
    return merged;
  }

  /**
   * Deep merge two configuration objects
   */
  private deepMerge(target: any, source: any, options?: MergeOptions): void {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = target[key];
        
        if (this.isObject(sourceValue) && this.isObject(targetValue)) {
          // Recursively merge objects
          this.deepMerge(targetValue, sourceValue, options);
        } else if (Array.isArray(sourceValue) && Array.isArray(targetValue)) {
          // Handle array merging based on strategy
          const strategy = options?.fieldStrategies?.[key] || options?.strategy || 'replace';
          
          switch (strategy) {
            case 'append':
              target[key] = [...targetValue, ...sourceValue];
              break;
            case 'merge':
              // Merge unique values
              target[key] = [...new Set([...targetValue, ...sourceValue])];
              break;
            case 'replace':
            default:
              target[key] = sourceValue;
              break;
          }
        } else {
          // Replace primitive values
          target[key] = sourceValue;
        }
      }
    }
  }

  /**
   * Check if value is a plain object
   */
  private isObject(value: any): boolean {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  /**
   * Register default configuration loaders
   */
  private registerDefaultLoaders(): void {
    this.register(new DefaultConfigLoader());
    this.register(new FileConfigLoader());
    this.register(new EnvironmentConfigLoader());
    this.register(new CliConfigLoader());
  }
}
