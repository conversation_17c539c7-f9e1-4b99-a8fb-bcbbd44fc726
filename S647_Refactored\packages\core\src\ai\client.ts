/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  AsyncResult,
  Logger,
} from '@inkbytefo/s647-shared';
import { AIManager } from './manager.js';

/**
 * AI Client - High-level interface for AI operations
 */
export class AIClient {
  private aiManager: AIManager;

  constructor(aiManager: AIManager, private logger?: Logger) {
    this.aiManager = aiManager;
  }

  /**
   * Create a chat completion
   */
  public async chat(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    this.logger?.debug('Creating chat completion', { model: request.model });
    return this.aiManager.createChatCompletion(request);
  }

  /**
   * Create a streaming chat completion
   */
  public async chatStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    this.logger?.debug('Creating streaming chat completion', { model: request.model });
    return this.aiManager.createChatCompletionStream(request);
  }

  /**
   * Create embeddings
   */
  public async embed(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    this.logger?.debug('Creating embedding', { model: request.model });
    return this.aiManager.createEmbedding(request);
  }

  /**
   * Count tokens
   */
  public async countTokens(
    input: string,
    modelId?: string
  ): AsyncResult<number> {
    this.logger?.debug('Counting tokens', { modelId });
    return this.aiManager.countTokens(input, modelId);
  }
}
