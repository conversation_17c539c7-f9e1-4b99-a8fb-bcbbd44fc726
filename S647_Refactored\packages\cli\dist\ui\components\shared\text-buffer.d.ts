/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Viewport dimensions for text buffer
 */
export interface Viewport {
    height: number;
    width: number;
}
/**
 * Text buffer state
 */
export interface TextBufferState {
    lines: string[];
    cursorRow: number;
    cursorCol: number;
    preferredCol: number | null;
    undoStack: UndoHistoryEntry[];
    redoStack: UndoHistoryEntry[];
    clipboard: string | null;
    selectionAnchor: [number, number] | null;
    viewportWidth: number;
}
/**
 * Undo history entry
 */
interface UndoHistoryEntry {
    lines: string[];
    cursorRow: number;
    cursorCol: number;
}
/**
 * Text buffer actions
 */
export type TextBufferAction = {
    type: 'set_text';
    payload: string;
    pushToUndo?: boolean;
} | {
    type: 'insert';
    payload: string;
} | {
    type: 'newline';
} | {
    type: 'backspace';
} | {
    type: 'delete';
} | {
    type: 'move';
    payload: {
        direction: 'up' | 'down' | 'left' | 'right' | 'home' | 'end';
    };
} | {
    type: 'undo';
} | {
    type: 'redo';
} | {
    type: 'replace_range';
    payload: {
        startRow: number;
        startCol: number;
        endRow: number;
        endCol: number;
        text: string;
    };
} | {
    type: 'move_to_offset';
    payload: {
        offset: number;
    };
} | {
    type: 'delete_word_left';
} | {
    type: 'delete_word_right';
} | {
    type: 'kill_line_right';
} | {
    type: 'kill_line_left';
};
/**
 * Text buffer interface
 */
export interface TextBuffer {
    lines: string[];
    text: string;
    cursor: [number, number];
    preferredCol: number | null;
    selectionAnchor: [number, number] | null;
    allVisualLines: string[];
    viewportVisualLines: string[];
    visualCursor: [number, number];
    visualScrollRow: number;
    setText: (text: string) => void;
    insert: (text: string) => void;
    newline: () => void;
    backspace: () => void;
    del: () => void;
    move: (direction: 'up' | 'down' | 'left' | 'right' | 'home' | 'end') => void;
    undo: () => void;
    redo: () => void;
    replaceRange: (startRow: number, startCol: number, endRow: number, endCol: number, text: string) => void;
    replaceRangeByOffset: (startOffset: number, endOffset: number, replacementText: string) => void;
    moveToOffset: (offset: number) => void;
    deleteWordLeft: () => void;
    deleteWordRight: () => void;
    killLineRight: () => void;
    killLineLeft: () => void;
    handleInput: (input: string, key: any) => void;
    openInExternalEditor: () => void;
}
/**
 * Text buffer props
 */
interface UseTextBufferProps {
    initialText?: string;
    initialCursorOffset?: number;
    viewport: Viewport;
    stdin?: NodeJS.ReadStream | null;
    setRawMode?: (mode: boolean) => void;
    onChange?: (text: string) => void;
    isValidPath: (path: string) => boolean;
    shellModeActive?: boolean;
}
/**
 * Text buffer reducer
 */
export declare function textBufferReducer(state: TextBufferState, action: TextBufferAction): TextBufferState;
/**
 * useTextBuffer hook
 */
export declare function useTextBuffer({ initialText, initialCursorOffset, viewport, stdin, setRawMode, onChange, isValidPath, shellModeActive, }: UseTextBufferProps): TextBuffer;
export {};
//# sourceMappingURL=text-buffer.d.ts.map