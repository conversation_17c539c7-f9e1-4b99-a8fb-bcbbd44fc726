/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { promises as fs } from 'fs';
import { join, resolve, isAbsolute, relative } from 'path';
import { z } from 'zod';
import micromatch from 'micromatch';
import type {
  <PERSON><PERSON><PERSON><PERSON>ms,
  ToolContext,
  ToolResult,
  ToolHelp,
  AsyncResult,
} from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';

const FileSearchParams = z.object({
  directory: z.string().describe('Directory to search in'),
  pattern: z.string().optional().default('*').describe('File pattern to match (glob pattern)'),
  recursive: z.boolean().optional().default(true).describe('Search recursively in subdirectories'),
  includeHidden: z.boolean().optional().default(false).describe('Include hidden files and directories'),
  maxDepth: z.number().optional().default(10).describe('Maximum directory depth to search'),
  maxResults: z.number().optional().default(100).describe('Maximum number of results to return'),
});

type FileSearchParams = z.infer<typeof FileSearchParams>;

interface FileInfo {
  path: string;
  relativePath: string;
  name: string;
  size: number;
  isDirectory: boolean;
  lastModified: string;
}

/**
 * Tool for searching files and directories
 */
export class FileSearchTool extends BaseTool {
  constructor() {
    super(
      'file-search',
      'File Search',
      'Search for files and directories using patterns',
      '1.0.0',
      'file',
      FileSearchParams
    );
  }

  public async execute(
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    try {
      const { directory, pattern, recursive, includeHidden, maxDepth, maxResults } = 
        await this.validateAndParse<FileSearchParams>(params);

      // Resolve directory path
      const resolvedDir = this.resolvePath(directory, context.workingDirectory);

      // Check if directory exists
      try {
        const stats = await fs.stat(resolvedDir);
        if (!stats.isDirectory()) {
          return this.error(`Path is not a directory: ${resolvedDir}`);
        }
      } catch {
        return this.error(`Directory not found: ${resolvedDir}`);
      }

      // Search for files
      const results = await this.searchFiles(
        resolvedDir,
        pattern,
        recursive,
        includeHidden,
        maxDepth,
        maxResults
      );

      const resultText = this.formatResults(results, resolvedDir);

      return this.success({
        type: 'text',
        content: resultText,
        metadata: {
          directory: resolvedDir,
          pattern,
          totalResults: results.length,
          searchOptions: {
            recursive,
            includeHidden,
            maxDepth,
            maxResults,
          },
        },
      });
    } catch (error) {
      return this.error(this.handleError(error));
    }
  }

  public getHelp(): ToolHelp {
    return {
      description: 'Search for files and directories using glob patterns',
      parameters: {
        directory: {
          type: 'string',
          description: 'Directory to search in (relative or absolute)',
          required: true,
        },
        pattern: {
          type: 'string',
          description: 'File pattern to match (glob pattern, e.g., "*.js", "**/*.ts")',
          required: false,
          default: '*',
        },
        recursive: {
          type: 'boolean',
          description: 'Search recursively in subdirectories',
          required: false,
          default: true,
        },
        includeHidden: {
          type: 'boolean',
          description: 'Include hidden files and directories (starting with .)',
          required: false,
          default: false,
        },
        maxDepth: {
          type: 'number',
          description: 'Maximum directory depth to search',
          required: false,
          default: 10,
        },
        maxResults: {
          type: 'number',
          description: 'Maximum number of results to return',
          required: false,
          default: 100,
        },
      },
      examples: [
        {
          description: 'Search for all JavaScript files',
          parameters: { 
            directory: './src', 
            pattern: '*.js' 
          },
        },
        {
          description: 'Search for TypeScript files recursively',
          parameters: { 
            directory: '.', 
            pattern: '**/*.ts',
            recursive: true 
          },
        },
        {
          description: 'Search for config files including hidden ones',
          parameters: { 
            directory: '.', 
            pattern: '*config*',
            includeHidden: true 
          },
        },
      ],
    };
  }

  private resolvePath(path: string, workingDirectory?: string): string {
    if (isAbsolute(path)) {
      return resolve(path);
    }
    return resolve(workingDirectory || process.cwd(), path);
  }

  private async searchFiles(
    directory: string,
    pattern: string,
    recursive: boolean,
    includeHidden: boolean,
    maxDepth: number,
    maxResults: number,
    currentDepth = 0
  ): Promise<FileInfo[]> {
    const results: FileInfo[] = [];

    if (currentDepth > maxDepth || results.length >= maxResults) {
      return results;
    }

    try {
      const entries = await fs.readdir(directory, { withFileTypes: true });

      for (const entry of entries) {
        if (results.length >= maxResults) break;

        // Skip hidden files if not included
        if (!includeHidden && entry.name.startsWith('.')) {
          continue;
        }

        const fullPath = join(directory, entry.name);
        const stats = await fs.stat(fullPath);

        // Check if file matches pattern
        if (micromatch.isMatch(entry.name, pattern)) {
          results.push({
            path: fullPath,
            relativePath: relative(directory, fullPath),
            name: entry.name,
            size: stats.size,
            isDirectory: entry.isDirectory(),
            lastModified: stats.mtime.toISOString(),
          });
        }

        // Recurse into directories if enabled
        if (recursive && entry.isDirectory()) {
          const subResults = await this.searchFiles(
            fullPath,
            pattern,
            recursive,
            includeHidden,
            maxDepth,
            maxResults - results.length,
            currentDepth + 1
          );
          results.push(...subResults);
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }

    return results;
  }

  private formatResults(results: FileInfo[], baseDirectory: string): string {
    if (results.length === 0) {
      return `No files found matching the pattern in ${baseDirectory}`;
    }

    const lines = [`Found ${results.length} files in ${baseDirectory}:\n`];

    for (const file of results) {
      const type = file.isDirectory ? '[DIR]' : '[FILE]';
      const size = file.isDirectory ? '' : ` (${this.formatSize(file.size)})`;
      lines.push(`${type} ${file.relativePath}${size}`);
    }

    return lines.join('\n');
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}
