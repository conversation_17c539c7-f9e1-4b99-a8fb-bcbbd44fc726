/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { DEFAULT_CONFIG } from '@inkbytefo/s647-shared';
/**
 * Default configuration loader
 * Provides fallback configuration values
 */
export class DefaultConfigLoader {
    name = 'default';
    priority = 0; // Lowest priority
    /**
     * Load default configuration
     */
    async load(_options) {
        try {
            return {
                success: true,
                config: DEFAULT_CONFIG,
                source: {
                    type: 'default',
                    priority: this.priority,
                    timestamp: Date.now(),
                    metadata: {
                        loader: this.name,
                        version: DEFAULT_CONFIG.version,
                    },
                },
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error : new Error('Unknown error loading default config'),
            };
        }
    }
    /**
     * Default loader can always load
     */
    async canLoad(_options) {
        return true;
    }
}
//# sourceMappingURL=default-loader.js.map