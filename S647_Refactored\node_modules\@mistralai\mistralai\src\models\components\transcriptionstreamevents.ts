/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  TranscriptionStreamDone,
  TranscriptionStreamDone$inboundSchema,
  TranscriptionStreamDone$Outbound,
  TranscriptionStreamDone$outboundSchema,
} from "./transcriptionstreamdone.js";
import {
  TranscriptionStreamEventTypes,
  TranscriptionStreamEventTypes$inboundSchema,
  TranscriptionStreamEventTypes$outboundSchema,
} from "./transcriptionstreameventtypes.js";
import {
  TranscriptionStreamLanguage,
  TranscriptionStreamLanguage$inboundSchema,
  TranscriptionStreamLanguage$Outbound,
  TranscriptionStreamLanguage$outboundSchema,
} from "./transcriptionstreamlanguage.js";
import {
  TranscriptionStreamSegmentDelta,
  TranscriptionStreamSegmentDelta$inboundSchema,
  TranscriptionStreamSegmentDelta$Outbound,
  TranscriptionStreamSegmentDelta$outboundSchema,
} from "./transcriptionstreamsegmentdelta.js";
import {
  TranscriptionStreamTextDelta,
  TranscriptionStreamTextDelta$inboundSchema,
  TranscriptionStreamTextDelta$Outbound,
  TranscriptionStreamTextDelta$outboundSchema,
} from "./transcriptionstreamtextdelta.js";

export type TranscriptionStreamEventsData =
  | (TranscriptionStreamLanguage & { type: "transcription.language" })
  | (TranscriptionStreamTextDelta & { type: "transcription.text.delta" })
  | (TranscriptionStreamSegmentDelta & { type: "transcription.segment" })
  | (TranscriptionStreamDone & { type: "transcription.done" });

export type TranscriptionStreamEvents = {
  event: TranscriptionStreamEventTypes;
  data:
    | (TranscriptionStreamLanguage & { type: "transcription.language" })
    | (TranscriptionStreamTextDelta & { type: "transcription.text.delta" })
    | (TranscriptionStreamSegmentDelta & { type: "transcription.segment" })
    | (TranscriptionStreamDone & { type: "transcription.done" });
};

/** @internal */
export const TranscriptionStreamEventsData$inboundSchema: z.ZodType<
  TranscriptionStreamEventsData,
  z.ZodTypeDef,
  unknown
> = z.union([
  TranscriptionStreamLanguage$inboundSchema.and(
    z.object({ type: z.literal("transcription.language") }).transform((v) => ({
      type: v.type,
    })),
  ),
  TranscriptionStreamTextDelta$inboundSchema.and(
    z.object({ type: z.literal("transcription.text.delta") }).transform((
      v,
    ) => ({ type: v.type })),
  ),
  TranscriptionStreamSegmentDelta$inboundSchema.and(
    z.object({ type: z.literal("transcription.segment") }).transform((v) => ({
      type: v.type,
    })),
  ),
  TranscriptionStreamDone$inboundSchema.and(
    z.object({ type: z.literal("transcription.done") }).transform((v) => ({
      type: v.type,
    })),
  ),
]);

/** @internal */
export type TranscriptionStreamEventsData$Outbound =
  | (TranscriptionStreamLanguage$Outbound & { type: "transcription.language" })
  | (TranscriptionStreamTextDelta$Outbound & {
    type: "transcription.text.delta";
  })
  | (TranscriptionStreamSegmentDelta$Outbound & {
    type: "transcription.segment";
  })
  | (TranscriptionStreamDone$Outbound & { type: "transcription.done" });

/** @internal */
export const TranscriptionStreamEventsData$outboundSchema: z.ZodType<
  TranscriptionStreamEventsData$Outbound,
  z.ZodTypeDef,
  TranscriptionStreamEventsData
> = z.union([
  TranscriptionStreamLanguage$outboundSchema.and(
    z.object({ type: z.literal("transcription.language") }).transform((v) => ({
      type: v.type,
    })),
  ),
  TranscriptionStreamTextDelta$outboundSchema.and(
    z.object({ type: z.literal("transcription.text.delta") }).transform((
      v,
    ) => ({ type: v.type })),
  ),
  TranscriptionStreamSegmentDelta$outboundSchema.and(
    z.object({ type: z.literal("transcription.segment") }).transform((v) => ({
      type: v.type,
    })),
  ),
  TranscriptionStreamDone$outboundSchema.and(
    z.object({ type: z.literal("transcription.done") }).transform((v) => ({
      type: v.type,
    })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamEventsData$ {
  /** @deprecated use `TranscriptionStreamEventsData$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamEventsData$inboundSchema;
  /** @deprecated use `TranscriptionStreamEventsData$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamEventsData$outboundSchema;
  /** @deprecated use `TranscriptionStreamEventsData$Outbound` instead. */
  export type Outbound = TranscriptionStreamEventsData$Outbound;
}

export function transcriptionStreamEventsDataToJSON(
  transcriptionStreamEventsData: TranscriptionStreamEventsData,
): string {
  return JSON.stringify(
    TranscriptionStreamEventsData$outboundSchema.parse(
      transcriptionStreamEventsData,
    ),
  );
}

export function transcriptionStreamEventsDataFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionStreamEventsData, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionStreamEventsData$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionStreamEventsData' from JSON`,
  );
}

/** @internal */
export const TranscriptionStreamEvents$inboundSchema: z.ZodType<
  TranscriptionStreamEvents,
  z.ZodTypeDef,
  unknown
> = z.object({
  event: TranscriptionStreamEventTypes$inboundSchema,
  data: z.string().transform((v, ctx) => {
    try {
      return JSON.parse(v);
    } catch (err) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `malformed json: ${err}`,
      });
      return z.NEVER;
    }
  }).pipe(
    z.union([
      TranscriptionStreamLanguage$inboundSchema.and(
        z.object({ type: z.literal("transcription.language") }).transform((
          v,
        ) => ({ type: v.type })),
      ),
      TranscriptionStreamTextDelta$inboundSchema.and(
        z.object({ type: z.literal("transcription.text.delta") }).transform((
          v,
        ) => ({ type: v.type })),
      ),
      TranscriptionStreamSegmentDelta$inboundSchema.and(
        z.object({ type: z.literal("transcription.segment") }).transform((
          v,
        ) => ({ type: v.type })),
      ),
      TranscriptionStreamDone$inboundSchema.and(
        z.object({ type: z.literal("transcription.done") }).transform((v) => ({
          type: v.type,
        })),
      ),
    ]),
  ),
});

/** @internal */
export type TranscriptionStreamEvents$Outbound = {
  event: string;
  data:
    | (TranscriptionStreamLanguage$Outbound & {
      type: "transcription.language";
    })
    | (TranscriptionStreamTextDelta$Outbound & {
      type: "transcription.text.delta";
    })
    | (TranscriptionStreamSegmentDelta$Outbound & {
      type: "transcription.segment";
    })
    | (TranscriptionStreamDone$Outbound & { type: "transcription.done" });
};

/** @internal */
export const TranscriptionStreamEvents$outboundSchema: z.ZodType<
  TranscriptionStreamEvents$Outbound,
  z.ZodTypeDef,
  TranscriptionStreamEvents
> = z.object({
  event: TranscriptionStreamEventTypes$outboundSchema,
  data: z.union([
    TranscriptionStreamLanguage$outboundSchema.and(
      z.object({ type: z.literal("transcription.language") }).transform((
        v,
      ) => ({ type: v.type })),
    ),
    TranscriptionStreamTextDelta$outboundSchema.and(
      z.object({ type: z.literal("transcription.text.delta") }).transform((
        v,
      ) => ({ type: v.type })),
    ),
    TranscriptionStreamSegmentDelta$outboundSchema.and(
      z.object({ type: z.literal("transcription.segment") }).transform((v) => ({
        type: v.type,
      })),
    ),
    TranscriptionStreamDone$outboundSchema.and(
      z.object({ type: z.literal("transcription.done") }).transform((v) => ({
        type: v.type,
      })),
    ),
  ]),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TranscriptionStreamEvents$ {
  /** @deprecated use `TranscriptionStreamEvents$inboundSchema` instead. */
  export const inboundSchema = TranscriptionStreamEvents$inboundSchema;
  /** @deprecated use `TranscriptionStreamEvents$outboundSchema` instead. */
  export const outboundSchema = TranscriptionStreamEvents$outboundSchema;
  /** @deprecated use `TranscriptionStreamEvents$Outbound` instead. */
  export type Outbound = TranscriptionStreamEvents$Outbound;
}

export function transcriptionStreamEventsToJSON(
  transcriptionStreamEvents: TranscriptionStreamEvents,
): string {
  return JSON.stringify(
    TranscriptionStreamEvents$outboundSchema.parse(transcriptionStreamEvents),
  );
}

export function transcriptionStreamEventsFromJSON(
  jsonString: string,
): SafeParseResult<TranscriptionStreamEvents, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TranscriptionStreamEvents$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TranscriptionStreamEvents' from JSON`,
  );
}
