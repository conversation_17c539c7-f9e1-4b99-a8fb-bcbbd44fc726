/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { z } from 'zod';
import { htmlToText } from 'html-to-text';
import type {
  ToolParams,
  ToolContext,
  ToolResult,
  ToolHelp,
  AsyncResult,
} from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';

const WebFetchParams = z.object({
  url: z.string().url().describe('URL to fetch'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'HEAD']).optional().default('GET').describe('HTTP method'),
  headers: z.record(z.string()).optional().describe('HTTP headers to send'),
  body: z.string().optional().describe('Request body (for POST/PUT requests)'),
  timeout: z.number().optional().default(30000).describe('Request timeout in milliseconds'),
  followRedirects: z.boolean().optional().default(true).describe('Follow HTTP redirects'),
  extractText: z.boolean().optional().default(true).describe('Extract text from HTML content'),
  maxSize: z.number().optional().default(5 * 1024 * 1024).describe('Maximum response size in bytes'),
});

type WebFetchParams = z.infer<typeof WebFetchParams>;

/**
 * Tool for fetching web content
 */
export class WebFetchTool extends BaseTool {
  constructor() {
    super(
      'web-fetch',
      'Web Fetch',
      'Fetch content from a web URL',
      '1.0.0',
      'web',
      WebFetchParams
    );
  }

  public async execute(
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    try {
      const { url, method, headers, body, timeout, followRedirects, extractText, maxSize } = 
        await this.validateAndParse<WebFetchParams>(params);

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      // Prepare fetch options
      const fetchOptions: RequestInit = {
        method,
        headers: {
          'User-Agent': 'S647-Agent/1.0',
          ...headers,
        },
        ...(body && (method === 'POST' || method === 'PUT') && { body }),
        redirect: followRedirects ? 'follow' : 'manual',
        signal: controller.signal,
      };

      // Fetch the URL
      const response = await fetch(url, fetchOptions);

      // Clear timeout
      clearTimeout(timeoutId);

      // Check response size
      const contentLength = response.headers.get('content-length');
      if (contentLength && parseInt(contentLength) > maxSize) {
        return this.error(`Response too large: ${contentLength} bytes (max: ${maxSize} bytes)`);
      }

      // Get response content
      const contentType = response.headers.get('content-type') || '';
      let content: string;

      if (contentType.includes('application/json')) {
        const json = await response.json();
        content = JSON.stringify(json, null, 2);
      } else {
        content = await response.text();
        
        // Check actual size after reading
        if (content.length > maxSize) {
          return this.error(`Response too large: ${content.length} bytes (max: ${maxSize} bytes)`);
        }

        // Extract text from HTML if requested
        if (extractText && contentType.includes('text/html')) {
          content = this.extractTextFromHtml(content);
        }
      }

      // Format response
      const responseInfo = this.formatResponse(response, content, url);

      return this.success({
        type: 'text',
        content: responseInfo,
        metadata: {
          url,
          method,
          status: response.status,
          statusText: response.statusText,
          contentType,
          contentLength: content.length,
          headers: this.headersToObject(response.headers),
          redirected: response.redirected,
          finalUrl: response.url,
        },
      });
    } catch (error) {
      const { timeout: timeoutValue } = await this.validateAndParse<WebFetchParams>(params);
      if (error instanceof Error && error.name === 'AbortError') {
        return this.error(`Request timeout: ${timeoutValue}ms exceeded`);
      }
      return this.error(this.handleError(error));
    }
  }

  public getHelp(): ToolHelp {
    return {
      description: 'Fetch content from a web URL with support for various HTTP methods and content types',
      parameters: {
        url: {
          type: 'string',
          description: 'URL to fetch (must be a valid HTTP/HTTPS URL)',
          required: true,
        },
        method: {
          type: 'string',
          description: 'HTTP method to use (GET, POST, PUT, DELETE, HEAD)',
          required: false,
          default: 'GET',
        },
        headers: {
          type: 'object',
          description: 'HTTP headers to send as key-value pairs',
          required: false,
        },
        body: {
          type: 'string',
          description: 'Request body for POST/PUT requests',
          required: false,
        },
        timeout: {
          type: 'number',
          description: 'Request timeout in milliseconds',
          required: false,
          default: 30000,
        },
        followRedirects: {
          type: 'boolean',
          description: 'Whether to follow HTTP redirects',
          required: false,
          default: true,
        },
        extractText: {
          type: 'boolean',
          description: 'Extract plain text from HTML content',
          required: false,
          default: true,
        },
        maxSize: {
          type: 'number',
          description: 'Maximum response size in bytes',
          required: false,
          default: 5242880,
        },
      },
      examples: [
        {
          description: 'Fetch a web page',
          parameters: { 
            url: 'https://example.com' 
          },
        },
        {
          description: 'Fetch JSON API data',
          parameters: { 
            url: 'https://api.example.com/data',
            headers: { 'Accept': 'application/json' }
          },
        },
        {
          description: 'POST data to an API',
          parameters: { 
            url: 'https://api.example.com/submit',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: '{"key": "value"}'
          },
        },
        {
          description: 'Fetch with custom timeout and no text extraction',
          parameters: { 
            url: 'https://example.com/large-page',
            timeout: 60000,
            extractText: false
          },
        },
      ],
    };
  }

  private extractTextFromHtml(html: string): string {
    try {
      return htmlToText(html, {
        wordwrap: 80,
        selectors: [
          { selector: 'a', options: { ignoreHref: true } },
          { selector: 'img', format: 'skip' },
          { selector: 'script', format: 'skip' },
          { selector: 'style', format: 'skip' },
        ],
      });
    } catch {
      // Fallback: simple HTML tag removal
      return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }
  }

  private formatResponse(response: Response, content: string, originalUrl: string): string {
    const lines = [`Web Fetch Result\n`];

    lines.push(`URL: ${originalUrl}`);
    if (response.redirected) {
      lines.push(`Final URL: ${response.url}`);
    }
    lines.push(`Status: ${response.status} ${response.statusText}`);
    lines.push(`Content-Type: ${response.headers.get('content-type') || 'unknown'}`);
    lines.push(`Content-Length: ${content.length} bytes`);
    lines.push('');

    // Add important headers
    const importantHeaders = ['date', 'server', 'cache-control', 'etag', 'last-modified'];
    for (const header of importantHeaders) {
      const value = response.headers.get(header);
      if (value) {
        lines.push(`${header.charAt(0).toUpperCase() + header.slice(1)}: ${value}`);
      }
    }

    lines.push('\n--- Content ---\n');
    lines.push(content);

    return lines.join('\n');
  }

  private headersToObject(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }
}
