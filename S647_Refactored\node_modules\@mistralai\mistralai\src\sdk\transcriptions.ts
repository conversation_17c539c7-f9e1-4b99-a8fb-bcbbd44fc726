/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { audioTranscriptionsComplete } from "../funcs/audioTranscriptionsComplete.js";
import { audioTranscriptionsStream } from "../funcs/audioTranscriptionsStream.js";
import { EventStream } from "../lib/event-streams.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { unwrapAsync } from "../types/fp.js";

export class Transcriptions extends ClientSDK {
  /**
   * Create Transcription
   */
  async complete(
    request: components.AudioTranscriptionRequest,
    options?: RequestOptions,
  ): Promise<components.TranscriptionResponse> {
    return unwrapAsync(audioTranscriptionsComplete(
      this,
      request,
      options,
    ));
  }

  /**
   * Create streaming transcription (SSE)
   */
  async stream(
    request: components.AudioTranscriptionRequestStream,
    options?: RequestOptions,
  ): Promise<EventStream<components.TranscriptionStreamEvents>> {
    return unwrapAsync(audioTranscriptionsStream(
      this,
      request,
      options,
    ));
  }
}
