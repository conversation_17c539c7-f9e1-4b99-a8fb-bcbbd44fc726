/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { HistoryItem, MessageType } from './useStreaming.js';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
/**
 * History manager interface
 */
export interface UseHistoryManagerReturn {
    history: HistoryItem[];
    addItem: (item: HistoryItem) => void;
    updateItem: (id: string, updates: Partial<HistoryItem>) => void;
    removeItem: (id: string) => void;
    clearHistory: () => void;
    getLastUserMessages: (count: number) => string[];
    saveHistory: () => Promise<void>;
    loadHistory: () => Promise<void>;
}
/**
 * History manager hook
 */
export declare function useHistory(config: Configuration, logger: Logger): UseHistoryManagerReturn;
/**
 * Format history item for display
 */
export declare function formatHistoryItem(item: HistoryItem): string;
/**
 * Get message prefix based on type
 */
export declare function getMessagePrefix(type: MessageType): string;
/**
 * Get message color based on type
 */
export declare function getMessageColor(type: MessageType): string;
/**
 * Filter history by type
 */
export declare function filterHistoryByType(history: HistoryItem[], type: MessageType): HistoryItem[];
/**
 * Get conversation context for AI
 */
export declare function getConversationContext(history: HistoryItem[], maxTokens?: number): HistoryItem[];
/**
 * Export history to text format
 */
export declare function exportHistoryToText(history: HistoryItem[]): string;
/**
 * Get history statistics
 */
export declare function getHistoryStats(history: HistoryItem[]): {
    providers: string[];
    total: number;
    user: number;
    assistant: number;
    system: number;
    tool: number;
    error: number;
    totalChars: number;
    estimatedTokens: number;
};
//# sourceMappingURL=useHistory.d.ts.map