/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FTModelCapabilitiesOut = {
  completionChat?: boolean | undefined;
  completionFim?: boolean | undefined;
  functionCalling?: boolean | undefined;
  fineTuning?: boolean | undefined;
  classification?: boolean | undefined;
};

/** @internal */
export const FTModelCapabilitiesOut$inboundSchema: z.ZodType<
  FTModelCapabilitiesOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  completion_chat: z.boolean().default(true),
  completion_fim: z.boolean().default(false),
  function_calling: z.boolean().default(false),
  fine_tuning: z.boolean().default(false),
  classification: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    "completion_chat": "completionChat",
    "completion_fim": "completionFim",
    "function_calling": "functionCalling",
    "fine_tuning": "fineTuning",
  });
});

/** @internal */
export type FTModelCapabilitiesOut$Outbound = {
  completion_chat: boolean;
  completion_fim: boolean;
  function_calling: boolean;
  fine_tuning: boolean;
  classification: boolean;
};

/** @internal */
export const FTModelCapabilitiesOut$outboundSchema: z.ZodType<
  FTModelCapabilitiesOut$Outbound,
  z.ZodTypeDef,
  FTModelCapabilitiesOut
> = z.object({
  completionChat: z.boolean().default(true),
  completionFim: z.boolean().default(false),
  functionCalling: z.boolean().default(false),
  fineTuning: z.boolean().default(false),
  classification: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    completionChat: "completion_chat",
    completionFim: "completion_fim",
    functionCalling: "function_calling",
    fineTuning: "fine_tuning",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FTModelCapabilitiesOut$ {
  /** @deprecated use `FTModelCapabilitiesOut$inboundSchema` instead. */
  export const inboundSchema = FTModelCapabilitiesOut$inboundSchema;
  /** @deprecated use `FTModelCapabilitiesOut$outboundSchema` instead. */
  export const outboundSchema = FTModelCapabilitiesOut$outboundSchema;
  /** @deprecated use `FTModelCapabilitiesOut$Outbound` instead. */
  export type Outbound = FTModelCapabilitiesOut$Outbound;
}

export function ftModelCapabilitiesOutToJSON(
  ftModelCapabilitiesOut: FTModelCapabilitiesOut,
): string {
  return JSON.stringify(
    FTModelCapabilitiesOut$outboundSchema.parse(ftModelCapabilitiesOut),
  );
}

export function ftModelCapabilitiesOutFromJSON(
  jsonString: string,
): SafeParseResult<FTModelCapabilitiesOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FTModelCapabilitiesOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FTModelCapabilitiesOut' from JSON`,
  );
}
