/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Default logging configuration
 */
export const DEFAULT_LOGGING = {
  level: 'info' as const,
  format: 'text' as const,
  output: 'console' as const,
  structured: false,
  includeTimestamp: true,
  includeLevel: true,
  includeSource: false,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
  datePattern: 'YYYY-MM-DD',
};

/**
 * Logging configurations for different environments
 */
export const LOGGING_CONFIGS = {
  development: {
    ...DEFAULT_LOGGING,
    level: 'debug' as const,
    includeSource: true,
    structured: false,
  },
  
  production: {
    ...DEFAULT_LOGGING,
    level: 'warn' as const,
    format: 'json' as const,
    structured: true,
    includeSource: false,
  },
  
  test: {
    ...DEFAULT_LOGGING,
    level: 'error' as const,
    output: 'file' as const,
    structured: false,
  },
};

/**
 * Get logging configuration for environment
 */
export function getLoggingConfigForEnvironment(env: 'development' | 'production' | 'test') {
  return LOGGING_CONFIGS[env] ?? DEFAULT_LOGGING;
}

/**
 * Log level priorities
 */
export const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
} as const;

/**
 * Check if log level should be logged
 */
export function shouldLog(currentLevel: keyof typeof LOG_LEVELS, messageLevel: keyof typeof LOG_LEVELS): boolean {
  return LOG_LEVELS[messageLevel] >= LOG_LEVELS[currentLevel];
}
