/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { fineTuningJobsCancel } from "../funcs/fineTuningJobsCancel.js";
import { fineTuningJobsCreate } from "../funcs/fineTuningJobsCreate.js";
import { fineTuningJobsGet } from "../funcs/fineTuningJobsGet.js";
import { fineTuningJobsList } from "../funcs/fineTuningJobsList.js";
import { fineTuningJobsStart } from "../funcs/fineTuningJobsStart.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { unwrapAsync } from "../types/fp.js";

export class Jobs extends ClientSDK {
  /**
   * Get Fine Tuning Jobs
   *
   * @remarks
   * Get a list of fine-tuning jobs for your organization and user.
   */
  async list(
    request?:
      | operations.JobsApiRoutesFineTuningGetFineTuningJobsRequest
      | undefined,
    options?: RequestOptions,
  ): Promise<components.JobsOut> {
    return unwrapAsync(fineTuningJobsList(
      this,
      request,
      options,
    ));
  }

  /**
   * Create Fine Tuning Job
   *
   * @remarks
   * Create a new fine-tuning job, it will be queued for processing.
   */
  async create(
    request: components.JobIn,
    options?: RequestOptions,
  ): Promise<operations.JobsApiRoutesFineTuningCreateFineTuningJobResponse> {
    return unwrapAsync(fineTuningJobsCreate(
      this,
      request,
      options,
    ));
  }

  /**
   * Get Fine Tuning Job
   *
   * @remarks
   * Get a fine-tuned job details by its UUID.
   */
  async get(
    request: operations.JobsApiRoutesFineTuningGetFineTuningJobRequest,
    options?: RequestOptions,
  ): Promise<operations.JobsApiRoutesFineTuningGetFineTuningJobResponse> {
    return unwrapAsync(fineTuningJobsGet(
      this,
      request,
      options,
    ));
  }

  /**
   * Cancel Fine Tuning Job
   *
   * @remarks
   * Request the cancellation of a fine tuning job.
   */
  async cancel(
    request: operations.JobsApiRoutesFineTuningCancelFineTuningJobRequest,
    options?: RequestOptions,
  ): Promise<operations.JobsApiRoutesFineTuningCancelFineTuningJobResponse> {
    return unwrapAsync(fineTuningJobsCancel(
      this,
      request,
      options,
    ));
  }

  /**
   * Start Fine Tuning Job
   *
   * @remarks
   * Request the start of a validated fine tuning job.
   */
  async start(
    request: operations.JobsApiRoutesFineTuningStartFineTuningJobRequest,
    options?: RequestOptions,
  ): Promise<operations.JobsApiRoutesFineTuningStartFineTuningJobResponse> {
    return unwrapAsync(fineTuningJobsStart(
      this,
      request,
      options,
    ));
  }
}
