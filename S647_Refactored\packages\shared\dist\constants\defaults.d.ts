/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default configuration values
 */
export declare const DEFAULT_CONFIG: {
    readonly version: "2.0.0";
    readonly environment: "production";
    readonly defaultProvider: "openai";
    readonly providers: {
        readonly default: {
            readonly type: "openai";
            readonly enabled: true;
            readonly timeout: 30000;
            readonly retries: 3;
        };
        readonly openai: {
            readonly type: "openai";
            readonly enabled: true;
            readonly timeout: 30000;
            readonly retries: 3;
        };
        readonly anthropic: {
            readonly type: "anthropic";
            readonly enabled: true;
            readonly timeout: 30000;
            readonly retries: 3;
        };
        readonly google: {
            readonly type: "google";
            readonly enabled: true;
            readonly timeout: 30000;
            readonly retries: 3;
        };
    };
    readonly tools: {
        readonly enabled: readonly ["file", "git", "web", "shell"];
        readonly file: {
            readonly enabled: true;
            readonly timeout: 10000;
            readonly retries: 2;
        };
        readonly git: {
            readonly enabled: true;
            readonly timeout: 15000;
            readonly retries: 2;
        };
        readonly web: {
            readonly enabled: true;
            readonly timeout: 30000;
            readonly retries: 3;
        };
        readonly shell: {
            readonly enabled: true;
            readonly timeout: 60000;
            readonly retries: 1;
        };
    };
    readonly ui: {
        readonly theme: "dark";
        readonly animations: true;
        readonly verbose: false;
        readonly showTimestamps: true;
        readonly showTokenCount: true;
        readonly autoScroll: true;
        readonly maxHistoryLines: 1000;
        readonly colors: {
            readonly primary: "#00d4ff";
            readonly secondary: "#ff6b6b";
            readonly success: "#51cf66";
            readonly warning: "#ffd43b";
            readonly error: "#ff6b6b";
            readonly info: "#74c0fc";
        };
    };
    readonly logging: {
        readonly level: "info";
        readonly format: "text";
        readonly output: "console";
        readonly structured: false;
        readonly includeTimestamp: true;
        readonly includeLevel: true;
        readonly includeSource: false;
    };
    readonly telemetry: {
        readonly enabled: false;
        readonly collectUsage: false;
        readonly collectErrors: false;
        readonly collectPerformance: false;
        readonly anonymize: true;
    };
    readonly security: {
        readonly sandbox: {
            readonly enabled: false;
            readonly type: "docker";
            readonly allowNetworking: true;
            readonly allowFileSystem: true;
        };
        readonly encryption: {
            readonly enabled: false;
            readonly algorithm: "aes-256-gcm";
            readonly keyDerivation: "pbkdf2";
        };
        readonly authentication: {
            readonly required: false;
            readonly methods: readonly ["api-key"];
            readonly tokenExpiry: 3600;
        };
    };
    readonly performance: {
        readonly maxConcurrentRequests: 10;
        readonly requestTimeout: 30000;
        readonly retryAttempts: 3;
        readonly retryDelay: 1000;
        readonly caching: {
            readonly enabled: true;
            readonly ttl: 300;
            readonly maxSize: 100;
        };
        readonly rateLimit: {
            readonly enabled: false;
            readonly requestsPerMinute: 60;
            readonly burstLimit: 10;
        };
    };
};
//# sourceMappingURL=defaults.d.ts.map