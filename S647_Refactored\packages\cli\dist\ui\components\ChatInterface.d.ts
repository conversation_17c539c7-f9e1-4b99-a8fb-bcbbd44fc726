/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
/**
 * Chat interface props
 */
export interface ChatInterfaceProps {
    config: Configuration;
    logger: Logger;
    onExit: () => void;
}
/**
 * Main chat interface component
 */
export declare const ChatInterface: React.FC<ChatInterfaceProps>;
//# sourceMappingURL=ChatInterface.d.ts.map