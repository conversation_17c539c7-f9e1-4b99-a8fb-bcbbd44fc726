/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FilesApiRoutesDeleteFileRequest = {
  fileId: string;
};

/** @internal */
export const FilesApiRoutesDeleteFileRequest$inboundSchema: z.ZodType<
  FilesApiRoutesDeleteFileRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  file_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "file_id": "fileId",
  });
});

/** @internal */
export type FilesApiRoutesDeleteFileRequest$Outbound = {
  file_id: string;
};

/** @internal */
export const FilesApiRoutesDeleteFileRequest$outboundSchema: z.ZodType<
  FilesApiRoutesDeleteFileRequest$Outbound,
  z.ZodTypeDef,
  FilesApiRoutesDeleteFileRequest
> = z.object({
  fileId: z.string(),
}).transform((v) => {
  return remap$(v, {
    fileId: "file_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilesApiRoutesDeleteFileRequest$ {
  /** @deprecated use `FilesApiRoutesDeleteFileRequest$inboundSchema` instead. */
  export const inboundSchema = FilesApiRoutesDeleteFileRequest$inboundSchema;
  /** @deprecated use `FilesApiRoutesDeleteFileRequest$outboundSchema` instead. */
  export const outboundSchema = FilesApiRoutesDeleteFileRequest$outboundSchema;
  /** @deprecated use `FilesApiRoutesDeleteFileRequest$Outbound` instead. */
  export type Outbound = FilesApiRoutesDeleteFileRequest$Outbound;
}

export function filesApiRoutesDeleteFileRequestToJSON(
  filesApiRoutesDeleteFileRequest: FilesApiRoutesDeleteFileRequest,
): string {
  return JSON.stringify(
    FilesApiRoutesDeleteFileRequest$outboundSchema.parse(
      filesApiRoutesDeleteFileRequest,
    ),
  );
}

export function filesApiRoutesDeleteFileRequestFromJSON(
  jsonString: string,
): SafeParseResult<FilesApiRoutesDeleteFileRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FilesApiRoutesDeleteFileRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FilesApiRoutesDeleteFileRequest' from JSON`,
  );
}
