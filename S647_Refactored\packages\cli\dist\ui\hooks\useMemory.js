/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useCallback, useEffect } from 'react';
/**
 * Memory manager hook
 */
export function useMemory(config, logger) {
    const [memories, setMemories] = useState(new Map());
    const [isLoading, setIsLoading] = useState(false);
    const [lastCleanup, setLastCleanup] = useState(new Date());
    // Get memory configuration
    const getMemoryConfig = useCallback(() => {
        return {
            maxEntries: config.tools.memory?.maxSize || 100,
            maxSize: 10 * 1024 * 1024, // 10MB
            ttl: config.tools.memory?.ttl || 3600, // 1 hour
            autoCleanup: true,
            compressionThreshold: 1000, // entries
        };
    }, [config.tools.memory]);
    // Generate unique ID
    const generateId = useCallback(() => {
        return `mem_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }, []);
    // Extract tags from content
    const extractTags = useCallback((content, context) => {
        const tags = new Set();
        // Extract hashtags
        const hashtagMatches = content.match(/#\w+/g);
        if (hashtagMatches) {
            hashtagMatches.forEach(tag => tags.add(tag.toLowerCase()));
        }
        // Extract keywords based on context
        const keywords = [
            // Programming languages
            'javascript', 'typescript', 'python', 'java', 'cpp', 'rust', 'go',
            // Frameworks
            'react', 'vue', 'angular', 'node', 'express', 'fastapi', 'django',
            // Tools
            'git', 'docker', 'kubernetes', 'aws', 'azure', 'gcp',
            // Concepts
            'api', 'database', 'authentication', 'security', 'performance', 'testing',
        ];
        const lowerContent = content.toLowerCase();
        const lowerContext = context.toLowerCase();
        keywords.forEach(keyword => {
            if (lowerContent.includes(keyword) || lowerContext.includes(keyword)) {
                tags.add(keyword);
            }
        });
        // Add context-based tags
        if (context.includes('file:')) {
            tags.add('file');
        }
        if (context.includes('error')) {
            tags.add('error');
        }
        if (context.includes('solution')) {
            tags.add('solution');
        }
        return Array.from(tags);
    }, []);
    // Calculate importance score
    const calculateImportance = useCallback((content, context, source) => {
        let importance = 5; // base importance
        // Adjust based on content length
        if (content.length > 500)
            importance += 1;
        if (content.length > 1000)
            importance += 1;
        // Adjust based on source
        switch (source) {
            case 'user':
                importance += 2; // User input is important
                break;
            case 'ai':
                importance += 1; // AI responses are moderately important
                break;
            case 'file':
                importance += 3; // File content is very important
                break;
            case 'system':
                importance -= 1; // System messages are less important
                break;
        }
        // Adjust based on keywords
        const importantKeywords = ['error', 'solution', 'important', 'critical', 'bug', 'fix'];
        const lowerContent = content.toLowerCase();
        importantKeywords.forEach(keyword => {
            if (lowerContent.includes(keyword)) {
                importance += 1;
            }
        });
        // Clamp to 1-10 range
        return Math.max(1, Math.min(10, importance));
    }, []);
    // Add memory entry
    const addMemory = useCallback(async (content, context, source = 'user', metadata) => {
        try {
            const memoryConfig = getMemoryConfig();
            // Check if memory is enabled
            if (!config.tools.memory?.enabled) {
                logger.debug('Memory is disabled, skipping add');
                return '';
            }
            // Generate entry
            const id = generateId();
            const tags = extractTags(content, context);
            const importance = calculateImportance(content, context, source);
            const now = new Date();
            const entry = {
                id,
                content,
                context,
                importance,
                timestamp: now,
                lastAccessed: now,
                accessCount: 1,
                tags,
                source,
                metadata: metadata || {},
            };
            // Add to memories
            setMemories(prev => {
                const newMemories = new Map(prev);
                newMemories.set(id, entry);
                // Check if we need to cleanup
                if (newMemories.size > memoryConfig.maxEntries) {
                    // Remove least important and oldest entries
                    const entries = Array.from(newMemories.values());
                    entries.sort((a, b) => {
                        // Sort by importance (desc) then by last accessed (desc)
                        if (a.importance !== b.importance) {
                            return b.importance - a.importance;
                        }
                        return b.lastAccessed.getTime() - a.lastAccessed.getTime();
                    });
                    // Keep only the top entries
                    const toKeep = entries.slice(0, memoryConfig.maxEntries - 1);
                    const newMap = new Map();
                    toKeep.forEach(entry => newMap.set(entry.id, entry));
                    newMap.set(id, entry);
                    logger.debug(`Memory cleanup: removed ${entries.length - toKeep.length} entries`);
                    return newMap;
                }
                return newMemories;
            });
            logger.debug(`Added memory entry: ${id} (importance: ${importance})`);
            return id;
        }
        catch (error) {
            logger.error('Failed to add memory:', error);
            return '';
        }
    }, [config.tools.memory?.enabled, getMemoryConfig, generateId, extractTags, calculateImportance, logger]);
    // Search memories
    const searchMemories = useCallback((query, maxResults = 10, minRelevance = 0.3) => {
        if (!query.trim())
            return [];
        const results = [];
        const lowerQuery = query.toLowerCase();
        const queryWords = lowerQuery.split(/\s+/).filter(word => word.length > 2);
        for (const entry of memories.values()) {
            let relevanceScore = 0;
            const matchedTags = [];
            // Check content match
            const lowerContent = entry.content.toLowerCase();
            const lowerContext = entry.context.toLowerCase();
            queryWords.forEach(word => {
                if (lowerContent.includes(word)) {
                    relevanceScore += 0.3;
                }
                if (lowerContext.includes(word)) {
                    relevanceScore += 0.2;
                }
            });
            // Check tag matches
            entry.tags.forEach(tag => {
                if (lowerQuery.includes(tag.replace('#', ''))) {
                    relevanceScore += 0.4;
                    matchedTags.push(tag);
                }
            });
            // Boost score based on importance and access count
            relevanceScore *= (entry.importance / 10);
            relevanceScore *= Math.log(entry.accessCount + 1);
            // Decay score based on age
            const ageInDays = (Date.now() - entry.timestamp.getTime()) / (1000 * 60 * 60 * 24);
            relevanceScore *= Math.exp(-ageInDays / 30); // Decay over 30 days
            if (relevanceScore >= minRelevance) {
                // Generate snippet
                const snippet = entry.content.length > 100
                    ? entry.content.substring(0, 100) + '...'
                    : entry.content;
                results.push({
                    entry,
                    relevanceScore,
                    matchedTags,
                    snippet,
                });
                // Update access count and last accessed
                entry.lastAccessed = new Date();
                entry.accessCount++;
            }
        }
        // Sort by relevance score
        results.sort((a, b) => b.relevanceScore - a.relevanceScore);
        return results.slice(0, maxResults);
    }, [memories]);
    // Get relevant memories for context
    const getRelevantMemories = useCallback((historyItems, maxMemories = 5) => {
        if (historyItems.length === 0)
            return [];
        // Extract keywords from recent conversation
        const recentContent = historyItems
            .slice(-5) // Last 5 messages
            .map(item => item.content)
            .join(' ');
        const searchResults = searchMemories(recentContent, maxMemories, 0.2);
        return searchResults.map(result => result.entry);
    }, [searchMemories]);
    // Remove memory
    const removeMemory = useCallback((id) => {
        setMemories(prev => {
            const newMemories = new Map(prev);
            const removed = newMemories.delete(id);
            if (removed) {
                logger.debug(`Removed memory entry: ${id}`);
            }
            return newMemories;
        });
        return true;
    }, [logger]);
    // Clear all memories
    const clearMemories = useCallback(() => {
        setMemories(new Map());
        logger.info('All memories cleared');
    }, [logger]);
    // Get memory statistics
    const getMemoryStats = useCallback(() => {
        const entries = Array.from(memories.values());
        if (entries.length === 0) {
            return {
                totalEntries: 0,
                totalSize: 0,
                averageImportance: 0,
                tagDistribution: {},
            };
        }
        const totalSize = entries.reduce((sum, entry) => sum + entry.content.length + entry.context.length, 0);
        const averageImportance = entries.reduce((sum, entry) => sum + entry.importance, 0) / entries.length;
        const mostAccessedEntry = entries.reduce((max, entry) => entry.accessCount > max.accessCount ? entry : max);
        const sortedByDate = [...entries].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        const tagDistribution = {};
        entries.forEach(entry => {
            entry.tags.forEach(tag => {
                tagDistribution[tag] = (tagDistribution[tag] || 0) + 1;
            });
        });
        return {
            totalEntries: entries.length,
            totalSize,
            averageImportance,
            mostAccessedEntry,
            oldestEntry: sortedByDate[0],
            newestEntry: sortedByDate[sortedByDate.length - 1],
            tagDistribution,
        };
    }, [memories]);
    // Auto-cleanup old memories
    const performCleanup = useCallback(() => {
        const memoryConfig = getMemoryConfig();
        const now = new Date();
        const cutoffTime = now.getTime() - (memoryConfig.ttl * 1000);
        setMemories(prev => {
            const newMemories = new Map(prev);
            let removedCount = 0;
            for (const [id, entry] of newMemories) {
                // Remove entries that are too old and have low importance
                if (entry.lastAccessed.getTime() < cutoffTime && entry.importance < 5) {
                    newMemories.delete(id);
                    removedCount++;
                }
            }
            if (removedCount > 0) {
                logger.debug(`Auto-cleanup removed ${removedCount} old memories`);
            }
            return newMemories;
        });
        setLastCleanup(now);
    }, [getMemoryConfig, logger]);
    // Save memories to file
    const saveMemories = useCallback(async () => {
        try {
            const memoriesData = {
                version: '2.0.0',
                timestamp: new Date().toISOString(),
                entries: Array.from(memories.entries()),
            };
            // In a real implementation, this would save to a file
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('s647-memories', JSON.stringify(memoriesData));
                logger.debug('Memories saved to localStorage');
            }
        }
        catch (error) {
            logger.error('Failed to save memories:', error);
        }
    }, [memories, logger]);
    // Load memories from file
    const loadMemories = useCallback(async () => {
        try {
            setIsLoading(true);
            // In a real implementation, this would load from a file
            if (typeof localStorage !== 'undefined') {
                const stored = localStorage.getItem('s647-memories');
                if (stored) {
                    const memoriesData = JSON.parse(stored);
                    if (memoriesData.version === '2.0.0' && Array.isArray(memoriesData.entries)) {
                        const loadedMemories = new Map();
                        memoriesData.entries.forEach(([id, entry]) => {
                            // Convert date strings back to Date objects
                            entry.timestamp = new Date(entry.timestamp);
                            entry.lastAccessed = new Date(entry.lastAccessed);
                            loadedMemories.set(id, entry);
                        });
                        setMemories(loadedMemories);
                        logger.debug(`Loaded ${loadedMemories.size} memories`);
                    }
                }
            }
        }
        catch (error) {
            logger.error('Failed to load memories:', error);
        }
        finally {
            setIsLoading(false);
        }
    }, [logger]);
    // Auto-save memories periodically
    useEffect(() => {
        const interval = setInterval(() => {
            if (memories.size > 0) {
                saveMemories();
            }
        }, 60000); // Save every minute
        return () => clearInterval(interval);
    }, [memories, saveMemories]);
    // Auto-cleanup periodically
    useEffect(() => {
        const interval = setInterval(() => {
            const timeSinceLastCleanup = Date.now() - lastCleanup.getTime();
            if (timeSinceLastCleanup > 300000) { // 5 minutes
                performCleanup();
            }
        }, 300000); // Check every 5 minutes
        return () => clearInterval(interval);
    }, [lastCleanup, performCleanup]);
    // Load memories on mount
    useEffect(() => {
        if (config.tools.memory?.enabled) {
            loadMemories();
        }
    }, [config.tools.memory?.enabled, loadMemories]);
    return {
        memories: Array.from(memories.values()),
        addMemory,
        searchMemories,
        getRelevantMemories,
        removeMemory,
        clearMemories,
        getMemoryStats,
        saveMemories,
        loadMemories,
        performCleanup,
        isLoading,
    };
}
//# sourceMappingURL=useMemory.js.map