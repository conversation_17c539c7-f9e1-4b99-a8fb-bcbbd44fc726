/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { ocrProcess } from "../funcs/ocrProcess.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import { unwrapAsync } from "../types/fp.js";

export class Ocr extends ClientSDK {
  /**
   * OCR
   */
  async process(
    request: components.OCRRequest,
    options?: RequestOptions,
  ): Promise<components.OCRResponse> {
    return unwrapAsync(ocrProcess(
      this,
      request,
      options,
    ));
  }
}
