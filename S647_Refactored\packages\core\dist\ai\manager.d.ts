/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Provider, ProviderConfig, ProviderType, ProviderId, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, Logger } from '@inkbytefo/s647-shared';
/**
 * AI Manager - Central coordinator for AI providers
 */
export declare class AIManager {
    private factory;
    private registry;
    private defaultProviderId?;
    private logger;
    constructor(logger?: Logger);
    /**
     * Initialize the AI manager with provider configurations
     */
    initialize(configs: Record<string, ProviderConfig>, defaultProvider?: string): AsyncResult<void>;
    /**
     * Get the default provider
     */
    getDefaultProvider(): Provider | undefined;
    /**
     * Get a provider by ID
     */
    getProvider(id: ProviderId): Provider | undefined;
    /**
     * Get all available providers
     */
    getAvailableProviders(): Provider[];
    /**
     * Get providers by type
     */
    getProvidersByType(type: ProviderType): Provider[];
    /**
     * Find the best provider for a model
     */
    findBestProvider(modelId: string): Provider | undefined;
    /**
     * Create a chat completion using the best available provider
     */
    createChatCompletion(request: ChatCompletionRequest, providerId?: ProviderId): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion using the best available provider
     */
    createChatCompletionStream(request: ChatCompletionRequest, providerId?: ProviderId): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings using the best available provider
     */
    createEmbedding(request: EmbeddingRequest, providerId?: ProviderId): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[], modelId?: string, providerId?: ProviderId): AsyncResult<number>;
    /**
     * Get available models from all providers
     */
    getAvailableModels(): AsyncResult<ModelInfo[]>;
    /**
     * Get provider statistics
     */
    getStats(): {
        total: number;
        available: number;
        byType: Record<ProviderType, number>;
    };
    /**
     * Dispose of all providers
     */
    dispose(): Promise<void>;
    /**
     * Select the best provider for a request
     */
    private selectProvider;
}
//# sourceMappingURL=manager.d.ts.map