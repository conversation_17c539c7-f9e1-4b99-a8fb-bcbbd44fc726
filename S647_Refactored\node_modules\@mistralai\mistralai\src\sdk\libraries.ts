/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { betaLibrariesCreate } from "../funcs/betaLibrariesCreate.js";
import { betaLibrariesDelete } from "../funcs/betaLibrariesDelete.js";
import { betaLibrariesGet } from "../funcs/betaLibrariesGet.js";
import { betaLibrariesList } from "../funcs/betaLibrariesList.js";
import { betaLibrariesUpdate } from "../funcs/betaLibrariesUpdate.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { unwrapAsync } from "../types/fp.js";
import { Accesses } from "./accesses.js";
import { Documents } from "./documents.js";

export class Libraries extends ClientSDK {
  private _documents?: Documents;
  get documents(): Documents {
    return (this._documents ??= new Documents(this._options));
  }

  private _accesses?: Accesses;
  get accesses(): Accesses {
    return (this._accesses ??= new Accesses(this._options));
  }

  /**
   * List all libraries you have access to.
   *
   * @remarks
   * List all libraries that you have created or have been shared with you.
   */
  async list(
    options?: RequestOptions,
  ): Promise<components.ListLibraryOut> {
    return unwrapAsync(betaLibrariesList(
      this,
      options,
    ));
  }

  /**
   * Create a new Library.
   *
   * @remarks
   * Create a new Library, you will be marked as the owner and only you will have the possibility to share it with others. When first created this will only be accessible by you.
   */
  async create(
    request: components.LibraryIn,
    options?: RequestOptions,
  ): Promise<components.LibraryOut> {
    return unwrapAsync(betaLibrariesCreate(
      this,
      request,
      options,
    ));
  }

  /**
   * Detailed information about a specific Library.
   *
   * @remarks
   * Given a library id, details information about that Library.
   */
  async get(
    request: operations.LibrariesGetV1Request,
    options?: RequestOptions,
  ): Promise<components.LibraryOut> {
    return unwrapAsync(betaLibrariesGet(
      this,
      request,
      options,
    ));
  }

  /**
   * Delete a library and all of it's document.
   *
   * @remarks
   * Given a library id, deletes it together with all documents that have been uploaded to that library.
   */
  async delete(
    request: operations.LibrariesDeleteV1Request,
    options?: RequestOptions,
  ): Promise<components.LibraryOut> {
    return unwrapAsync(betaLibrariesDelete(
      this,
      request,
      options,
    ));
  }

  /**
   * Update a library.
   *
   * @remarks
   * Given a library id, you can update the name and description.
   */
  async update(
    request: operations.LibrariesUpdateV1Request,
    options?: RequestOptions,
  ): Promise<components.LibraryOut> {
    return unwrapAsync(betaLibrariesUpdate(
      this,
      request,
      options,
    ));
  }
}
