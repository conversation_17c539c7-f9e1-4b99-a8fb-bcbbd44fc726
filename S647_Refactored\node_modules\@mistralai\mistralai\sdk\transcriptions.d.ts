import { EventStream } from "../lib/event-streams.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
export declare class Transcriptions extends ClientSDK {
    /**
     * Create Transcription
     */
    complete(request: components.AudioTranscriptionRequest, options?: RequestOptions): Promise<components.TranscriptionResponse>;
    /**
     * Create streaming transcription (SSE)
     */
    stream(request: components.AudioTranscriptionRequestStream, options?: RequestOptions): Promise<EventStream<components.TranscriptionStreamEvents>>;
}
//# sourceMappingURL=transcriptions.d.ts.map