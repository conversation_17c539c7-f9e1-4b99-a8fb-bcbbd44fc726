/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConfigLoader, ConfigLoaderOptions, LoadResult } from './types.js';
/**
 * File-based configuration loader
 * Loads configuration from JSON files
 */
export declare class FileConfigLoader implements ConfigLoader {
    readonly name = "file";
    readonly priority = 50;
    private readonly configPaths;
    constructor(configPaths?: string[]);
    /**
     * Get default configuration file paths in order of priority
     */
    private getDefaultConfigPaths;
    /**
     * Load configuration from files
     */
    load(options?: ConfigLoaderOptions): Promise<LoadResult>;
    /**
     * Check if this loader can load configuration
     */
    canLoad(options?: ConfigLoaderOptions): Promise<boolean>;
    /**
     * Check if file exists
     */
    private fileExists;
    /**
     * Read and parse configuration file
     */
    private readConfigFile;
}
//# sourceMappingURL=file-loader.d.ts.map