import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
export declare class Models extends ClientSDK {
    /**
     * List Models
     *
     * @remarks
     * List all models available to the user.
     */
    list(options?: RequestOptions): Promise<components.ModelList>;
    /**
     * Retrieve Model
     *
     * @remarks
     * Retrieve information about a model.
     */
    retrieve(request: operations.RetrieveModelV1ModelsModelIdGetRequest, options?: RequestOptions): Promise<operations.RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet>;
    /**
     * Delete Model
     *
     * @remarks
     * Delete a fine-tuned model.
     */
    delete(request: operations.DeleteModelV1ModelsModelIdDeleteRequest, options?: RequestOptions): Promise<components.DeleteModelOut>;
    /**
     * Update Fine Tuned Model
     *
     * @remarks
     * Update a model name or description.
     */
    update(request: operations.JobsApiRoutesFineTuningUpdateFineTunedModelRequest, options?: RequestOptions): Promise<operations.JobsApiRoutesFineTuningUpdateFineTunedModelResponse>;
    /**
     * Archive Fine Tuned Model
     *
     * @remarks
     * Archive a fine-tuned model.
     */
    archive(request: operations.JobsApiRoutesFineTuningArchiveFineTunedModelRequest, options?: RequestOptions): Promise<components.ArchiveFTModelOut>;
    /**
     * Unarchive Fine Tuned Model
     *
     * @remarks
     * Un-archive a fine-tuned model.
     */
    unarchive(request: operations.JobsApiRoutesFineTuningUnarchiveFineTunedModelRequest, options?: RequestOptions): Promise<components.UnarchiveFTModelOut>;
}
//# sourceMappingURL=models.d.ts.map