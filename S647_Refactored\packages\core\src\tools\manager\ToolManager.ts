/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  Tool,
  ToolId,
  ToolParams,
  ToolContext,
  ToolResult,
  AsyncResult,
} from '@inkbytefo/s647-shared';
import { ToolRegistry } from '../registry/registry.js';
import {
  FileReadTool,
  FileWriteTool,
  FileSearchTool,
  GitStatusTool,
  GitCommitTool,
  WebSearchTool,
  WebFetchTool,
  ShellExecuteTool,
} from '../implementations/index.js';

/**
 * Tool manager for registering and executing tools
 */
export class ToolManager {
  private registry: ToolRegistry;

  constructor() {
    this.registry = new ToolRegistry();
    this.registerDefaultTools();
  }

  /**
   * Register a tool
   */
  public registerTool(tool: Tool): void {
    this.registry.register(tool);
  }

  /**
   * Unregister a tool
   */
  public unregisterTool(id: ToolId): void {
    this.registry.unregister(id);
  }

  /**
   * Get a tool by ID
   */
  public getTool(id: ToolId): Tool | undefined {
    return this.registry.get(id);
  }

  /**
   * Get all registered tools
   */
  public getAllTools(): Tool[] {
    return this.registry.getAll();
  }

  /**
   * Get tools by category
   */
  public getToolsByCategory(category: string): Tool[] {
    return this.registry.getByCategory(category);
  }

  /**
   * Search tools by query
   */
  public searchTools(query: string): Tool[] {
    return this.registry.search(query);
  }

  /**
   * Execute a tool
   */
  public async executeTool(
    id: ToolId,
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    const tool = this.getTool(id);
    if (!tool) {
      return {
        success: false,
        error: new Error(`Tool not found: ${id}`),
      };
    }

    try {
      return await tool.execute(params, context);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  }

  /**
   * Validate tool parameters
   */
  public validateToolParams(id: ToolId, params: ToolParams): { valid: boolean; errors: string[] } {
    const tool = this.getTool(id);
    if (!tool) {
      return {
        valid: false,
        errors: [`Tool not found: ${id}`],
      };
    }

    return tool.validate(params);
  }

  /**
   * Get tool help
   */
  public getToolHelp(id: ToolId) {
    const tool = this.getTool(id);
    if (!tool) {
      return undefined;
    }

    return tool.getHelp();
  }

  /**
   * Get tool categories
   */
  public getCategories(): string[] {
    const categories = new Set<string>();
    for (const tool of this.getAllTools()) {
      categories.add(tool.category);
    }
    return Array.from(categories).sort();
  }

  /**
   * Get tools summary
   */
  public getToolsSummary() {
    const tools = this.getAllTools();
    const categories = this.getCategories();

    return {
      totalTools: tools.length,
      categories: categories.map(category => ({
        name: category,
        count: this.getToolsByCategory(category).length,
        tools: this.getToolsByCategory(category).map(tool => ({
          id: tool.id,
          name: tool.name,
          description: tool.description,
          version: tool.version,
        })),
      })),
    };
  }

  /**
   * Clear all tools
   */
  public clear(): void {
    this.registry.clear();
  }

  /**
   * Register default tools
   */
  private registerDefaultTools(): void {
    // File tools
    this.registerTool(new FileReadTool());
    this.registerTool(new FileWriteTool());
    this.registerTool(new FileSearchTool());

    // Git tools
    this.registerTool(new GitStatusTool());
    this.registerTool(new GitCommitTool());

    // Web tools
    this.registerTool(new WebSearchTool());
    this.registerTool(new WebFetchTool());

    // Shell tools
    this.registerTool(new ShellExecuteTool());
  }
}
