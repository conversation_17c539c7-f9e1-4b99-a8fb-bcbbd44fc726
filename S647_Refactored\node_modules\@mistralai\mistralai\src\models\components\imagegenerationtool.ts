/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const ImageGenerationToolType = {
  ImageGeneration: "image_generation",
} as const;
export type ImageGenerationToolType = ClosedEnum<
  typeof ImageGenerationToolType
>;

export type ImageGenerationTool = {
  type?: ImageGenerationToolType | undefined;
};

/** @internal */
export const ImageGenerationToolType$inboundSchema: z.ZodNativeEnum<
  typeof ImageGenerationToolType
> = z.nativeEnum(ImageGenerationToolType);

/** @internal */
export const ImageGenerationToolType$outboundSchema: z.ZodNativeEnum<
  typeof ImageGenerationToolType
> = ImageGenerationToolType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ImageGenerationToolType$ {
  /** @deprecated use `ImageGenerationToolType$inboundSchema` instead. */
  export const inboundSchema = ImageGenerationToolType$inboundSchema;
  /** @deprecated use `ImageGenerationToolType$outboundSchema` instead. */
  export const outboundSchema = ImageGenerationToolType$outboundSchema;
}

/** @internal */
export const ImageGenerationTool$inboundSchema: z.ZodType<
  ImageGenerationTool,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: ImageGenerationToolType$inboundSchema.default("image_generation"),
});

/** @internal */
export type ImageGenerationTool$Outbound = {
  type: string;
};

/** @internal */
export const ImageGenerationTool$outboundSchema: z.ZodType<
  ImageGenerationTool$Outbound,
  z.ZodTypeDef,
  ImageGenerationTool
> = z.object({
  type: ImageGenerationToolType$outboundSchema.default("image_generation"),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ImageGenerationTool$ {
  /** @deprecated use `ImageGenerationTool$inboundSchema` instead. */
  export const inboundSchema = ImageGenerationTool$inboundSchema;
  /** @deprecated use `ImageGenerationTool$outboundSchema` instead. */
  export const outboundSchema = ImageGenerationTool$outboundSchema;
  /** @deprecated use `ImageGenerationTool$Outbound` instead. */
  export type Outbound = ImageGenerationTool$Outbound;
}

export function imageGenerationToolToJSON(
  imageGenerationTool: ImageGenerationTool,
): string {
  return JSON.stringify(
    ImageGenerationTool$outboundSchema.parse(imageGenerationTool),
  );
}

export function imageGenerationToolFromJSON(
  jsonString: string,
): SafeParseResult<ImageGenerationTool, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ImageGenerationTool$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ImageGenerationTool' from JSON`,
  );
}
