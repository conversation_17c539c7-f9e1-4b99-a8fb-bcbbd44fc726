/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type RetrieveModelV1ModelsModelIdGetRequest = {
  /**
   * The ID of the model to retrieve.
   */
  modelId: string;
};

/**
 * Successful Response
 */
export type RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet =
  | (components.BaseModelCard & { type: "base" })
  | (components.FTModelCard & { type: "fine-tuned" });

/** @internal */
export const RetrieveModelV1ModelsModelIdGetRequest$inboundSchema: z.ZodType<
  RetrieveModelV1ModelsModelIdGetRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  model_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "model_id": "modelId",
  });
});

/** @internal */
export type RetrieveModelV1ModelsModelIdGetRequest$Outbound = {
  model_id: string;
};

/** @internal */
export const RetrieveModelV1ModelsModelIdGetRequest$outboundSchema: z.ZodType<
  RetrieveModelV1ModelsModelIdGetRequest$Outbound,
  z.ZodTypeDef,
  RetrieveModelV1ModelsModelIdGetRequest
> = z.object({
  modelId: z.string(),
}).transform((v) => {
  return remap$(v, {
    modelId: "model_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RetrieveModelV1ModelsModelIdGetRequest$ {
  /** @deprecated use `RetrieveModelV1ModelsModelIdGetRequest$inboundSchema` instead. */
  export const inboundSchema =
    RetrieveModelV1ModelsModelIdGetRequest$inboundSchema;
  /** @deprecated use `RetrieveModelV1ModelsModelIdGetRequest$outboundSchema` instead. */
  export const outboundSchema =
    RetrieveModelV1ModelsModelIdGetRequest$outboundSchema;
  /** @deprecated use `RetrieveModelV1ModelsModelIdGetRequest$Outbound` instead. */
  export type Outbound = RetrieveModelV1ModelsModelIdGetRequest$Outbound;
}

export function retrieveModelV1ModelsModelIdGetRequestToJSON(
  retrieveModelV1ModelsModelIdGetRequest:
    RetrieveModelV1ModelsModelIdGetRequest,
): string {
  return JSON.stringify(
    RetrieveModelV1ModelsModelIdGetRequest$outboundSchema.parse(
      retrieveModelV1ModelsModelIdGetRequest,
    ),
  );
}

export function retrieveModelV1ModelsModelIdGetRequestFromJSON(
  jsonString: string,
): SafeParseResult<RetrieveModelV1ModelsModelIdGetRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      RetrieveModelV1ModelsModelIdGetRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'RetrieveModelV1ModelsModelIdGetRequest' from JSON`,
  );
}

/** @internal */
export const RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$inboundSchema:
  z.ZodType<
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet,
    z.ZodTypeDef,
    unknown
  > = z.union([
    components.BaseModelCard$inboundSchema.and(
      z.object({ type: z.literal("base") }).transform((v) => ({
        type: v.type,
      })),
    ),
    components.FTModelCard$inboundSchema.and(
      z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
        type: v.type,
      })),
    ),
  ]);

/** @internal */
export type RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound =
  | (components.BaseModelCard$Outbound & { type: "base" })
  | (components.FTModelCard$Outbound & { type: "fine-tuned" });

/** @internal */
export const RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$outboundSchema:
  z.ZodType<
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound,
    z.ZodTypeDef,
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet
  > = z.union([
    components.BaseModelCard$outboundSchema.and(
      z.object({ type: z.literal("base") }).transform((v) => ({
        type: v.type,
      })),
    ),
    components.FTModelCard$outboundSchema.and(
      z.object({ type: z.literal("fine-tuned") }).transform((v) => ({
        type: v.type,
      })),
    ),
  ]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$ {
  /** @deprecated use `RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$inboundSchema` instead. */
  export const inboundSchema =
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$inboundSchema;
  /** @deprecated use `RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$outboundSchema` instead. */
  export const outboundSchema =
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$outboundSchema;
  /** @deprecated use `RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound` instead. */
  export type Outbound =
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$Outbound;
}

export function retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGetToJSON(
  retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet:
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet,
): string {
  return JSON.stringify(
    RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$outboundSchema
      .parse(
        retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet,
      ),
  );
}

export function retrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGetFromJSON(
  jsonString: string,
): SafeParseResult<
  RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet$inboundSchema
        .parse(JSON.parse(x)),
    `Failed to parse 'RetrieveModelV1ModelsModelIdGetResponseRetrieveModelV1ModelsModelIdGet' from JSON`,
  );
}
