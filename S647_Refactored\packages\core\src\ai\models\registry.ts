/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { ModelInfo } from '@inkbytefo/s647-shared';

/**
 * Model registry for managing available models
 */
export class ModelRegistry {
  private models = new Map<string, ModelInfo>();

  public register(model: ModelInfo): void {
    this.models.set(model.id, model);
  }

  public unregister(modelId: string): void {
    this.models.delete(modelId);
  }

  public get(modelId: string): ModelInfo | undefined {
    return this.models.get(modelId);
  }

  public getAll(): ModelInfo[] {
    return Array.from(this.models.values());
  }

  public has(modelId: string): boolean {
    return this.models.has(modelId);
  }

  public clear(): void {
    this.models.clear();
  }
}
