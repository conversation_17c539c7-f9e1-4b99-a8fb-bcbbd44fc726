/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type SharingOut = {
  libraryId: string;
  userId?: string | null | undefined;
  orgId: string;
  role: string;
  shareWithType: string;
  shareWithUuid: string;
};

/** @internal */
export const SharingOut$inboundSchema: z.ZodType<
  SharingOut,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  user_id: z.nullable(z.string()).optional(),
  org_id: z.string(),
  role: z.string(),
  share_with_type: z.string(),
  share_with_uuid: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "user_id": "userId",
    "org_id": "orgId",
    "share_with_type": "shareWithType",
    "share_with_uuid": "shareWithUuid",
  });
});

/** @internal */
export type SharingOut$Outbound = {
  library_id: string;
  user_id?: string | null | undefined;
  org_id: string;
  role: string;
  share_with_type: string;
  share_with_uuid: string;
};

/** @internal */
export const SharingOut$outboundSchema: z.ZodType<
  SharingOut$Outbound,
  z.ZodTypeDef,
  SharingOut
> = z.object({
  libraryId: z.string(),
  userId: z.nullable(z.string()).optional(),
  orgId: z.string(),
  role: z.string(),
  shareWithType: z.string(),
  shareWithUuid: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    userId: "user_id",
    orgId: "org_id",
    shareWithType: "share_with_type",
    shareWithUuid: "share_with_uuid",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SharingOut$ {
  /** @deprecated use `SharingOut$inboundSchema` instead. */
  export const inboundSchema = SharingOut$inboundSchema;
  /** @deprecated use `SharingOut$outboundSchema` instead. */
  export const outboundSchema = SharingOut$outboundSchema;
  /** @deprecated use `SharingOut$Outbound` instead. */
  export type Outbound = SharingOut$Outbound;
}

export function sharingOutToJSON(sharingOut: SharingOut): string {
  return JSON.stringify(SharingOut$outboundSchema.parse(sharingOut));
}

export function sharingOutFromJSON(
  jsonString: string,
): SafeParseResult<SharingOut, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => SharingOut$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'SharingOut' from JSON`,
  );
}
