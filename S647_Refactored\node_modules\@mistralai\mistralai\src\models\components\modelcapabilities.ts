/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type ModelCapabilities = {
  completionChat?: boolean | undefined;
  completionFim?: boolean | undefined;
  functionCalling?: boolean | undefined;
  fineTuning?: boolean | undefined;
  vision?: boolean | undefined;
  classification?: boolean | undefined;
};

/** @internal */
export const ModelCapabilities$inboundSchema: z.ZodType<
  ModelCapabilities,
  z.ZodTypeDef,
  unknown
> = z.object({
  completion_chat: z.boolean().default(true),
  completion_fim: z.boolean().default(false),
  function_calling: z.boolean().default(true),
  fine_tuning: z.boolean().default(false),
  vision: z.boolean().default(false),
  classification: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    "completion_chat": "completionChat",
    "completion_fim": "completionFim",
    "function_calling": "functionCalling",
    "fine_tuning": "fineTuning",
  });
});

/** @internal */
export type ModelCapabilities$Outbound = {
  completion_chat: boolean;
  completion_fim: boolean;
  function_calling: boolean;
  fine_tuning: boolean;
  vision: boolean;
  classification: boolean;
};

/** @internal */
export const ModelCapabilities$outboundSchema: z.ZodType<
  ModelCapabilities$Outbound,
  z.ZodTypeDef,
  ModelCapabilities
> = z.object({
  completionChat: z.boolean().default(true),
  completionFim: z.boolean().default(false),
  functionCalling: z.boolean().default(true),
  fineTuning: z.boolean().default(false),
  vision: z.boolean().default(false),
  classification: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    completionChat: "completion_chat",
    completionFim: "completion_fim",
    functionCalling: "function_calling",
    fineTuning: "fine_tuning",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModelCapabilities$ {
  /** @deprecated use `ModelCapabilities$inboundSchema` instead. */
  export const inboundSchema = ModelCapabilities$inboundSchema;
  /** @deprecated use `ModelCapabilities$outboundSchema` instead. */
  export const outboundSchema = ModelCapabilities$outboundSchema;
  /** @deprecated use `ModelCapabilities$Outbound` instead. */
  export type Outbound = ModelCapabilities$Outbound;
}

export function modelCapabilitiesToJSON(
  modelCapabilities: ModelCapabilities,
): string {
  return JSON.stringify(
    ModelCapabilities$outboundSchema.parse(modelCapabilities),
  );
}

export function modelCapabilitiesFromJSON(
  jsonString: string,
): SafeParseResult<ModelCapabilities, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ModelCapabilities$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ModelCapabilities' from JSON`,
  );
}
