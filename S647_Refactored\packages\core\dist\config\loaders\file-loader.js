/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { promises as fs } from 'fs';
import { join, resolve } from 'path';
import { homedir } from 'os';
/**
 * File-based configuration loader
 * Loads configuration from JSON files
 */
export class FileConfigLoader {
    name = 'file';
    priority = 50; // Medium priority
    configPaths;
    constructor(configPaths) {
        this.configPaths = configPaths || this.getDefaultConfigPaths();
    }
    /**
     * Get default configuration file paths in order of priority
     */
    getDefaultConfigPaths() {
        return [
            // Project-specific config (highest priority)
            '.s647/config.json',
            '.s647.json',
            's647.config.json',
            // Global config (lower priority)
            join(homedir(), '.s647', 'config.json'),
            join(homedir(), '.s647.json'),
        ];
    }
    /**
     * Load configuration from files
     */
    async load(options) {
        const baseDir = options?.baseDir || process.cwd();
        const warnings = [];
        for (const configPath of this.configPaths) {
            try {
                const fullPath = resolve(baseDir, configPath);
                // Check if file exists
                if (!(await this.fileExists(fullPath))) {
                    continue;
                }
                // Read and parse config file
                const configData = await this.readConfigFile(fullPath);
                return {
                    success: true,
                    config: configData,
                    source: {
                        type: 'file',
                        path: fullPath,
                        priority: this.priority,
                        timestamp: Date.now(),
                        metadata: {
                            loader: this.name,
                            configPath,
                        },
                    },
                    ...(warnings.length > 0 && { warnings }),
                };
            }
            catch (error) {
                warnings.push(`Failed to load config from ${configPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                continue;
            }
        }
        // No config file found
        return {
            success: false,
            error: new Error('No configuration file found'),
            ...(warnings.length > 0 && { warnings }),
        };
    }
    /**
     * Check if this loader can load configuration
     */
    async canLoad(options) {
        const baseDir = options?.baseDir || process.cwd();
        for (const configPath of this.configPaths) {
            const fullPath = resolve(baseDir, configPath);
            if (await this.fileExists(fullPath)) {
                return true;
            }
        }
        return false;
    }
    /**
     * Check if file exists
     */
    async fileExists(path) {
        try {
            await fs.access(path);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Read and parse configuration file
     */
    async readConfigFile(path) {
        try {
            const content = await fs.readFile(path, 'utf-8');
            const parsed = JSON.parse(content);
            if (typeof parsed !== 'object' || parsed === null) {
                throw new Error('Configuration file must contain a JSON object');
            }
            return parsed;
        }
        catch (error) {
            if (error instanceof SyntaxError) {
                throw new Error(`Invalid JSON in configuration file: ${error.message}`);
            }
            throw error;
        }
    }
}
//# sourceMappingURL=file-loader.js.map