/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CodeInterpreterTool,
  CodeInterpreterTool$inboundSchema,
  CodeInterpreterTool$Outbound,
  CodeInterpreterTool$outboundSchema,
} from "./codeinterpretertool.js";
import {
  CompletionArgs,
  CompletionArgs$inboundSchema,
  CompletionArgs$Outbound,
  CompletionArgs$outboundSchema,
} from "./completionargs.js";
import {
  DocumentLibraryTool,
  DocumentLibraryTool$inboundSchema,
  DocumentLibraryTool$Outbound,
  DocumentLibraryTool$outboundSchema,
} from "./documentlibrarytool.js";
import {
  FunctionTool,
  FunctionTool$inboundSchema,
  FunctionTool$Outbound,
  FunctionTool$outboundSchema,
} from "./functiontool.js";
import {
  ImageGenerationTool,
  ImageGenerationTool$inboundSchema,
  ImageGenerationTool$Outbound,
  ImageGenerationTool$outboundSchema,
} from "./imagegenerationtool.js";
import {
  WebSearchPremiumTool,
  WebSearchPremiumTool$inboundSchema,
  WebSearchPremiumTool$Outbound,
  WebSearchPremiumTool$outboundSchema,
} from "./websearchpremiumtool.js";
import {
  WebSearchTool,
  WebSearchTool$inboundSchema,
  WebSearchTool$Outbound,
  WebSearchTool$outboundSchema,
} from "./websearchtool.js";

export type ModelConversationTools =
  | (CodeInterpreterTool & { type: "code_interpreter" })
  | (ImageGenerationTool & { type: "image_generation" })
  | (WebSearchTool & { type: "web_search" })
  | (WebSearchPremiumTool & { type: "web_search_premium" })
  | (DocumentLibraryTool & { type: "document_library" })
  | (FunctionTool & { type: "function" });

export const ModelConversationObject = {
  Conversation: "conversation",
} as const;
export type ModelConversationObject = ClosedEnum<
  typeof ModelConversationObject
>;

export type ModelConversation = {
  /**
   * Instruction prompt the model will follow during the conversation.
   */
  instructions?: string | null | undefined;
  /**
   * List of tools which are available to the model during the conversation.
   */
  tools?:
    | Array<
      | (CodeInterpreterTool & { type: "code_interpreter" })
      | (ImageGenerationTool & { type: "image_generation" })
      | (WebSearchTool & { type: "web_search" })
      | (WebSearchPremiumTool & { type: "web_search_premium" })
      | (DocumentLibraryTool & { type: "document_library" })
      | (FunctionTool & { type: "function" })
    >
    | undefined;
  /**
   * White-listed arguments from the completion API
   */
  completionArgs?: CompletionArgs | undefined;
  /**
   * Name given to the conversation.
   */
  name?: string | null | undefined;
  /**
   * Description of the what the conversation is about.
   */
  description?: string | null | undefined;
  object?: ModelConversationObject | undefined;
  id: string;
  createdAt: Date;
  updatedAt: Date;
  model: string;
};

/** @internal */
export const ModelConversationTools$inboundSchema: z.ZodType<
  ModelConversationTools,
  z.ZodTypeDef,
  unknown
> = z.union([
  CodeInterpreterTool$inboundSchema.and(
    z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
      type: v.type,
    })),
  ),
  ImageGenerationTool$inboundSchema.and(
    z.object({ type: z.literal("image_generation") }).transform((v) => ({
      type: v.type,
    })),
  ),
  WebSearchTool$inboundSchema.and(
    z.object({ type: z.literal("web_search") }).transform((v) => ({
      type: v.type,
    })),
  ),
  WebSearchPremiumTool$inboundSchema.and(
    z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
      type: v.type,
    })),
  ),
  DocumentLibraryTool$inboundSchema.and(
    z.object({ type: z.literal("document_library") }).transform((v) => ({
      type: v.type,
    })),
  ),
  FunctionTool$inboundSchema.and(
    z.object({ type: z.literal("function") }).transform((v) => ({
      type: v.type,
    })),
  ),
]);

/** @internal */
export type ModelConversationTools$Outbound =
  | (CodeInterpreterTool$Outbound & { type: "code_interpreter" })
  | (ImageGenerationTool$Outbound & { type: "image_generation" })
  | (WebSearchTool$Outbound & { type: "web_search" })
  | (WebSearchPremiumTool$Outbound & { type: "web_search_premium" })
  | (DocumentLibraryTool$Outbound & { type: "document_library" })
  | (FunctionTool$Outbound & { type: "function" });

/** @internal */
export const ModelConversationTools$outboundSchema: z.ZodType<
  ModelConversationTools$Outbound,
  z.ZodTypeDef,
  ModelConversationTools
> = z.union([
  CodeInterpreterTool$outboundSchema.and(
    z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
      type: v.type,
    })),
  ),
  ImageGenerationTool$outboundSchema.and(
    z.object({ type: z.literal("image_generation") }).transform((v) => ({
      type: v.type,
    })),
  ),
  WebSearchTool$outboundSchema.and(
    z.object({ type: z.literal("web_search") }).transform((v) => ({
      type: v.type,
    })),
  ),
  WebSearchPremiumTool$outboundSchema.and(
    z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
      type: v.type,
    })),
  ),
  DocumentLibraryTool$outboundSchema.and(
    z.object({ type: z.literal("document_library") }).transform((v) => ({
      type: v.type,
    })),
  ),
  FunctionTool$outboundSchema.and(
    z.object({ type: z.literal("function") }).transform((v) => ({
      type: v.type,
    })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModelConversationTools$ {
  /** @deprecated use `ModelConversationTools$inboundSchema` instead. */
  export const inboundSchema = ModelConversationTools$inboundSchema;
  /** @deprecated use `ModelConversationTools$outboundSchema` instead. */
  export const outboundSchema = ModelConversationTools$outboundSchema;
  /** @deprecated use `ModelConversationTools$Outbound` instead. */
  export type Outbound = ModelConversationTools$Outbound;
}

export function modelConversationToolsToJSON(
  modelConversationTools: ModelConversationTools,
): string {
  return JSON.stringify(
    ModelConversationTools$outboundSchema.parse(modelConversationTools),
  );
}

export function modelConversationToolsFromJSON(
  jsonString: string,
): SafeParseResult<ModelConversationTools, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ModelConversationTools$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ModelConversationTools' from JSON`,
  );
}

/** @internal */
export const ModelConversationObject$inboundSchema: z.ZodNativeEnum<
  typeof ModelConversationObject
> = z.nativeEnum(ModelConversationObject);

/** @internal */
export const ModelConversationObject$outboundSchema: z.ZodNativeEnum<
  typeof ModelConversationObject
> = ModelConversationObject$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModelConversationObject$ {
  /** @deprecated use `ModelConversationObject$inboundSchema` instead. */
  export const inboundSchema = ModelConversationObject$inboundSchema;
  /** @deprecated use `ModelConversationObject$outboundSchema` instead. */
  export const outboundSchema = ModelConversationObject$outboundSchema;
}

/** @internal */
export const ModelConversation$inboundSchema: z.ZodType<
  ModelConversation,
  z.ZodTypeDef,
  unknown
> = z.object({
  instructions: z.nullable(z.string()).optional(),
  tools: z.array(
    z.union([
      CodeInterpreterTool$inboundSchema.and(
        z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
          type: v.type,
        })),
      ),
      ImageGenerationTool$inboundSchema.and(
        z.object({ type: z.literal("image_generation") }).transform((v) => ({
          type: v.type,
        })),
      ),
      WebSearchTool$inboundSchema.and(
        z.object({ type: z.literal("web_search") }).transform((v) => ({
          type: v.type,
        })),
      ),
      WebSearchPremiumTool$inboundSchema.and(
        z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
          type: v.type,
        })),
      ),
      DocumentLibraryTool$inboundSchema.and(
        z.object({ type: z.literal("document_library") }).transform((v) => ({
          type: v.type,
        })),
      ),
      FunctionTool$inboundSchema.and(
        z.object({ type: z.literal("function") }).transform((v) => ({
          type: v.type,
        })),
      ),
    ]),
  ).optional(),
  completion_args: CompletionArgs$inboundSchema.optional(),
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
  object: ModelConversationObject$inboundSchema.default("conversation"),
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  updated_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  model: z.string(),
}).transform((v) => {
  return remap$(v, {
    "completion_args": "completionArgs",
    "created_at": "createdAt",
    "updated_at": "updatedAt",
  });
});

/** @internal */
export type ModelConversation$Outbound = {
  instructions?: string | null | undefined;
  tools?:
    | Array<
      | (CodeInterpreterTool$Outbound & { type: "code_interpreter" })
      | (ImageGenerationTool$Outbound & { type: "image_generation" })
      | (WebSearchTool$Outbound & { type: "web_search" })
      | (WebSearchPremiumTool$Outbound & { type: "web_search_premium" })
      | (DocumentLibraryTool$Outbound & { type: "document_library" })
      | (FunctionTool$Outbound & { type: "function" })
    >
    | undefined;
  completion_args?: CompletionArgs$Outbound | undefined;
  name?: string | null | undefined;
  description?: string | null | undefined;
  object: string;
  id: string;
  created_at: string;
  updated_at: string;
  model: string;
};

/** @internal */
export const ModelConversation$outboundSchema: z.ZodType<
  ModelConversation$Outbound,
  z.ZodTypeDef,
  ModelConversation
> = z.object({
  instructions: z.nullable(z.string()).optional(),
  tools: z.array(
    z.union([
      CodeInterpreterTool$outboundSchema.and(
        z.object({ type: z.literal("code_interpreter") }).transform((v) => ({
          type: v.type,
        })),
      ),
      ImageGenerationTool$outboundSchema.and(
        z.object({ type: z.literal("image_generation") }).transform((v) => ({
          type: v.type,
        })),
      ),
      WebSearchTool$outboundSchema.and(
        z.object({ type: z.literal("web_search") }).transform((v) => ({
          type: v.type,
        })),
      ),
      WebSearchPremiumTool$outboundSchema.and(
        z.object({ type: z.literal("web_search_premium") }).transform((v) => ({
          type: v.type,
        })),
      ),
      DocumentLibraryTool$outboundSchema.and(
        z.object({ type: z.literal("document_library") }).transform((v) => ({
          type: v.type,
        })),
      ),
      FunctionTool$outboundSchema.and(
        z.object({ type: z.literal("function") }).transform((v) => ({
          type: v.type,
        })),
      ),
    ]),
  ).optional(),
  completionArgs: CompletionArgs$outboundSchema.optional(),
  name: z.nullable(z.string()).optional(),
  description: z.nullable(z.string()).optional(),
  object: ModelConversationObject$outboundSchema.default("conversation"),
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  updatedAt: z.date().transform(v => v.toISOString()),
  model: z.string(),
}).transform((v) => {
  return remap$(v, {
    completionArgs: "completion_args",
    createdAt: "created_at",
    updatedAt: "updated_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ModelConversation$ {
  /** @deprecated use `ModelConversation$inboundSchema` instead. */
  export const inboundSchema = ModelConversation$inboundSchema;
  /** @deprecated use `ModelConversation$outboundSchema` instead. */
  export const outboundSchema = ModelConversation$outboundSchema;
  /** @deprecated use `ModelConversation$Outbound` instead. */
  export type Outbound = ModelConversation$Outbound;
}

export function modelConversationToJSON(
  modelConversation: ModelConversation,
): string {
  return JSON.stringify(
    ModelConversation$outboundSchema.parse(modelConversation),
  );
}

export function modelConversationFromJSON(
  jsonString: string,
): SafeParseResult<ModelConversation, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ModelConversation$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ModelConversation' from JSON`,
  );
}
