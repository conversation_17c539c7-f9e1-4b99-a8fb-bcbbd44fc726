/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { betaAgentsCreate } from "../funcs/betaAgentsCreate.js";
import { betaAgentsGet } from "../funcs/betaAgentsGet.js";
import { betaAgentsList } from "../funcs/betaAgentsList.js";
import { betaAgentsUpdate } from "../funcs/betaAgentsUpdate.js";
import { betaAgentsUpdateVersion } from "../funcs/betaAgentsUpdateVersion.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { unwrapAsync } from "../types/fp.js";

export class MistralAgents extends ClientSDK {
  /**
   * Create a agent that can be used within a conversation.
   *
   * @remarks
   * Create a new agent giving it instructions, tools, description. The agent is then available to be used as a regular assistant in a conversation or as part of an agent pool from which it can be used.
   */
  async create(
    request: components.AgentCreationRequest,
    options?: RequestOptions,
  ): Promise<components.Agent> {
    return unwrapAsync(betaAgentsCreate(
      this,
      request,
      options,
    ));
  }

  /**
   * List agent entities.
   *
   * @remarks
   * Retrieve a list of agent entities sorted by creation time.
   */
  async list(
    request?: operations.AgentsApiV1AgentsListRequest | undefined,
    options?: RequestOptions,
  ): Promise<Array<components.Agent>> {
    return unwrapAsync(betaAgentsList(
      this,
      request,
      options,
    ));
  }

  /**
   * Retrieve an agent entity.
   *
   * @remarks
   * Given an agent retrieve an agent entity with its attributes.
   */
  async get(
    request: operations.AgentsApiV1AgentsGetRequest,
    options?: RequestOptions,
  ): Promise<components.Agent> {
    return unwrapAsync(betaAgentsGet(
      this,
      request,
      options,
    ));
  }

  /**
   * Update an agent entity.
   *
   * @remarks
   * Update an agent attributes and create a new version.
   */
  async update(
    request: operations.AgentsApiV1AgentsUpdateRequest,
    options?: RequestOptions,
  ): Promise<components.Agent> {
    return unwrapAsync(betaAgentsUpdate(
      this,
      request,
      options,
    ));
  }

  /**
   * Update an agent version.
   *
   * @remarks
   * Switch the version of an agent.
   */
  async updateVersion(
    request: operations.AgentsApiV1AgentsUpdateVersionRequest,
    options?: RequestOptions,
  ): Promise<components.Agent> {
    return unwrapAsync(betaAgentsUpdateVersion(
      this,
      request,
      options,
    ));
  }
}
