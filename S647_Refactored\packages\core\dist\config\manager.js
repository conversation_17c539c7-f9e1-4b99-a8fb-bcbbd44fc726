/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { ConfigurationSchema } from '@inkbytefo/s647-shared';
import { ConfigLoaderRegistry } from './loaders/registry.js';
/**
 * Configuration manager
 * Handles loading, saving, and validation of configuration
 */
export class ConfigurationManager {
    loaderRegistry;
    cachedConfig;
    configPath;
    constructor() {
        this.loaderRegistry = new ConfigLoaderRegistry();
    }
    /**
     * Load configuration from all sources
     */
    async load(options) {
        try {
            const result = await this.loaderRegistry.loadConfiguration(options);
            if (!result.success || !result.config) {
                return {
                    success: false,
                    error: result.error || new Error('Failed to load configuration'),
                };
            }
            // Validate the merged configuration
            const validationResult = await this.validate(result.config);
            if (!validationResult.success) {
                return {
                    success: false,
                    error: validationResult.error || new Error('Configuration validation failed'),
                };
            }
            // Cache the loaded configuration
            this.cachedConfig = result.config;
            return {
                success: true,
                data: this.cachedConfig,
                ...(result.warnings && { warnings: result.warnings }),
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error : new Error('Unknown error loading configuration'),
            };
        }
    }
    /**
     * Save configuration to file
     */
    async save(config, path) {
        try {
            const configToSave = config || this.cachedConfig;
            if (!configToSave) {
                return {
                    success: false,
                    error: new Error('No configuration to save'),
                };
            }
            // Validate configuration before saving
            const validationResult = await this.validate(configToSave);
            if (!validationResult.success) {
                return {
                    success: false,
                    error: validationResult.error || new Error('Configuration validation failed'),
                };
            }
            // Determine save path
            const savePath = path || this.configPath || this.getDefaultConfigPath();
            // Ensure directory exists
            await this.ensureDirectoryExists(dirname(savePath));
            // Save configuration
            const configJson = JSON.stringify(configToSave, null, 2);
            await fs.writeFile(savePath, configJson, 'utf-8');
            // Update cached config and path
            this.cachedConfig = configToSave;
            this.configPath = savePath;
            return { success: true, data: undefined };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error : new Error('Unknown error saving configuration'),
            };
        }
    }
    /**
     * Validate configuration against schema
     */
    async validate(config) {
        try {
            const configToValidate = config || this.cachedConfig;
            if (!configToValidate) {
                return {
                    success: false,
                    error: new Error('No configuration to validate'),
                };
            }
            // Validate using Zod schema
            const result = ConfigurationSchema.safeParse(configToValidate);
            if (!result.success) {
                const errorMessages = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
                return {
                    success: false,
                    error: new Error(`Configuration validation failed: ${errorMessages}`),
                };
            }
            return { success: true, data: true };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error : new Error('Unknown error validating configuration'),
            };
        }
    }
    /**
     * Get cached configuration
     */
    getCached() {
        return this.cachedConfig;
    }
    /**
     * Clear cached configuration
     */
    clearCache() {
        this.cachedConfig = undefined;
        this.configPath = undefined;
    }
    /**
     * Get default configuration file path
     */
    getDefaultConfigPath() {
        return join(process.cwd(), '.s647', 'config.json');
    }
    /**
     * Ensure directory exists
     */
    async ensureDirectoryExists(dirPath) {
        try {
            await fs.mkdir(dirPath, { recursive: true });
        }
        catch (error) {
            // Ignore error if directory already exists
            if (error?.code !== 'EEXIST') {
                throw error;
            }
        }
    }
    /**
     * Get configuration loader registry
     */
    getLoaderRegistry() {
        return this.loaderRegistry;
    }
    /**
     * Reload configuration from sources
     */
    async reload(options) {
        this.clearCache();
        return this.load(options);
    }
}
//# sourceMappingURL=manager.js.map