/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Configuration, AsyncResult, ExtendedAsyncResult } from '@inkbytefo/s647-shared';
import { ConfigLoaderRegistry } from './loaders/registry.js';
import type { ConfigLoaderOptions } from './loaders/types.js';
/**
 * Configuration manager
 * Handles loading, saving, and validation of configuration
 */
export declare class ConfigurationManager {
    private readonly loaderRegistry;
    private cachedConfig?;
    private configPath?;
    constructor();
    /**
     * Load configuration from all sources
     */
    load(options?: ConfigLoaderOptions): ExtendedAsyncResult<Configuration>;
    /**
     * Save configuration to file
     */
    save(config?: Configuration, path?: string): AsyncResult<void>;
    /**
     * Validate configuration against schema
     */
    validate(config?: Partial<Configuration>): AsyncResult<boolean>;
    /**
     * Get cached configuration
     */
    getCached(): Configuration | undefined;
    /**
     * Clear cached configuration
     */
    clearCache(): void;
    /**
     * Get default configuration file path
     */
    private getDefaultConfigPath;
    /**
     * Ensure directory exists
     */
    private ensureDirectoryExists;
    /**
     * Get configuration loader registry
     */
    getLoaderRegistry(): ConfigLoaderRegistry;
    /**
     * Reload configuration from sources
     */
    reload(options?: ConfigLoaderOptions): AsyncResult<Configuration>;
}
//# sourceMappingURL=manager.d.ts.map