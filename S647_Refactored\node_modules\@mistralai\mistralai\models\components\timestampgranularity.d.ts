import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
export declare const TimestampGranularity: {
    readonly Segment: "segment";
};
export type TimestampGranularity = ClosedEnum<typeof TimestampGranularity>;
/** @internal */
export declare const TimestampGranularity$inboundSchema: z.ZodNativeEnum<typeof TimestampGranularity>;
/** @internal */
export declare const TimestampGranularity$outboundSchema: z.ZodNativeEnum<typeof TimestampGranularity>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace TimestampGranularity$ {
    /** @deprecated use `TimestampGranularity$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly Segment: "segment";
    }>;
    /** @deprecated use `TimestampGranularity$outboundSchema` instead. */
    const outboundSchema: z.<PERSON><{
        readonly Segment: "segment";
    }>;
}
//# sourceMappingURL=timestampgranularity.d.ts.map