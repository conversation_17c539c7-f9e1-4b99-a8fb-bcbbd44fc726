/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type LibrariesDocumentsGetSignedUrlV1Request = {
  libraryId: string;
  documentId: string;
};

/** @internal */
export const LibrariesDocumentsGetSignedUrlV1Request$inboundSchema: z.ZodType<
  LibrariesDocumentsGetSignedUrlV1Request,
  z.ZodTypeDef,
  unknown
> = z.object({
  library_id: z.string(),
  document_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "library_id": "libraryId",
    "document_id": "documentId",
  });
});

/** @internal */
export type LibrariesDocumentsGetSignedUrlV1Request$Outbound = {
  library_id: string;
  document_id: string;
};

/** @internal */
export const LibrariesDocumentsGetSignedUrlV1Request$outboundSchema: z.ZodType<
  LibrariesDocumentsGetSignedUrlV1Request$Outbound,
  z.ZodTypeDef,
  LibrariesDocumentsGetSignedUrlV1Request
> = z.object({
  libraryId: z.string(),
  documentId: z.string(),
}).transform((v) => {
  return remap$(v, {
    libraryId: "library_id",
    documentId: "document_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LibrariesDocumentsGetSignedUrlV1Request$ {
  /** @deprecated use `LibrariesDocumentsGetSignedUrlV1Request$inboundSchema` instead. */
  export const inboundSchema =
    LibrariesDocumentsGetSignedUrlV1Request$inboundSchema;
  /** @deprecated use `LibrariesDocumentsGetSignedUrlV1Request$outboundSchema` instead. */
  export const outboundSchema =
    LibrariesDocumentsGetSignedUrlV1Request$outboundSchema;
  /** @deprecated use `LibrariesDocumentsGetSignedUrlV1Request$Outbound` instead. */
  export type Outbound = LibrariesDocumentsGetSignedUrlV1Request$Outbound;
}

export function librariesDocumentsGetSignedUrlV1RequestToJSON(
  librariesDocumentsGetSignedUrlV1Request:
    LibrariesDocumentsGetSignedUrlV1Request,
): string {
  return JSON.stringify(
    LibrariesDocumentsGetSignedUrlV1Request$outboundSchema.parse(
      librariesDocumentsGetSignedUrlV1Request,
    ),
  );
}

export function librariesDocumentsGetSignedUrlV1RequestFromJSON(
  jsonString: string,
): SafeParseResult<
  LibrariesDocumentsGetSignedUrlV1Request,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      LibrariesDocumentsGetSignedUrlV1Request$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'LibrariesDocumentsGetSignedUrlV1Request' from JSON`,
  );
}
