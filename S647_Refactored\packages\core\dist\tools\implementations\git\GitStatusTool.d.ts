/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON>lParams, ToolContext, ToolResult, Tool<PERSON>elp, AsyncResult } from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';
/**
 * Tool for getting git repository status
 */
export declare class GitStatusTool extends BaseTool {
    constructor();
    execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    getHelp(): ToolHelp;
    private resolvePath;
    private formatHuman;
    private formatPorcelain;
}
//# sourceMappingURL=GitStatusTool.d.ts.map