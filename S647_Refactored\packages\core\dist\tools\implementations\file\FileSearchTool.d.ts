/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON>lParams, ToolContext, ToolResult, Tool<PERSON>elp, AsyncResult } from '@inkbytefo/s647-shared';
import { BaseTool } from '../../base/BaseTool.js';
/**
 * Tool for searching files and directories
 */
export declare class FileSearchTool extends BaseTool {
    constructor();
    execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;
    getHelp(): ToolHelp;
    private resolvePath;
    private searchFiles;
    private formatResults;
    private formatSize;
}
//# sourceMappingURL=FileSearchTool.d.ts.map