/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { DefaultConfigLoader } from './default-loader.js';
import { FileConfigLoader } from './file-loader.js';
import { EnvironmentConfigLoader } from './env-loader.js';
import { CliConfigLoader } from './cli-loader.js';
/**
 * Configuration loader registry
 * Manages multiple configuration loaders and merges their results
 */
export class ConfigLoaderRegistry {
    loaders = new Map();
    constructor() {
        // Register default loaders
        this.registerDefaultLoaders();
    }
    /**
     * Register a configuration loader
     */
    register(loader) {
        this.loaders.set(loader.name, loader);
    }
    /**
     * Unregister a configuration loader
     */
    unregister(name) {
        return this.loaders.delete(name);
    }
    /**
     * Get a configuration loader by name
     */
    get(name) {
        return this.loaders.get(name);
    }
    /**
     * Get all registered loaders
     */
    getAll() {
        return Array.from(this.loaders.values());
    }
    /**
     * Load configuration from all loaders and merge results
     */
    async loadConfiguration(options) {
        const results = [];
        const warnings = [];
        // Get loaders sorted by priority (highest first)
        const sortedLoaders = this.getAll().sort((a, b) => b.priority - a.priority);
        // Load from each loader
        for (const loader of sortedLoaders) {
            try {
                if (await loader.canLoad(options)) {
                    const result = await loader.load(options);
                    results.push(result);
                    if (result.warnings) {
                        warnings.push(...result.warnings);
                    }
                }
            }
            catch (error) {
                warnings.push(`Loader ${loader.name} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        // Merge configurations
        const mergedConfig = this.mergeConfigurations(results);
        return {
            success: true,
            config: mergedConfig,
            source: {
                type: 'file', // Primary source type
                priority: 0,
                timestamp: Date.now(),
                metadata: {
                    loaderCount: results.length,
                    sources: results.map(r => r.source).filter(Boolean),
                },
            },
            ...(warnings.length > 0 && { warnings }),
        };
    }
    /**
     * Merge configurations from multiple sources
     */
    mergeConfigurations(results, options) {
        const merged = {};
        // Start with lowest priority and work up
        const sortedResults = results
            .filter(r => r.success && r.config)
            .sort((a, b) => (a.source?.priority || 0) - (b.source?.priority || 0));
        for (const result of sortedResults) {
            if (result.config) {
                this.deepMerge(merged, result.config, options);
            }
        }
        return merged;
    }
    /**
     * Deep merge two configuration objects
     */
    deepMerge(target, source, options) {
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                const sourceValue = source[key];
                const targetValue = target[key];
                if (this.isObject(sourceValue) && this.isObject(targetValue)) {
                    // Recursively merge objects
                    this.deepMerge(targetValue, sourceValue, options);
                }
                else if (Array.isArray(sourceValue) && Array.isArray(targetValue)) {
                    // Handle array merging based on strategy
                    const strategy = options?.fieldStrategies?.[key] || options?.strategy || 'replace';
                    switch (strategy) {
                        case 'append':
                            target[key] = [...targetValue, ...sourceValue];
                            break;
                        case 'merge':
                            // Merge unique values
                            target[key] = [...new Set([...targetValue, ...sourceValue])];
                            break;
                        case 'replace':
                        default:
                            target[key] = sourceValue;
                            break;
                    }
                }
                else {
                    // Replace primitive values
                    target[key] = sourceValue;
                }
            }
        }
    }
    /**
     * Check if value is a plain object
     */
    isObject(value) {
        return value !== null && typeof value === 'object' && !Array.isArray(value);
    }
    /**
     * Register default configuration loaders
     */
    registerDefaultLoaders() {
        this.register(new DefaultConfigLoader());
        this.register(new FileConfigLoader());
        this.register(new EnvironmentConfigLoader());
        this.register(new CliConfigLoader());
    }
}
//# sourceMappingURL=registry.js.map