/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  Provider,
  ProviderType,
  ProviderId,
} from '@inkbytefo/s647-shared';
import type { IProviderRegistry } from '../interfaces/provider.js';

/**
 * Provider registry implementation
 */
export class ProviderRegistry implements IProviderRegistry {
  private providers = new Map<ProviderId, Provider>();
  private static instance: ProviderRegistry;

  /**
   * Get the singleton instance
   */
  public static getInstance(): ProviderRegistry {
    if (!ProviderRegistry.instance) {
      ProviderRegistry.instance = new ProviderRegistry();
    }
    return ProviderRegistry.instance;
  }

  /**
   * Register a provider
   */
  public register(provider: Provider): void {
    this.providers.set(provider.id, provider);
  }

  /**
   * Unregister a provider
   */
  public unregister(id: ProviderId): void {
    const provider = this.providers.get(id);
    if (provider) {
      provider.dispose();
      this.providers.delete(id);
    }
  }

  /**
   * Get a provider by ID
   */
  public get(id: ProviderId): Provider | undefined {
    return this.providers.get(id);
  }

  /**
   * Get all providers
   */
  public getAll(): Provider[] {
    return Array.from(this.providers.values());
  }

  /**
   * Get providers by type
   */
  public getByType(type: ProviderType): Provider[] {
    return this.getAll().filter(provider => provider.type === type);
  }

  /**
   * Get available providers
   */
  public getAvailable(): Provider[] {
    return this.getAll().filter(provider => provider.status === 'available');
  }

  /**
   * Find the best provider for a model
   */
  public findBestProvider(modelId: string): Provider | undefined {
    const availableProviders = this.getAvailable();
    
    // First, try to find a provider that explicitly supports this model
    for (const provider of availableProviders) {
      // This would require implementing getModels() and checking if the model is supported
      // For now, we'll use a simple heuristic based on model naming conventions
      if (this.providerSupportsModel(provider, modelId)) {
        return provider;
      }
    }

    // If no specific provider found, return the first available one
    return availableProviders[0];
  }

  /**
   * Dispose all providers
   */
  public async dispose(): Promise<void> {
    const disposePromises = Array.from(this.providers.values()).map(
      provider => provider.dispose()
    );
    
    await Promise.allSettled(disposePromises);
    this.providers.clear();
  }

  /**
   * Check if a provider supports a specific model
   */
  private providerSupportsModel(provider: Provider, modelId: string): boolean {
    const modelLower = modelId.toLowerCase();
    
    switch (provider.type) {
      case 'openai':
        return modelLower.includes('gpt') || 
               modelLower.includes('text-') || 
               modelLower.includes('davinci') ||
               modelLower.includes('curie') ||
               modelLower.includes('babbage') ||
               modelLower.includes('ada');
      
      case 'anthropic':
        return modelLower.includes('claude');
      
      case 'google':
        return modelLower.includes('gemini') || 
               modelLower.includes('palm') ||
               modelLower.includes('bison');
      
      case 'mistral':
        return modelLower.includes('mistral') ||
               modelLower.includes('mixtral');
      
      case 'openrouter':
        // OpenRouter supports many models
        return true;
      
      case 'custom':
      case 'local':
        // Custom and local providers can support any model
        return true;
      
      default:
        return false;
    }
  }

  /**
   * Get provider statistics
   */
  public getStats(): {
    total: number;
    available: number;
    byType: Record<ProviderType, number>;
  } {
    const all = this.getAll();
    const available = this.getAvailable();
    
    const byType: Record<string, number> = {};
    for (const provider of all) {
      byType[provider.type] = (byType[provider.type] || 0) + 1;
    }

    return {
      total: all.length,
      available: available.length,
      byType: byType as Record<ProviderType, number>,
    };
  }

  /**
   * Clear all providers (for testing)
   */
  public clear(): void {
    this.providers.clear();
  }
}
