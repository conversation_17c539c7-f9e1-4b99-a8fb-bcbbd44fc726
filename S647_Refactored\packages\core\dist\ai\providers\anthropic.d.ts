/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { AnthropicProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@inkbytefo/s647-shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * Anthropic provider implementation
 */
export declare class AnthropicProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: AnthropicProviderConfig);
    get anthropicConfig(): AnthropicProviderConfig;
    /**
     * Initialize the Anthropic provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings (not supported by Anthropic)
     */
    createEmbedding(_request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
    /**
     * Convert messages to Anthropic format
     */
    private convertMessages;
    /**
     * Convert tools to Anthropic format
     */
    private convertTools;
    /**
     * Convert stop reason to OpenAI format
     */
    private convertStopReason;
    /**
     * Convert Anthropic stream to OpenAI format
     */
    private convertStream;
}
//# sourceMappingURL=anthropic.d.ts.map