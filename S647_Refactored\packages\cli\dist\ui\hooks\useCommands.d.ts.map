{"version": 3, "file": "useCommands.d.ts", "sourceRoot": "", "sources": ["../../../src/ui/hooks/useCommands.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AACpE,OAAO,KAAK,EAAE,WAAW,EAAe,MAAM,mBAAmB,CAAC;AAElE;;GAEG;AACH,MAAM,WAAW,OAAO;IACtB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;IACnB,QAAQ,EAAE,QAAQ,GAAG,IAAI,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC;IACxD,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,cAAc,KAAK,OAAO,CAAC,aAAa,CAAC,CAAC;CAC9E;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,aAAa,CAAC;IACtB,MAAM,EAAE,MAAM,CAAC;IACf,cAAc,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,CAAC;IAC5C,YAAY,EAAE,MAAM,IAAI,CAAC;IACzB,UAAU,EAAE,MAAM,WAAW,EAAE,CAAC;IAChC,eAAe,EAAE,MAAM,CAAC;IACxB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;CAChD;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED;;GAEG;AACH,wBAAgB,WAAW,CACzB,MAAM,EAAE,aAAa,EACrB,MAAM,EAAE,MAAM,EACd,cAAc,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,EAC3C,YAAY,EAAE,MAAM,IAAI,EACxB,UAAU,EAAE,MAAM,WAAW,EAAE,EAC/B,eAAe,EAAE,MAAM,EACvB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI;;4BA6QG,MAAM,KAAG,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;qCAoDnC,MAAM,KAAG,MAAM,EAAE;EAsBtE"}