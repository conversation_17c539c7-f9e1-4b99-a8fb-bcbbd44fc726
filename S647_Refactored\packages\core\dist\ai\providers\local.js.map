{"version": 3, "file": "local.js", "sourceRoot": "", "sources": ["../../../src/ai/providers/local.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAcH,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAEzD;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,YAAY;IACrC,MAAM,CAAM,CAAC,6DAA6D;IAElF,YAAY,EAAc,EAAE,MAA2B;QACrD,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACpB,CAAC;IAED,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,MAA6B,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,+CAA+C;YAC/C,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE1C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;gBACvB,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,WAAW,EAAE,iDAAiD;gBACjG,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ;gBAClC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,KAAK,EAAE,+BAA+B;gBAC3E,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,EAAE,0BAA0B;gBACrE,cAAc,EAAE;oBACd,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;iBAC5B;aACF,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAE5B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,2BAA2B;YAC3B,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,MAAM,CAAC;gBACP,8DAA8D;gBAC9D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,SAAS,CAAC,CAAC;gBACpE,OAAO,QAAQ,CAAC,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,0CAA0C;YAC1C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACjD,MAAM,MAAM,GAAgB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;oBAC7D,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,KAAK,CAAC,EAAE;oBACd,QAAQ,EAAE,OAAgB;oBAC1B,YAAY,EAAE;wBACZ,IAAI,EAAE,IAAI;wBACV,UAAU,EAAE,IAAI;wBAChB,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;wBACrC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;wBACjE,eAAe,EAAE,KAAK,EAAE,uDAAuD;wBAC/E,SAAS,EAAE,IAAI;wBACf,cAAc,EAAE,IAAI;wBACpB,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;qBACtE;oBACD,aAAa,EAAE,KAAK,CAAC,cAAc,IAAI,IAAI;oBAC3C,SAAS,EAAE,KAAK,CAAC,qBAAqB,IAAI,IAAI;iBAC/C,CAAC,CAAC,CAAC;gBAEJ,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACzC,CAAC;YAAC,MAAM,CAAC;gBACP,wEAAwE;gBACxE,MAAM,MAAM,GAAgB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC;wBACtD,EAAE,EAAE,aAAa;wBACjB,IAAI,EAAE,aAAa;wBACnB,QAAQ,EAAE,OAAgB;wBAC1B,YAAY,EAAE;4BACZ,IAAI,EAAE,IAAI;4BACV,UAAU,EAAE,IAAI;4BAChB,SAAS,EAAE,KAAK;4BAChB,MAAM,EAAE,KAAK;4BACb,eAAe,EAAE,KAAK;4BACtB,SAAS,EAAE,IAAI;4BACf,cAAc,EAAE,IAAI;4BACpB,UAAU,EAAE,KAAK;yBAClB;wBACD,aAAa,EAAE,IAAI;wBACnB,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;gBAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAC/B,OAA8B;QAE9B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,KAAK,EAAE,OAAO,CAAC,IAAI;gBACnB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,KAAK;gBACb,4DAA4D;gBAC5D,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC9C,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;aAC/D,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAkC,EAAE,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B,CACrC,OAA8B;QAE9B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,KAAK,EAAE,OAAO,CAAC,IAAI;gBACnB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,IAAI;gBACZ,4DAA4D;gBAC5D,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC9C,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;aAC/D,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAA4C,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAC1B,OAAyB;QAEzB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACnD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;aACrB,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAA6B,EAAE,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,KAA6B;QAE7B,IAAI,CAAC;YACH,8DAA8D;YAC9D,0CAA0C;YAC1C,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK,QAAQ;gBACpC,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEnF,kFAAkF;YAClF,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;CACF"}