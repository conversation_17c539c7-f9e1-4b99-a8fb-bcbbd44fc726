/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Default performance configuration
 */
export const DEFAULT_PERFORMANCE = {
    maxConcurrentRequests: 10,
    requestTimeout: 30000, // 30 seconds
    retryDelay: 1000, // 1 second
    maxRetries: 3,
    caching: {
        enabled: true,
        maxSize: 100 * 1024 * 1024, // 100MB
        ttl: 3600000, // 1 hour
        checkPeriod: 600000, // 10 minutes
    },
    memory: {
        maxHeapSize: 512 * 1024 * 1024, // 512MB
        gcThreshold: 0.8, // 80%
        monitoringEnabled: true,
    },
    networking: {
        keepAlive: true,
        maxSockets: 50,
        timeout: 30000,
        retryOnTimeout: true,
    },
    fileSystem: {
        maxFileSize: 50 * 1024 * 1024, // 50MB
        maxConcurrentReads: 5,
        bufferSize: 64 * 1024, // 64KB
    },
};
/**
 * Performance configurations for different environments
 */
export const PERFORMANCE_CONFIGS = {
    development: {
        ...DEFAULT_PERFORMANCE,
        maxConcurrentRequests: 5,
        requestTimeout: 60000, // 1 minute
        caching: {
            ...DEFAULT_PERFORMANCE.caching,
            enabled: false, // Disable caching in development
        },
        memory: {
            ...DEFAULT_PERFORMANCE.memory,
            maxHeapSize: 256 * 1024 * 1024, // 256MB
            monitoringEnabled: true,
        },
    },
    production: {
        ...DEFAULT_PERFORMANCE,
        maxConcurrentRequests: 20,
        requestTimeout: 15000, // 15 seconds
        caching: {
            ...DEFAULT_PERFORMANCE.caching,
            enabled: true,
            maxSize: 500 * 1024 * 1024, // 500MB
        },
        memory: {
            ...DEFAULT_PERFORMANCE.memory,
            maxHeapSize: 1024 * 1024 * 1024, // 1GB
            monitoringEnabled: false,
        },
    },
    test: {
        ...DEFAULT_PERFORMANCE,
        maxConcurrentRequests: 2,
        requestTimeout: 5000, // 5 seconds
        caching: {
            ...DEFAULT_PERFORMANCE.caching,
            enabled: false,
        },
        memory: {
            ...DEFAULT_PERFORMANCE.memory,
            maxHeapSize: 128 * 1024 * 1024, // 128MB
            monitoringEnabled: false,
        },
    },
};
/**
 * Get performance configuration for environment
 */
export function getPerformanceConfigForEnvironment(env) {
    return PERFORMANCE_CONFIGS[env] || DEFAULT_PERFORMANCE;
}
/**
 * Calculate optimal concurrency based on system resources
 */
export function calculateOptimalConcurrency() {
    const cpuCount = require('os').cpus().length;
    const memoryGB = require('os').totalmem() / (1024 * 1024 * 1024);
    // Base concurrency on CPU count and available memory
    const baseConcurrency = Math.min(cpuCount * 2, Math.floor(memoryGB / 2));
    return Math.max(2, Math.min(baseConcurrency, 50));
}
/**
 * Memory usage monitoring utilities
 */
export function getMemoryUsage() {
    const usage = process.memoryUsage();
    const total = require('os').totalmem();
    const used = usage.heapUsed;
    return {
        used,
        total,
        percentage: (used / total) * 100,
    };
}
/**
 * Check if memory usage is above threshold
 */
export function isMemoryUsageHigh(threshold = 0.8) {
    const { percentage } = getMemoryUsage();
    return percentage / 100 > threshold;
}
//# sourceMappingURL=performance.js.map