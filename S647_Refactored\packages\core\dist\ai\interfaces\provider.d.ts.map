{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../../src/ai/interfaces/provider.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EACV,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,EACjB,SAAS,EACT,UAAU,EACV,WAAW,EACX,WAAW,EACZ,MAAM,wBAAwB,CAAC;AAEhC;;GAEG;AACH,8BAAsB,YAAa,YAAW,QAAQ;IACpD,SAAgB,EAAE,EAAE,UAAU,CAAC;IAC/B,SAAgB,IAAI,EAAE,YAAY,CAAC;IACnC,SAAgB,MAAM,EAAE,cAAc,CAAC;IACvC,SAAS,CAAC,OAAO,EAAE,cAAc,CAAa;gBAElC,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc;IAMlD,IAAW,MAAM,IAAI,cAAc,CAElC;IAED;;OAEG;aACa,UAAU,IAAI,WAAW,CAAC,IAAI,CAAC;IAE/C;;OAEG;aACa,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;IAE/C;;OAEG;aACa,SAAS,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;IAErD;;OAEG;aACa,oBAAoB,CAClC,OAAO,EAAE,qBAAqB,GAC7B,WAAW,CAAC,sBAAsB,CAAC;IAEtC;;OAEG;aACa,0BAA0B,CACxC,OAAO,EAAE,qBAAqB,GAC7B,WAAW,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;IAElD;;OAEG;aACa,eAAe,CAC7B,OAAO,EAAE,gBAAgB,GACxB,WAAW,CAAC,iBAAiB,CAAC;IAEjC;;OAEG;aACa,WAAW,CACzB,KAAK,EAAE,MAAM,GAAG,WAAW,EAAE,GAC5B,WAAW,CAAC,MAAM,CAAC;IAEtB;;OAEG;IACU,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIrC;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,IAAI;IAMhC;;OAEG;IACH,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK;IAO5C;;OAEG;IACH,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,cAAc,GAAG,IAAI;CAGlD;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IAEtE;;OAEG;IACH,iBAAiB,IAAI,YAAY,EAAE,CAAC;IAEpC;;OAEG;IACH,cAAc,CAAC,MAAM,EAAE,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;CAC3D;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI,CAAC;IAEnC;;OAEG;IACH,UAAU,CAAC,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC;IAEjC;;OAEG;IACH,GAAG,CAAC,EAAE,EAAE,UAAU,GAAG,QAAQ,GAAG,SAAS,CAAC;IAE1C;;OAEG;IACH,MAAM,IAAI,QAAQ,EAAE,CAAC;IAErB;;OAEG;IACH,SAAS,CAAC,IAAI,EAAE,YAAY,GAAG,QAAQ,EAAE,CAAC;IAE1C;;OAEG;IACH,YAAY,IAAI,QAAQ,EAAE,CAAC;IAE3B;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC;IAExD;;OAEG;IACH,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;CAC1B"}