/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ReferenceChunk,
  ReferenceChunk$inboundSchema,
  ReferenceChunk$Outbound,
  ReferenceChunk$outboundSchema,
} from "./referencechunk.js";
import {
  TextChunk,
  TextChunk$inboundSchema,
  TextChunk$Outbound,
  TextChunk$outboundSchema,
} from "./textchunk.js";

export type Thinking = ReferenceChunk | TextChunk;

export const ThinkChunkType = {
  Thinking: "thinking",
} as const;
export type ThinkChunkType = ClosedEnum<typeof ThinkChunkType>;

export type ThinkChunk = {
  thinking: Array<ReferenceChunk | TextChunk>;
  /**
   * Whether the thinking chunk is closed or not. Currently only used for prefixing.
   */
  closed?: boolean | undefined;
  type?: ThinkChunkType | undefined;
};

/** @internal */
export const Thinking$inboundSchema: z.ZodType<
  Thinking,
  z.ZodTypeDef,
  unknown
> = z.union([ReferenceChunk$inboundSchema, TextChunk$inboundSchema]);

/** @internal */
export type Thinking$Outbound = ReferenceChunk$Outbound | TextChunk$Outbound;

/** @internal */
export const Thinking$outboundSchema: z.ZodType<
  Thinking$Outbound,
  z.ZodTypeDef,
  Thinking
> = z.union([ReferenceChunk$outboundSchema, TextChunk$outboundSchema]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Thinking$ {
  /** @deprecated use `Thinking$inboundSchema` instead. */
  export const inboundSchema = Thinking$inboundSchema;
  /** @deprecated use `Thinking$outboundSchema` instead. */
  export const outboundSchema = Thinking$outboundSchema;
  /** @deprecated use `Thinking$Outbound` instead. */
  export type Outbound = Thinking$Outbound;
}

export function thinkingToJSON(thinking: Thinking): string {
  return JSON.stringify(Thinking$outboundSchema.parse(thinking));
}

export function thinkingFromJSON(
  jsonString: string,
): SafeParseResult<Thinking, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Thinking$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Thinking' from JSON`,
  );
}

/** @internal */
export const ThinkChunkType$inboundSchema: z.ZodNativeEnum<
  typeof ThinkChunkType
> = z.nativeEnum(ThinkChunkType);

/** @internal */
export const ThinkChunkType$outboundSchema: z.ZodNativeEnum<
  typeof ThinkChunkType
> = ThinkChunkType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ThinkChunkType$ {
  /** @deprecated use `ThinkChunkType$inboundSchema` instead. */
  export const inboundSchema = ThinkChunkType$inboundSchema;
  /** @deprecated use `ThinkChunkType$outboundSchema` instead. */
  export const outboundSchema = ThinkChunkType$outboundSchema;
}

/** @internal */
export const ThinkChunk$inboundSchema: z.ZodType<
  ThinkChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  thinking: z.array(
    z.union([ReferenceChunk$inboundSchema, TextChunk$inboundSchema]),
  ),
  closed: z.boolean().optional(),
  type: ThinkChunkType$inboundSchema.default("thinking"),
});

/** @internal */
export type ThinkChunk$Outbound = {
  thinking: Array<ReferenceChunk$Outbound | TextChunk$Outbound>;
  closed?: boolean | undefined;
  type: string;
};

/** @internal */
export const ThinkChunk$outboundSchema: z.ZodType<
  ThinkChunk$Outbound,
  z.ZodTypeDef,
  ThinkChunk
> = z.object({
  thinking: z.array(
    z.union([ReferenceChunk$outboundSchema, TextChunk$outboundSchema]),
  ),
  closed: z.boolean().optional(),
  type: ThinkChunkType$outboundSchema.default("thinking"),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ThinkChunk$ {
  /** @deprecated use `ThinkChunk$inboundSchema` instead. */
  export const inboundSchema = ThinkChunk$inboundSchema;
  /** @deprecated use `ThinkChunk$outboundSchema` instead. */
  export const outboundSchema = ThinkChunk$outboundSchema;
  /** @deprecated use `ThinkChunk$Outbound` instead. */
  export type Outbound = ThinkChunk$Outbound;
}

export function thinkChunkToJSON(thinkChunk: ThinkChunk): string {
  return JSON.stringify(ThinkChunk$outboundSchema.parse(thinkChunk));
}

export function thinkChunkFromJSON(
  jsonString: string,
): SafeParseResult<ThinkChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ThinkChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ThinkChunk' from JSON`,
  );
}
