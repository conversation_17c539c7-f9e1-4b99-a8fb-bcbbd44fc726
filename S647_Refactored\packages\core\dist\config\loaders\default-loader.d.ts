/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConfigLoader, ConfigLoaderOptions, LoadResult } from './types.js';
/**
 * Default configuration loader
 * Provides fallback configuration values
 */
export declare class DefaultConfigLoader implements ConfigLoader {
    readonly name = "default";
    readonly priority = 0;
    /**
     * Load default configuration
     */
    load(_options?: ConfigLoaderOptions): Promise<LoadResult>;
    /**
     * Default loader can always load
     */
    canLoad(_options?: ConfigLoaderOptions): Promise<boolean>;
}
//# sourceMappingURL=default-loader.d.ts.map