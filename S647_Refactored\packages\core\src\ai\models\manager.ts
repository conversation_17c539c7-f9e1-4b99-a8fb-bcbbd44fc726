/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { ModelInfo, AsyncResult } from '@inkbytefo/s647-shared';
import type { ModelManager as IModelManager } from '../interfaces/model.js';

/**
 * Model manager implementation
 */
export class ModelManager implements IModelManager {
  private models = new Map<string, ModelInfo>();

  public async getAvailableModels(): AsyncResult<ModelInfo[]> {
    try {
      return { success: true, data: Array.from(this.models.values()) };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error : new Error(String(error)) };
    }
  }

  public async getModelInfo(modelId: string): AsyncResult<ModelInfo> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        throw new Error(`Model not found: ${modelId}`);
      }
      return { success: true, data: model };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error : new Error(String(error)) };
    }
  }

  public async validateModel(modelId: string): AsyncResult<boolean> {
    try {
      const exists = this.models.has(modelId);
      return { success: true, data: exists };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error : new Error(String(error)) };
    }
  }

  public registerModel(model: ModelInfo): void {
    this.models.set(model.id, model);
  }

  public unregisterModel(modelId: string): void {
    this.models.delete(modelId);
  }
}
