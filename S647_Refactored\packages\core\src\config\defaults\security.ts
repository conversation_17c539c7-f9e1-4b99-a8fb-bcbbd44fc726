/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Default security configuration
 */
export const DEFAULT_SECURITY = {
  encryptApiKeys: true,
  encryptionAlgorithm: 'aes-256-gcm',
  keyDerivationIterations: 100000,
  sessionTimeout: 3600000, // 1 hour
  maxFailedAttempts: 5,
  lockoutDuration: 300000, // 5 minutes
  allowedOrigins: [],
  blockedIPs: [],
  rateLimiting: {
    enabled: true,
    maxRequests: 100,
    windowMs: 60000, // 1 minute
  },
  contentSecurityPolicy: {
    enabled: true,
    directives: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'https:'],
    },
  },
};

/**
 * Security configurations for different environments
 */
export const SECURITY_CONFIGS = {
  development: {
    ...DEFAULT_SECURITY,
    encryptApiKeys: false,
    sessionTimeout: 86400000, // 24 hours
    maxFailedAttempts: 10,
    rateLimiting: {
      enabled: false,
      maxRequests: 1000,
      windowMs: 60000,
    },
  },
  
  production: {
    ...DEFAULT_SECURITY,
    encryptApiKeys: true,
    sessionTimeout: 1800000, // 30 minutes
    maxFailedAttempts: 3,
    rateLimiting: {
      enabled: true,
      maxRequests: 50,
      windowMs: 60000,
    },
  },
  
  test: {
    ...DEFAULT_SECURITY,
    encryptApiKeys: false,
    sessionTimeout: 300000, // 5 minutes
    maxFailedAttempts: 100,
    rateLimiting: {
      enabled: false,
      maxRequests: 10000,
      windowMs: 60000,
    },
  },
};

/**
 * Get security configuration for environment
 */
export function getSecurityConfigForEnvironment(env: 'development' | 'production' | 'test'): typeof DEFAULT_SECURITY {
  return SECURITY_CONFIGS[env] || DEFAULT_SECURITY;
}

/**
 * Validate API key format
 */
export function isValidApiKeyFormat(apiKey: string): boolean {
  // Basic validation - should be at least 20 characters and contain alphanumeric characters
  return /^[a-zA-Z0-9_-]{20,}$/.test(apiKey);
}

/**
 * Sanitize sensitive data for logging
 */
export function sanitizeForLogging(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sensitiveKeys = ['apiKey', 'password', 'token', 'secret', 'key', 'auth'];
  const sanitized = { ...data };

  for (const key in sanitized) {
    if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof sanitized[key] === 'object') {
      sanitized[key] = sanitizeForLogging(sanitized[key]);
    }
  }

  return sanitized;
}
