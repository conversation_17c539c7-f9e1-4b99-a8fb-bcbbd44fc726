/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Provider, ProviderConfig, ProviderType, ProviderId, AsyncResult } from '@inkbytefo/s647-shared';
import type { IProviderFactory } from '../interfaces/provider.js';
/**
 * Provider factory implementation
 */
export declare class ProviderFactory implements IProviderFactory {
    private static instance;
    /**
     * Get the singleton instance
     */
    static getInstance(): ProviderFactory;
    /**
     * Create a provider instance
     */
    create(id: ProviderId, config: ProviderConfig): AsyncResult<Provider>;
    /**
     * Get supported provider types
     */
    getSupportedTypes(): ProviderType[];
    /**
     * Validate provider configuration
     */
    validateConfig(config: ProviderConfig): AsyncResult<void>;
    /**
     * Validate OpenAI configuration
     */
    private validateOpenAIConfig;
    /**
     * Validate Anthropic configuration
     */
    private validateAnthropicConfig;
    /**
     * Validate Google configuration
     */
    private validateGoogleConfig;
    /**
     * Validate Mistral configuration
     */
    private validateMistralConfig;
    /**
     * Validate OpenRouter configuration
     */
    private validateOpenRouterConfig;
    /**
     * Validate Custom configuration
     */
    private validateCustomConfig;
    /**
     * Validate Local configuration
     */
    private validateLocalConfig;
}
//# sourceMappingURL=factory.d.ts.map