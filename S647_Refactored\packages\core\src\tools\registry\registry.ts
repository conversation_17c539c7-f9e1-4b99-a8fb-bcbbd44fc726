/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { Tool, ToolId } from '@inkbytefo/s647-shared';

/**
 * Tool registry implementation
 */
export class ToolRegistry {
  private tools = new Map<ToolId, Tool>();

  public register(tool: Tool): void {
    this.tools.set(tool.id, tool);
  }

  public unregister(id: ToolId): void {
    this.tools.delete(id);
  }

  public get(id: ToolId): Tool | undefined {
    return this.tools.get(id);
  }

  public getAll(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getByCategory(category: string): Tool[] {
    return this.getAll().filter(tool => tool.category === category);
  }

  public search(query: string): Tool[] {
    const lowerQuery = query.toLowerCase();
    return this.getAll().filter(tool => 
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery)
    );
  }

  public clear(): void {
    this.tools.clear();
  }
}
