/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type FilesApiRoutesListFilesRequest = {
  page?: number | undefined;
  pageSize?: number | undefined;
  sampleType?: Array<components.SampleType> | null | undefined;
  source?: Array<components.Source> | null | undefined;
  search?: string | null | undefined;
  purpose?: components.FilePurpose | null | undefined;
};

/** @internal */
export const FilesApiRoutesListFilesRequest$inboundSchema: z.ZodType<
  FilesApiRoutesListFilesRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  page: z.number().int().default(0),
  page_size: z.number().int().default(100),
  sample_type: z.nullable(z.array(components.SampleType$inboundSchema))
    .optional(),
  source: z.nullable(z.array(components.Source$inboundSchema)).optional(),
  search: z.nullable(z.string()).optional(),
  purpose: z.nullable(components.FilePurpose$inboundSchema).optional(),
}).transform((v) => {
  return remap$(v, {
    "page_size": "pageSize",
    "sample_type": "sampleType",
  });
});

/** @internal */
export type FilesApiRoutesListFilesRequest$Outbound = {
  page: number;
  page_size: number;
  sample_type?: Array<string> | null | undefined;
  source?: Array<string> | null | undefined;
  search?: string | null | undefined;
  purpose?: string | null | undefined;
};

/** @internal */
export const FilesApiRoutesListFilesRequest$outboundSchema: z.ZodType<
  FilesApiRoutesListFilesRequest$Outbound,
  z.ZodTypeDef,
  FilesApiRoutesListFilesRequest
> = z.object({
  page: z.number().int().default(0),
  pageSize: z.number().int().default(100),
  sampleType: z.nullable(z.array(components.SampleType$outboundSchema))
    .optional(),
  source: z.nullable(z.array(components.Source$outboundSchema)).optional(),
  search: z.nullable(z.string()).optional(),
  purpose: z.nullable(components.FilePurpose$outboundSchema).optional(),
}).transform((v) => {
  return remap$(v, {
    pageSize: "page_size",
    sampleType: "sample_type",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilesApiRoutesListFilesRequest$ {
  /** @deprecated use `FilesApiRoutesListFilesRequest$inboundSchema` instead. */
  export const inboundSchema = FilesApiRoutesListFilesRequest$inboundSchema;
  /** @deprecated use `FilesApiRoutesListFilesRequest$outboundSchema` instead. */
  export const outboundSchema = FilesApiRoutesListFilesRequest$outboundSchema;
  /** @deprecated use `FilesApiRoutesListFilesRequest$Outbound` instead. */
  export type Outbound = FilesApiRoutesListFilesRequest$Outbound;
}

export function filesApiRoutesListFilesRequestToJSON(
  filesApiRoutesListFilesRequest: FilesApiRoutesListFilesRequest,
): string {
  return JSON.stringify(
    FilesApiRoutesListFilesRequest$outboundSchema.parse(
      filesApiRoutesListFilesRequest,
    ),
  );
}

export function filesApiRoutesListFilesRequestFromJSON(
  jsonString: string,
): SafeParseResult<FilesApiRoutesListFilesRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FilesApiRoutesListFilesRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FilesApiRoutesListFilesRequest' from JSON`,
  );
}
